{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Ranking\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useSelector } from 'react-redux';\nimport { useNavigate } from 'react-router-dom';\nimport { message } from 'antd';\nimport { TbTrophy, TbCrown, TbStar, TbFlame, TbBrain, TbHome, TbRefresh, TbMedal, TbRocket, TbDiamond, TbAward, TbShield, TbUsers } from 'react-icons/tb';\nimport { getAllReportsForRanking, getXPLeaderboard, getUserRanking } from '../../../apicalls/reports';\nimport { getAllUsers } from '../../../apicalls/users';\nimport ProfilePicture from '../../../components/common/ProfilePicture';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AmazingRankingPage = () => {\n  _s();\n  const userState = useSelector(state => state.users || {});\n  const reduxUser = userState.user || null;\n\n  // Try multiple sources for user data\n  const localStorageUser = (() => {\n    try {\n      const userData = localStorage.getItem('user');\n      return userData ? JSON.parse(userData) : null;\n    } catch {\n      return null;\n    }\n  })();\n  const tokenUser = (() => {\n    try {\n      const token = localStorage.getItem('token');\n      if (token) {\n        const payload = JSON.parse(atob(token.split('.')[1]));\n        return payload;\n      }\n      return null;\n    } catch {\n      return null;\n    }\n  })();\n\n  // Use the first available user data\n  const user = reduxUser || localStorageUser || tokenUser;\n\n  // State for full user data\n  const [fullUserData, setFullUserData] = useState(null);\n\n  // Debug: Log all user sources\n  console.log('🔍 User Data Sources:', {\n    redux: reduxUser,\n    localStorage: localStorageUser,\n    token: tokenUser,\n    final: user\n  });\n\n  // Debug: Log user data structure for migrated users (simplified)\n  if (user && !fullUserData) {\n    console.log('🔍 Loading user data for:', user.userId);\n  }\n  const navigate = useNavigate();\n  const [rankingData, setRankingData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [currentUserRank, setCurrentUserRank] = useState(null);\n  const [viewMode, setViewMode] = useState('global');\n  const [showStats, setShowStats] = useState(true);\n  const [animationPhase, setAnimationPhase] = useState(0);\n  const [motivationalQuote, setMotivationalQuote] = useState('');\n  const [currentUserLeague, setCurrentUserLeague] = useState(null);\n  const [leagueUsers, setLeagueUsers] = useState([]);\n  const [showLeagueView, setShowLeagueView] = useState(false);\n  const [selectedLeague, setSelectedLeague] = useState(null);\n  const [leagueGroups, setLeagueGroups] = useState({});\n  const [userHasBeenShown, setUserHasBeenShown] = useState(false);\n  const [autoScrollCompleted, setAutoScrollCompleted] = useState(false);\n\n  // Refs for league sections\n  const leagueRefs = useRef({});\n  const headerRef = useRef(null);\n  const currentUserRef = useRef(null);\n  const podiumUserRef = useRef(null);\n  const listUserRef = useRef(null);\n\n  // Motivational quotes for different performance levels\n  const motivationalQuotes = [\"🚀 Every expert was once a beginner. Keep climbing!\", \"⭐ Your potential is endless. Show them what you're made of!\", \"🔥 Champions are made in the moments when nobody's watching.\", \"💎 Pressure makes diamonds. You're becoming brilliant!\", \"🎯 Success is not final, failure is not fatal. Keep going!\", \"⚡ The only impossible journey is the one you never begin.\", \"🌟 Believe in yourself and all that you are capable of!\", \"🏆 Greatness is not about being better than others, it's about being better than yesterday.\", \"💪 Your only limit is your mind. Break through it!\", \"🎨 Paint your success with the colors of determination!\"];\n\n  // Enhanced League System with Duolingo-style progression\n  const leagueSystem = {\n    mythic: {\n      min: 50000,\n      color: 'from-purple-300 via-pink-300 via-red-300 to-orange-300',\n      bgColor: 'bg-gradient-to-br from-purple-900/50 via-pink-900/50 to-red-900/50',\n      textColor: '#FFD700',\n      nameColor: '#FF1493',\n      shadowColor: 'rgba(255, 20, 147, 0.9)',\n      glow: 'shadow-pink-500/90',\n      icon: TbCrown,\n      title: 'MYTHIC',\n      description: 'Legendary Master',\n      borderColor: '#FF1493',\n      effect: 'mythic-aura',\n      leagueIcon: '👑',\n      promotionXP: 0,\n      // Max league\n      relegationXP: 40000,\n      maxUsers: 10\n    },\n    legendary: {\n      min: 25000,\n      color: 'from-purple-400 via-indigo-400 via-blue-400 to-cyan-400',\n      bgColor: 'bg-gradient-to-br from-purple-900/40 via-indigo-900/40 to-blue-900/40',\n      textColor: '#8A2BE2',\n      nameColor: '#9370DB',\n      shadowColor: 'rgba(138, 43, 226, 0.9)',\n      glow: 'shadow-purple-500/80',\n      icon: TbDiamond,\n      title: 'LEGENDARY',\n      description: 'Elite Champion',\n      borderColor: '#8A2BE2',\n      effect: 'legendary-sparkle',\n      leagueIcon: '💎',\n      promotionXP: 50000,\n      relegationXP: 20000,\n      maxUsers: 25\n    },\n    diamond: {\n      min: 12000,\n      color: 'from-cyan-300 via-blue-300 via-indigo-300 to-purple-300',\n      bgColor: 'bg-gradient-to-br from-cyan-900/40 via-blue-900/40 to-indigo-900/40',\n      textColor: '#00CED1',\n      nameColor: '#40E0D0',\n      shadowColor: 'rgba(0, 206, 209, 0.9)',\n      glow: 'shadow-cyan-400/80',\n      icon: TbShield,\n      title: 'DIAMOND',\n      description: 'Expert Level',\n      borderColor: '#00CED1',\n      effect: 'diamond-shine',\n      leagueIcon: '🛡️',\n      promotionXP: 25000,\n      relegationXP: 8000,\n      maxUsers: 50\n    },\n    platinum: {\n      min: 6000,\n      color: 'from-slate-300 via-gray-300 via-zinc-300 to-stone-300',\n      bgColor: 'bg-gradient-to-br from-slate-800/40 via-gray-800/40 to-zinc-800/40',\n      textColor: '#C0C0C0',\n      nameColor: '#D3D3D3',\n      shadowColor: 'rgba(192, 192, 192, 0.9)',\n      glow: 'shadow-slate-400/80',\n      icon: TbAward,\n      title: 'PLATINUM',\n      description: 'Advanced',\n      borderColor: '#C0C0C0',\n      effect: 'platinum-gleam',\n      leagueIcon: '🏆',\n      promotionXP: 12000,\n      relegationXP: 4000,\n      maxUsers: 100\n    },\n    gold: {\n      min: 3000,\n      color: 'from-yellow-300 via-amber-300 via-orange-300 to-red-300',\n      bgColor: 'bg-gradient-to-br from-yellow-900/40 via-amber-900/40 to-orange-900/40',\n      textColor: '#FFD700',\n      nameColor: '#FFA500',\n      shadowColor: 'rgba(255, 215, 0, 0.9)',\n      glow: 'shadow-yellow-400/80',\n      icon: TbTrophy,\n      title: 'GOLD',\n      description: 'Skilled',\n      borderColor: '#FFD700',\n      effect: 'gold-glow',\n      leagueIcon: '🥇',\n      promotionXP: 6000,\n      relegationXP: 2000,\n      maxUsers: 200\n    },\n    silver: {\n      min: 1500,\n      color: 'from-gray-300 via-slate-300 via-zinc-300 to-gray-300',\n      bgColor: 'bg-gradient-to-br from-gray-800/40 via-slate-800/40 to-zinc-800/40',\n      textColor: '#C0C0C0',\n      nameColor: '#B8B8B8',\n      shadowColor: 'rgba(192, 192, 192, 0.9)',\n      glow: 'shadow-gray-400/80',\n      icon: TbMedal,\n      title: 'SILVER',\n      description: 'Improving',\n      borderColor: '#C0C0C0',\n      effect: 'silver-shimmer',\n      leagueIcon: '🥈',\n      promotionXP: 3000,\n      relegationXP: 800,\n      maxUsers: 300\n    },\n    bronze: {\n      min: 500,\n      color: 'from-orange-300 via-amber-300 via-yellow-300 to-orange-300',\n      bgColor: 'bg-gradient-to-br from-orange-900/40 via-amber-900/40 to-yellow-900/40',\n      textColor: '#CD7F32',\n      nameColor: '#D2691E',\n      shadowColor: 'rgba(205, 127, 50, 0.9)',\n      glow: 'shadow-orange-400/80',\n      icon: TbStar,\n      title: 'BRONZE',\n      description: 'Learning',\n      borderColor: '#CD7F32',\n      effect: 'bronze-warm',\n      leagueIcon: '🥉',\n      promotionXP: 1500,\n      relegationXP: 200,\n      maxUsers: 500\n    },\n    rookie: {\n      min: 0,\n      color: 'from-green-300 via-emerald-300 via-teal-300 to-cyan-300',\n      bgColor: 'bg-gradient-to-br from-green-900/40 via-emerald-900/40 to-teal-900/40',\n      textColor: '#32CD32',\n      nameColor: '#90EE90',\n      shadowColor: 'rgba(50, 205, 50, 0.9)',\n      glow: 'shadow-green-400/80',\n      icon: TbRocket,\n      title: 'ROOKIE',\n      description: 'Starting Out',\n      borderColor: '#32CD32',\n      effect: 'rookie-glow',\n      leagueIcon: '🚀',\n      promotionXP: 500,\n      relegationXP: 0,\n      // Can't be relegated from rookie\n      maxUsers: 1000\n    }\n  };\n\n  // Get user's league based on XP with enhanced progression\n  const getUserLeague = xp => {\n    for (const [league, config] of Object.entries(leagueSystem)) {\n      if (xp >= config.min) return {\n        league,\n        ...config\n      };\n    }\n    return {\n      league: 'rookie',\n      ...leagueSystem.rookie\n    };\n  };\n\n  // Group users by their leagues for better organization\n  const groupUsersByLeague = users => {\n    const leagues = {};\n    users.forEach(user => {\n      const userLeague = getUserLeague(user.totalXP);\n      if (!leagues[userLeague.league]) {\n        leagues[userLeague.league] = {\n          config: userLeague,\n          users: []\n        };\n      }\n      leagues[userLeague.league].users.push({\n        ...user,\n        tier: userLeague // Update to use league instead of tier\n      });\n    });\n\n    // Sort users within each league by XP\n    Object.keys(leagues).forEach(leagueKey => {\n      leagues[leagueKey].users.sort((a, b) => b.totalXP - a.totalXP);\n    });\n    return leagues;\n  };\n\n  // Get current user's league and friends in the same league\n  const getCurrentUserLeagueData = (allUsers, currentUser) => {\n    if (!currentUser) return null;\n    const userLeague = getUserLeague(currentUser.totalXP || 0);\n    const leagueUsers = allUsers.filter(user => {\n      const league = getUserLeague(user.totalXP);\n      return league.league === userLeague.league;\n    }).sort((a, b) => b.totalXP - a.totalXP);\n    return {\n      league: userLeague,\n      users: leagueUsers,\n      userRank: leagueUsers.findIndex(u => u._id === currentUser._id) + 1,\n      totalInLeague: leagueUsers.length\n    };\n  };\n\n  // Handle league selection with unique visual effect\n  const handleLeagueSelect = leagueKey => {\n    var _leagueGroups$leagueK;\n    console.log('🎯 League selected:', leagueKey);\n\n    // Set selected league with unique visual effect\n    setSelectedLeague(leagueKey);\n    setShowLeagueView(true);\n    setLeagueUsers(((_leagueGroups$leagueK = leagueGroups[leagueKey]) === null || _leagueGroups$leagueK === void 0 ? void 0 : _leagueGroups$leagueK.users) || []);\n\n    // Scroll to league section with smooth animation\n    setTimeout(() => {\n      const leagueElement = document.querySelector(`[data-league=\"${leagueKey}\"]`) || document.getElementById(`league-${leagueKey}`) || leagueRefs.current[leagueKey];\n      if (leagueElement) {\n        leagueElement.scrollIntoView({\n          behavior: 'smooth',\n          block: 'center',\n          inline: 'nearest'\n        });\n\n        // Add unique visual effect - pulse animation\n        leagueElement.style.transform = 'scale(1.02)';\n        leagueElement.style.transition = 'all 0.3s ease';\n        leagueElement.style.boxShadow = '0 0 30px rgba(59, 130, 246, 0.5)';\n        setTimeout(() => {\n          leagueElement.style.transform = 'scale(1)';\n          leagueElement.style.boxShadow = '';\n        }, 600);\n      }\n    }, 100);\n  };\n\n  // Get ordered league keys from best to worst\n  const getOrderedLeagues = () => {\n    const leagueOrder = ['mythic', 'legendary', 'diamond', 'platinum', 'gold', 'silver', 'bronze', 'rookie'];\n    return leagueOrder.filter(league => leagueGroups[league] && leagueGroups[league].users.length > 0);\n  };\n\n  // Fetch ranking data using enhanced XP system\n  const fetchRankingData = async (forceRefresh = false) => {\n    try {\n      setLoading(true);\n      console.log('🚀 Fetching enhanced XP ranking data...', forceRefresh ? '(Force Refresh)' : '');\n\n      // Try the new XP-based leaderboard first\n      try {\n        console.log('📊 Fetching XP leaderboard...');\n        const xpLeaderboardResponse = await getXPLeaderboard({\n          limit: 1000,\n          levelFilter: (user === null || user === void 0 ? void 0 : user.level) || 'all',\n          includeInactive: false,\n          // Add timestamp for cache busting when force refreshing\n          ...(forceRefresh && {\n            _t: Date.now()\n          })\n        });\n        console.log('✨ XP Leaderboard response:', xpLeaderboardResponse);\n        if (xpLeaderboardResponse && xpLeaderboardResponse.success && xpLeaderboardResponse.data) {\n          console.log('🎯 Using enhanced XP ranking data');\n\n          // Filter to only include users who have actually taken quizzes and earned XP\n          const filteredData = xpLeaderboardResponse.data.filter(userData => userData.totalXP && userData.totalXP > 0 || userData.totalQuizzesTaken && userData.totalQuizzesTaken > 0);\n\n          // Debug: Check first few users' profile data\n          console.log('🔍 First 3 users profile data:', filteredData.slice(0, 3).map(u => ({\n            _id: u._id,\n            name: u.name,\n            profileImage: u.profileImage,\n            profilePicture: u.profilePicture,\n            hasProfileData: !!(u.profileImage || u.profilePicture)\n          })));\n          const transformedData = filteredData.map((userData, index) => ({\n            _id: userData._id,\n            name: userData.name || 'Anonymous Champion',\n            email: userData.email || '',\n            class: userData.class || '',\n            level: userData.level || '',\n            profilePicture: userData.profileImage || userData.profilePicture || '',\n            profileImage: userData.profileImage || userData.profilePicture || '',\n            totalXP: userData.totalXP || 0,\n            totalQuizzesTaken: userData.totalQuizzesTaken || 0,\n            averageScore: userData.averageScore || 0,\n            currentStreak: userData.currentStreak || 0,\n            bestStreak: userData.bestStreak || 0,\n            subscriptionStatus: userData.subscriptionStatus || 'free',\n            rank: index + 1,\n            tier: getUserLeague(userData.totalXP || 0),\n            isRealUser: true,\n            rankingScore: userData.rankingScore || 0,\n            // Enhanced XP data\n            currentLevel: userData.currentLevel || 1,\n            xpToNextLevel: userData.xpToNextLevel || 100,\n            lifetimeXP: userData.lifetimeXP || 0,\n            seasonXP: userData.seasonXP || 0,\n            achievements: userData.achievements || [],\n            dataSource: 'enhanced_xp'\n          }));\n\n          // Debug: Check final transformed data for top 3 users\n          console.log('🏆 Top 3 transformed users:', transformedData.slice(0, 3).map(u => ({\n            _id: u._id,\n            name: u.name,\n            profileImage: u.profileImage,\n            profilePicture: u.profilePicture,\n            hasProfileData: !!(u.profileImage || u.profilePicture)\n          })));\n          setRankingData(transformedData);\n\n          // Find current user's rank\n          const userRankIndex = transformedData.findIndex(item => item._id === (user === null || user === void 0 ? void 0 : user._id));\n          setCurrentUserRank(userRankIndex >= 0 ? userRankIndex + 1 : null);\n\n          // Set up league data for current user\n          if (user) {\n            const userLeagueData = getCurrentUserLeagueData(transformedData, user);\n            setCurrentUserLeague(userLeagueData);\n            setLeagueUsers((userLeagueData === null || userLeagueData === void 0 ? void 0 : userLeagueData.users) || []);\n          }\n\n          // Group all users by their leagues\n          const grouped = groupUsersByLeague(transformedData);\n          setLeagueGroups(grouped);\n          setLoading(false);\n          return;\n        }\n      } catch (xpError) {\n        console.log('⚠️ XP leaderboard failed, trying fallback:', xpError);\n      }\n\n      // Fallback to legacy system if XP leaderboard fails\n      console.log('🔄 Falling back to legacy ranking system...');\n      let rankingResponse, usersResponse;\n      try {\n        console.log('📊 Fetching legacy ranking reports...');\n        rankingResponse = await getAllReportsForRanking();\n        console.log('👥 Fetching all users...');\n        usersResponse = await getAllUsers();\n      } catch (error) {\n        console.log('⚡ Error fetching legacy data:', error);\n        try {\n          usersResponse = await getAllUsers();\n        } catch (userError) {\n          console.log('❌ Failed to fetch users:', userError);\n        }\n      }\n      let transformedData = [];\n      if (usersResponse && usersResponse.success && usersResponse.data) {\n        console.log('🔄 Processing legacy user data...');\n\n        // Create a map of user reports for quick lookup\n        const userReportsMap = {};\n        if (rankingResponse && rankingResponse.success && rankingResponse.data) {\n          rankingResponse.data.forEach(item => {\n            var _item$user;\n            const userId = ((_item$user = item.user) === null || _item$user === void 0 ? void 0 : _item$user._id) || item.userId;\n            if (userId) {\n              userReportsMap[userId] = item.reports || [];\n            }\n          });\n        }\n        transformedData = usersResponse.data.filter(userData => userData && userData._id) // Filter out invalid users only (admins included for testing)\n        .map((userData, index) => {\n          // Get reports for this user\n          const userReports = userReportsMap[userData._id] || [];\n\n          // Use existing user data or calculate from reports\n          let totalQuizzes = userReports.length || userData.totalQuizzesTaken || 0;\n          let totalScore = userReports.reduce((sum, report) => sum + (report.score || 0), 0);\n          let averageScore = totalQuizzes > 0 ? Math.round(totalScore / totalQuizzes) : userData.averageScore || 0;\n\n          // For existing users with old data, make intelligent assumptions\n          if (!userReports.length && userData.totalPoints) {\n            // Assume higher points = more exams and better performance\n            const estimatedQuizzes = Math.max(1, Math.floor(userData.totalPoints / 100)); // Assume ~100 points per quiz\n            const estimatedAverage = Math.min(95, Math.max(60, 60 + userData.totalPoints / estimatedQuizzes / 10)); // Scale average based on points\n\n            totalQuizzes = estimatedQuizzes;\n            averageScore = Math.round(estimatedAverage);\n            totalScore = Math.round(averageScore * totalQuizzes);\n            console.log(`📊 Estimated stats for ${userData.name}: ${estimatedQuizzes} quizzes, ${estimatedAverage}% avg from ${userData.totalPoints} points`);\n          }\n\n          // Calculate XP based on performance (enhanced calculation)\n          let totalXP = userData.totalXP || 0;\n          if (!totalXP) {\n            // Calculate XP from available data\n            if (userData.totalPoints) {\n              // Use existing points as base XP with bonuses\n              totalXP = Math.floor(userData.totalPoints +\n              // Base points\n              totalQuizzes * 25 + (\n              // Participation bonus\n              averageScore > 80 ? totalQuizzes * 15 : 0) + (\n              // Excellence bonus\n              averageScore > 90 ? totalQuizzes * 10 : 0) // Mastery bonus\n              );\n            } else if (totalQuizzes > 0) {\n              // Calculate from quiz performance\n              totalXP = Math.floor(averageScore * totalQuizzes * 8 +\n              // Base XP from scores\n              totalQuizzes * 40 + (\n              // Participation bonus\n              averageScore > 80 ? totalQuizzes * 20 : 0) // Excellence bonus\n              );\n            }\n          }\n\n          // Calculate streaks (enhanced logic)\n          let currentStreak = userData.currentStreak || 0;\n          let bestStreak = userData.bestStreak || 0;\n          if (userReports.length > 0) {\n            // Calculate from actual reports\n            let tempStreak = 0;\n            userReports.forEach(report => {\n              if (report.score >= 60) {\n                // Passing score\n                tempStreak++;\n                bestStreak = Math.max(bestStreak, tempStreak);\n              } else {\n                tempStreak = 0;\n              }\n            });\n            currentStreak = tempStreak;\n          } else if (userData.totalPoints && !currentStreak) {\n            // Estimate streaks from points (higher points = likely better streaks)\n            const pointsPerQuiz = totalQuizzes > 0 ? userData.totalPoints / totalQuizzes : 0;\n            if (pointsPerQuiz > 80) {\n              currentStreak = Math.min(totalQuizzes, Math.floor(pointsPerQuiz / 20)); // Estimate current streak\n              bestStreak = Math.max(currentStreak, Math.floor(pointsPerQuiz / 15)); // Estimate best streak\n            }\n          }\n\n          return {\n            _id: userData._id,\n            name: userData.name || 'Anonymous Champion',\n            email: userData.email || '',\n            class: userData.class || '',\n            level: userData.level || '',\n            profilePicture: userData.profileImage || userData.profilePicture || '',\n            profileImage: userData.profileImage || userData.profilePicture || '',\n            totalXP: totalXP,\n            totalQuizzesTaken: totalQuizzes,\n            averageScore: averageScore,\n            currentStreak: currentStreak,\n            bestStreak: bestStreak,\n            subscriptionStatus: userData.subscriptionStatus || 'free',\n            rank: index + 1,\n            tier: getUserLeague(totalXP),\n            isRealUser: true,\n            // Additional tracking fields for future updates\n            originalPoints: userData.totalPoints || 0,\n            hasReports: userReports.length > 0,\n            dataSource: userReports.length > 0 ? 'reports' : userData.totalPoints ? 'legacy_points' : 'estimated'\n          };\n        });\n\n        // Sort by XP descending\n        transformedData.sort((a, b) => b.totalXP - a.totalXP);\n\n        // Update ranks after sorting\n        transformedData.forEach((user, index) => {\n          user.rank = index + 1;\n        });\n        setRankingData(transformedData);\n\n        // Find current user's rank with multiple matching strategies\n        let userRank = -1;\n        if (user) {\n          // Try exact ID match first\n          userRank = transformedData.findIndex(item => item._id === user._id);\n\n          // If not found, try string comparison (in case of type differences)\n          if (userRank === -1) {\n            userRank = transformedData.findIndex(item => String(item._id) === String(user._id));\n          }\n\n          // If still not found, try matching by name (as fallback)\n          if (userRank === -1 && user.name) {\n            userRank = transformedData.findIndex(item => item.name === user.name);\n          }\n        }\n        setCurrentUserRank(userRank >= 0 ? userRank + 1 : null);\n\n        // Set up league data for current user\n        if (user) {\n          const userLeagueData = getCurrentUserLeagueData(transformedData, user);\n          setCurrentUserLeague(userLeagueData);\n          setLeagueUsers((userLeagueData === null || userLeagueData === void 0 ? void 0 : userLeagueData.users) || []);\n        }\n\n        // Group all users by their leagues\n        const grouped = groupUsersByLeague(transformedData);\n        setLeagueGroups(grouped);\n\n        // Enhanced debug logging for user ranking (development only)\n        if (process.env.NODE_ENV === 'development') {\n          console.log('🔍 Enhanced User ranking debug:', {\n            currentUser: user === null || user === void 0 ? void 0 : user.name,\n            userId: user === null || user === void 0 ? void 0 : user._id,\n            userIdType: typeof (user === null || user === void 0 ? void 0 : user._id),\n            isAdmin: (user === null || user === void 0 ? void 0 : user.role) === 'admin' || (user === null || user === void 0 ? void 0 : user.isAdmin),\n            userXP: user === null || user === void 0 ? void 0 : user.totalXP,\n            userRankIndex: userRank,\n            userRankPosition: userRank >= 0 ? userRank + 1 : null,\n            totalRankedUsers: transformedData.length,\n            firstFewUserIds: transformedData.slice(0, 5).map(u => ({\n              id: u._id,\n              type: typeof u._id,\n              name: u.name\n            })),\n            exactMatch: transformedData.find(item => item._id === (user === null || user === void 0 ? void 0 : user._id)),\n            stringMatch: transformedData.find(item => String(item._id) === String(user === null || user === void 0 ? void 0 : user._id)),\n            nameMatch: transformedData.find(item => item.name === (user === null || user === void 0 ? void 0 : user.name))\n          });\n        }\n\n        // Log data sources for transparency\n        const dataSources = {\n          reports: transformedData.filter(u => u.dataSource === 'reports').length,\n          legacy_points: transformedData.filter(u => u.dataSource === 'legacy_points').length,\n          estimated: transformedData.filter(u => u.dataSource === 'estimated').length\n        };\n        console.log('🎉 Amazing ranking data loaded!', transformedData.length, 'real champions');\n        console.log('📊 Data sources:', dataSources);\n        console.log('🏆 Top 5 champions:', transformedData.slice(0, 5).map(u => ({\n          name: u.name,\n          xp: u.totalXP,\n          quizzes: u.totalQuizzesTaken,\n          avg: u.averageScore,\n          source: u.dataSource\n        })));\n      } else {\n        console.log('⚠️ No user data available');\n        setRankingData([]);\n        setCurrentUserRank(null);\n        message.warning('No ranking data available. Please check your connection.');\n      }\n    } catch (error) {\n      console.error('💥 Error fetching ranking data:', error);\n      message.error('Failed to load the leaderboard. But champions never give up!');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch full user data\n  const fetchFullUserData = async () => {\n    if (!(user !== null && user !== void 0 && user.userId)) {\n      console.log('❌ No userId available:', user);\n      return;\n    }\n    try {\n      console.log('🔍 Fetching full user data for userId:', user.userId);\n      const response = await getAllUsers();\n      console.log('📋 getAllUsers response:', response);\n      if (response.success) {\n        console.log('📊 Total users found:', response.data.length);\n        console.log('🔍 Looking for userId:', user.userId);\n        console.log('📝 First 5 user IDs:', response.data.slice(0, 5).map(u => ({\n          id: u._id,\n          name: u.name\n        })));\n        const userData = response.data.find(u => String(u._id) === String(user.userId));\n        if (userData) {\n          console.log('✅ Found full user data:', userData);\n          // Ensure profile picture properties are set\n          const userDataWithProfile = {\n            ...userData,\n            profilePicture: userData.profileImage || userData.profilePicture || '',\n            profileImage: userData.profileImage || userData.profilePicture || ''\n          };\n          setFullUserData(userDataWithProfile);\n        } else {\n          console.log('❌ User not found in users list');\n          console.log('🔍 Trying alternative search methods...');\n\n          // Try different ID formats\n          const userDataAlt = response.data.find(u => u._id === user.userId || u.id === user.userId || String(u._id).includes(user.userId) || String(user.userId).includes(u._id));\n          if (userDataAlt) {\n            console.log('✅ Found user with alternative method:', userDataAlt);\n            // Ensure profile picture properties are set\n            const userDataWithProfile = {\n              ...userDataAlt,\n              profilePicture: userDataAlt.profileImage || userDataAlt.profilePicture || '',\n              profileImage: userDataAlt.profileImage || userDataAlt.profilePicture || ''\n            };\n            setFullUserData(userDataWithProfile);\n          } else {\n            console.log('❌ User not found with any method');\n          }\n        }\n      } else {\n        console.log('❌ getAllUsers failed:', response);\n      }\n    } catch (error) {\n      console.error('❌ Error fetching user data:', error);\n    }\n  };\n\n  // Try to find user in ranking data as fallback\n  useEffect(() => {\n    if (!fullUserData && user !== null && user !== void 0 && user.userId && rankingData.length > 0) {\n      console.log('🔍 Trying to find user in ranking data...');\n      const userInRanking = rankingData.find(u => String(u._id) === String(user.userId));\n      if (userInRanking) {\n        console.log('✅ Found user in ranking data:', userInRanking);\n        // Ensure profile picture properties are set\n        const userDataWithProfile = {\n          ...userInRanking,\n          profilePicture: userInRanking.profileImage || userInRanking.profilePicture || '',\n          profileImage: userInRanking.profileImage || userInRanking.profilePicture || ''\n        };\n        setFullUserData(userDataWithProfile);\n      } else {\n        console.log('❌ User not found in ranking data either');\n      }\n    }\n  }, [rankingData, user, fullUserData]);\n\n  // Initialize component\n  useEffect(() => {\n    fetchRankingData();\n    fetchFullUserData(); // Fetch full user data\n\n    // Set random motivational quote\n    const randomQuote = motivationalQuotes[Math.floor(Math.random() * motivationalQuotes.length)];\n    setMotivationalQuote(randomQuote);\n\n    // Start animation sequence\n    const animationTimer = setInterval(() => {\n      setAnimationPhase(prev => (prev + 1) % 4);\n    }, 3000);\n\n    // Auto-refresh disabled to prevent interference with Find Me functionality\n    // const refreshTimer = setInterval(() => {\n    //   console.log('🔄 Auto-refreshing ranking data...');\n    //   fetchRankingData();\n    // }, 30000);\n\n    // Refresh when user comes back from quiz (window focus)\n    const handleWindowFocus = () => {\n      console.log('🎯 Window focused - refreshing ranking data...');\n      fetchRankingData(true); // Force refresh when returning from quiz\n    };\n\n    // Listen for real-time ranking updates from quiz completion\n    const handleRankingUpdate = event => {\n      console.log('🚀 Real-time ranking update triggered:', event.detail);\n\n      // Clear any cached data to ensure fresh fetch\n      localStorage.removeItem('rankingCache');\n      localStorage.removeItem('userRankingPosition');\n      localStorage.removeItem('leaderboardData');\n\n      // Immediate refresh after quiz completion with multiple attempts\n      const refreshWithRetry = async (attempts = 3) => {\n        for (let i = 0; i < attempts; i++) {\n          try {\n            var _event$detail;\n            console.log(`🔄 Refreshing ranking data (attempt ${i + 1}/${attempts})`);\n            await fetchRankingData(true); // Force refresh to bypass cache\n\n            // Verify the XP was updated by checking if user's XP matches the event data\n            if ((_event$detail = event.detail) !== null && _event$detail !== void 0 && _event$detail.newTotalXP && user) {\n              const updatedUser = rankingData.find(u => String(u._id) === String(user._id));\n              if (updatedUser && updatedUser.totalXP >= event.detail.newTotalXP) {\n                console.log('✅ XP update confirmed in ranking data');\n                break;\n              }\n            }\n\n            // Wait before retry\n            if (i < attempts - 1) {\n              await new Promise(resolve => setTimeout(resolve, 1500));\n            }\n          } catch (error) {\n            console.error(`❌ Ranking refresh attempt ${i + 1} failed:`, error);\n            if (i < attempts - 1) {\n              await new Promise(resolve => setTimeout(resolve, 1500));\n            }\n          }\n        }\n      };\n\n      // Start refresh with delay to ensure server processing\n      setTimeout(() => {\n        refreshWithRetry();\n      }, 1000);\n    };\n    window.addEventListener('focus', handleWindowFocus);\n    window.addEventListener('rankingUpdate', handleRankingUpdate);\n    return () => {\n      clearInterval(animationTimer);\n      // clearInterval(refreshTimer); // Commented out since refreshTimer is disabled\n      window.removeEventListener('focus', handleWindowFocus);\n      window.removeEventListener('rankingUpdate', handleRankingUpdate);\n    };\n  }, []);\n\n  // Auto-select user's current league when data loads\n  useEffect(() => {\n    if (user && leagueGroups && Object.keys(leagueGroups).length > 0 && !selectedLeague) {\n      // Find user's current league\n      for (const [leagueKey, leagueData] of Object.entries(leagueGroups)) {\n        const userInLeague = leagueData.users.find(u => String(u._id) === String(user._id));\n        if (userInLeague) {\n          console.log('🎯 Auto-selecting user league:', leagueKey);\n          setSelectedLeague(leagueKey);\n          setShowLeagueView(true);\n          setLeagueUsers(leagueData.users);\n          break;\n        }\n      }\n    }\n  }, [user, leagueGroups, selectedLeague]);\n\n  // Get top performers for special display (no filtering)\n  const topPerformers = rankingData.slice(0, 3);\n  const otherPerformers = rankingData.slice(3);\n\n  // Get user's current league information\n  const getUserLeagueInfo = () => {\n    if (!(user !== null && user !== void 0 && user._id)) return null;\n\n    // Check if user is in top 3 (podium)\n    const isInPodium = topPerformers.some(performer => String(performer._id) === String(user._id));\n    if (isInPodium) {\n      const podiumPosition = topPerformers.findIndex(performer => String(performer._id) === String(user._id)) + 1;\n      return {\n        type: 'podium',\n        position: podiumPosition,\n        league: 'Champion Podium',\n        leagueKey: 'podium'\n      };\n    }\n\n    // Find user's league\n    for (const [leagueKey, leagueData] of Object.entries(leagueGroups)) {\n      var _leagueData$users;\n      const userInLeague = (_leagueData$users = leagueData.users) === null || _leagueData$users === void 0 ? void 0 : _leagueData$users.find(u => String(u._id) === String(user._id));\n      if (userInLeague) {\n        const position = leagueData.users.findIndex(u => String(u._id) === String(user._id)) + 1;\n        return {\n          type: 'league',\n          position: position,\n          league: leagueData.title,\n          leagueKey: leagueKey,\n          totalUsers: leagueData.users.length\n        };\n      }\n    }\n    return null;\n  };\n  const userLeagueInfo = getUserLeagueInfo();\n\n  // Helper function to check if a user is the current user\n  const isCurrentUser = userId => {\n    return user && String(userId) === String(user._id);\n  };\n\n  // Helper function to check if user should be highlighted (only before they've been shown)\n  const shouldHighlightUser = userId => {\n    return isCurrentUser(userId) && !userHasBeenShown;\n  };\n\n  // Allow users to click anywhere to disable highlighting\n  const handlePageClick = () => {\n    if (!userHasBeenShown) {\n      setUserHasBeenShown(true);\n      console.log('👆 User clicked - highlighting disabled');\n    }\n  };\n\n  // Reset highlighting when user or league changes\n  useEffect(() => {\n    setUserHasBeenShown(false);\n    setAutoScrollCompleted(false); // Reset auto-scroll state\n  }, [user === null || user === void 0 ? void 0 : user._id, selectedLeague]);\n\n  // Auto-scroll to user position ONLY on first visit\n  useEffect(() => {\n    console.log('🔄 Auto-scroll check:', {\n      userId: user === null || user === void 0 ? void 0 : user._id,\n      autoScrollCompleted,\n      rankingDataLength: rankingData.length\n    });\n\n    // Only scroll if user exists, hasn't been scrolled yet, and we have data\n    if (!(user !== null && user !== void 0 && user._id) || autoScrollCompleted || rankingData.length === 0) {\n      console.log('❌ Auto-scroll skipped:', {\n        hasUser: !!(user !== null && user !== void 0 && user._id),\n        completed: autoScrollCompleted,\n        hasData: rankingData.length > 0\n      });\n      return;\n    }\n    const scrollToUser = () => {\n      console.log('🎯 Starting auto-scroll for user:', user._id);\n\n      // First, try to find user in any ranking data\n      const userInRanking = rankingData.find(u => String(u._id) === String(user._id));\n      if (!userInRanking) {\n        console.log('❌ User not found in ranking data');\n        setAutoScrollCompleted(true); // Mark as completed even if not found\n        return;\n      }\n      console.log('✅ User found in ranking at position:', userInRanking.rank);\n\n      // Check if user is in top 3 (podium)\n      const isInPodium = userInRanking.rank <= 3;\n      console.log('🏆 Is user in podium?', isInPodium);\n      if (isInPodium) {\n        // Scroll to podium section\n        console.log('📍 Scrolling to podium section...');\n        const podiumSection = document.querySelector('[data-section=\"podium\"]');\n        console.log('🎪 Podium section found:', !!podiumSection);\n        if (podiumSection) {\n          setTimeout(() => {\n            podiumSection.scrollIntoView({\n              behavior: 'smooth',\n              block: 'center',\n              inline: 'nearest'\n            });\n            console.log('✅ Scrolled to podium');\n            // Mark as completed after scroll\n            setTimeout(() => {\n              setUserHasBeenShown(true);\n              setAutoScrollCompleted(true);\n              console.log('✅ Auto-scroll completed');\n            }, 1000);\n          }, 500);\n        } else {\n          setAutoScrollCompleted(true);\n        }\n      } else {\n        // Look for user element in the ranking list\n        console.log('📍 Looking for user element with ID:', user._id);\n        const userElement = document.querySelector(`[data-user-id=\"${user._id}\"]`);\n        console.log('🎯 User element found:', !!userElement);\n        if (userElement) {\n          setTimeout(() => {\n            userElement.scrollIntoView({\n              behavior: 'smooth',\n              block: 'center',\n              inline: 'nearest'\n            });\n            console.log('✅ Scrolled to user position');\n            // Mark as completed after scroll\n            setTimeout(() => {\n              setUserHasBeenShown(true);\n              setAutoScrollCompleted(true);\n              console.log('✅ Auto-scroll completed');\n            }, 1000);\n          }, 500);\n        } else {\n          console.log('❌ User element not found in DOM');\n          setAutoScrollCompleted(true);\n        }\n      }\n    };\n\n    // Delay to ensure DOM is ready, but not too long\n    const timer = setTimeout(scrollToUser, 2000);\n    return () => clearTimeout(timer);\n  }, [user === null || user === void 0 ? void 0 : user._id, rankingData, autoScrollCompleted]);\n\n  // Get subscription status badge - simplified to only ACTIVATED and EXPIRED\n  const getSubscriptionBadge = (subscriptionStatus, subscriptionEndDate, subscriptionPlan, activePlanTitle, userIndex = 0) => {\n    const now = new Date();\n    const endDate = subscriptionEndDate ? new Date(subscriptionEndDate) : null;\n    console.log('Subscription Debug:', {\n      subscriptionStatus,\n      subscriptionEndDate,\n      subscriptionPlan,\n      activePlanTitle,\n      endDate,\n      now,\n      isActive: endDate && endDate > now,\n      userIndex\n    });\n\n    // Check if user has an active subscription\n    if (subscriptionStatus === 'active' || subscriptionStatus === 'premium') {\n      // Check if subscription is still valid (not expired)\n      if (!endDate || endDate > now) {\n        // User has active plan - show ACTIVATED\n        return {\n          text: 'ACTIVATED',\n          color: '#10B981',\n          // Green\n          bgColor: 'rgba(16, 185, 129, 0.2)',\n          borderColor: '#10B981'\n        };\n      } else {\n        // Subscription status is active but end date has passed - show EXPIRED\n        return {\n          text: 'EXPIRED',\n          color: '#EF4444',\n          // Red\n          bgColor: 'rgba(239, 68, 68, 0.2)',\n          borderColor: '#EF4444'\n        };\n      }\n    } else {\n      // No active subscription - show EXPIRED\n      return {\n        text: 'EXPIRED',\n        color: '#EF4444',\n        // Red\n        bgColor: 'rgba(239, 68, 68, 0.2)',\n        borderColor: '#EF4444'\n      };\n    }\n  };\n\n  // Skeleton loading component\n  const RankingSkeleton = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-12 bg-white/10 rounded-lg w-96 mx-auto mb-4 animate-pulse\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1054,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-6 bg-white/5 rounded w-64 mx-auto animate-pulse\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1055,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1053,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-center items-end mb-16 space-x-8\",\n        children: [2, 1, 3].map(position => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `text-center ${position === 1 ? 'order-2' : position === 2 ? 'order-1' : 'order-3'}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `w-20 h-20 bg-gradient-to-br from-yellow-400/20 to-yellow-600/20 rounded-full mx-auto mb-4 animate-pulse`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1062,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-4 bg-white/10 rounded w-16 mx-auto mb-2 animate-pulse\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1063,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-3 bg-white/5 rounded w-12 mx-auto animate-pulse\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1064,\n            columnNumber: 15\n          }, this)]\n        }, position, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1061,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1059,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4 max-w-4xl mx-auto\",\n        children: [...Array(8)].map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white/5 rounded-xl p-4 animate-pulse\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-white/10 rounded-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1074,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"h-4 bg-white/10 rounded w-32 mb-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1076,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"h-3 bg-white/5 rounded w-24\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1077,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1075,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-6 bg-white/10 rounded w-16\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1079,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1073,\n            columnNumber: 15\n          }, this)\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1072,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1070,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1051,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 1050,\n    columnNumber: 5\n  }, this);\n\n  // Show skeleton only on initial load\n  if (loading && rankingData.length === 0) {\n    return /*#__PURE__*/_jsxDEV(RankingSkeleton, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1090,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n        /* Dark background for better color visibility */\n        body {\n          background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%) !important;\n          min-height: 100vh;\n          color: #ffffff !important;\n        }\n\n        .ranking-page-container {\n          background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);\n          min-height: 100vh;\n          color: #ffffff;\n        }\n\n        /* Fix black text visibility - Enhanced */\n        .ranking-page-container * {\n          color: inherit;\n        }\n\n        .ranking-page-container .text-black,\n        .ranking-page-container .text-gray-900,\n        .ranking-page-container h1,\n        .ranking-page-container h2,\n        .ranking-page-container h3,\n        .ranking-page-container h4,\n        .ranking-page-container h5,\n        .ranking-page-container h6,\n        .ranking-page-container p,\n        .ranking-page-container span,\n        .ranking-page-container div {\n          color: #ffffff !important;\n        }\n\n        .ranking-page-container [style*=\"color: #000000\"],\n        .ranking-page-container [style*=\"color: black\"],\n        .ranking-page-container [style*=\"color:#000000\"],\n        .ranking-page-container [style*=\"color:black\"],\n        .ranking-page-container [style*=\"color: #1f2937\"],\n        .ranking-page-container [style*=\"color:#1f2937\"] {\n          color: #ffffff !important;\n        }\n\n        /* Force white text for names and content */\n        .ranking-page-container .font-bold,\n        .ranking-page-container .font-black,\n        .ranking-page-container .font-semibold,\n        .ranking-page-container .font-medium {\n          color: #ffffff !important;\n        }\n\n\n        /* Enhanced hover effects for ranking cards */\n        .ranking-card {\n          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n        }\n\n        .ranking-card:hover {\n          transform: translateY(-2px) scale(1.01);\n        }\n\n        /* Smooth animations for league badges */\n        .league-badge {\n          transition: all 0.2s ease-in-out;\n        }\n\n        .league-badge:hover {\n          transform: scale(1.05);\n        }\n\n        /* Gradient text animations */\n        @keyframes gradientShift {\n          0%, 100% { background-position: 0% 50%; }\n          50% { background-position: 100% 50%; }\n        }\n\n        .animated-gradient {\n          background-size: 200% 200%;\n          animation: gradientShift 3s ease infinite;\n        }\n\n        /* League-specific animations */\n        .mythic-aura {\n          animation: mythicPulse 2s ease-in-out infinite alternate;\n        }\n\n        .legendary-sparkle {\n          animation: legendarySparkle 3s ease-in-out infinite;\n        }\n\n        .diamond-shine {\n          animation: diamondShine 2.5s ease-in-out infinite;\n        }\n\n        .platinum-gleam {\n          animation: platinumGleam 3s ease-in-out infinite;\n        }\n\n        .gold-glow {\n          animation: goldGlow 2s ease-in-out infinite alternate;\n        }\n\n        .silver-shimmer {\n          animation: silverShimmer 2.5s ease-in-out infinite;\n        }\n\n        .bronze-warm {\n          animation: bronzeWarm 3s ease-in-out infinite;\n        }\n\n        .rookie-glow {\n          animation: rookieGlow 2s ease-in-out infinite alternate;\n        }\n\n        @keyframes mythicPulse {\n          0% { box-shadow: 0 0 20px rgba(255, 20, 147, 0.5); }\n          100% { box-shadow: 0 0 40px rgba(255, 20, 147, 0.8), 0 0 60px rgba(138, 43, 226, 0.6); }\n        }\n\n        @keyframes legendarySparkle {\n          0%, 100% { filter: brightness(1) hue-rotate(0deg); }\n          50% { filter: brightness(1.2) hue-rotate(10deg); }\n        }\n\n        @keyframes diamondShine {\n          0%, 100% { filter: brightness(1) saturate(1); }\n          50% { filter: brightness(1.3) saturate(1.2); }\n        }\n\n        @keyframes platinumGleam {\n          0%, 100% { filter: brightness(1); }\n          50% { filter: brightness(1.1) contrast(1.1); }\n        }\n\n        @keyframes goldGlow {\n          0% { filter: brightness(1) drop-shadow(0 0 10px #FFD700); }\n          100% { filter: brightness(1.2) drop-shadow(0 0 20px #FFD700); }\n        }\n\n        @keyframes silverShimmer {\n          0%, 100% { filter: brightness(1); }\n          50% { filter: brightness(1.15) contrast(1.05); }\n        }\n\n        @keyframes bronzeWarm {\n          0%, 100% { filter: brightness(1) hue-rotate(0deg); }\n          50% { filter: brightness(1.1) hue-rotate(5deg); }\n        }\n\n        @keyframes rookieGlow {\n          0% { filter: brightness(1) drop-shadow(0 0 5px #32CD32); }\n          100% { filter: brightness(1.15) drop-shadow(0 0 15px #32CD32); }\n        }\n\n        /* Horizontal podium animations */\n        .podium-animation {\n          animation: podiumFloat 4s ease-in-out infinite;\n        }\n\n        @keyframes podiumFloat {\n          0%, 100% { transform: translateY(0px); }\n          50% { transform: translateY(-5px); }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1095,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"ranking-page-container ranking-page min-h-screen bg-gradient-to-br from-slate-900 via-gray-900 to-black relative overflow-hidden\",\n      onClick: handlePageClick,\n      children: [!userHasBeenShown && user && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        exit: {\n          opacity: 0,\n          y: -20\n        },\n        className: \"fixed top-4 left-1/2 transform -translate-x-1/2 z-50 bg-yellow-500/90 backdrop-blur-sm text-black px-4 py-2 rounded-lg shadow-lg text-sm font-medium\",\n        children: \"\\uD83C\\uDFAF Finding your position... Click anywhere to stop highlighting\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1262,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute -top-40 -right-40 w-80 h-80 bg-purple-600 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-blob\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1274,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-blue-600 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-blob animation-delay-2000\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1275,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-40 left-40 w-80 h-80 bg-indigo-600 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-blob animation-delay-4000\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1276,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-1/2 right-1/3 w-60 h-60 bg-cyan-600 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-blob animation-delay-6000\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1277,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1273,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n        children: [...Array(20)].map((_, i) => /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"absolute w-2 h-2 bg-white rounded-full opacity-20\",\n          animate: {\n            y: [0, -100, 0],\n            x: [0, Math.random() * 100 - 50, 0],\n            opacity: [0.2, 0.8, 0.2]\n          },\n          transition: {\n            duration: 3 + Math.random() * 2,\n            repeat: Infinity,\n            delay: Math.random() * 2\n          },\n          style: {\n            left: `${Math.random() * 100}%`,\n            top: `${Math.random() * 100}%`\n          }\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1283,\n          columnNumber: 11\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1281,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative z-10\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: -20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          className: \"px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6 md:py-8 lg:py-12\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white/5 backdrop-blur-lg rounded-xl sm:rounded-2xl md:rounded-3xl p-3 sm:p-4 md:p-6 lg:p-8 border border-white/10\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col sm:flex-row gap-2 sm:gap-3 md:gap-4 lg:gap-6 items-center justify-center\",\n                children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: () => navigate('/user/hub'),\n                  className: \"flex items-center gap-2 md:gap-3 px-4 md:px-6 py-3 md:py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-bold shadow-lg hover:shadow-xl transition-all duration-300 w-full sm:w-auto\",\n                  style: {\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TbHome, {\n                    className: \"w-5 h-5 md:w-6 md:h-6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1326,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Hub\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1327,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1317,\n                  columnNumber: 17\n                }, this), userLeagueInfo && /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 20\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  className: \"flex items-center gap-2 md:gap-3 px-4 md:px-6 py-3 md:py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl font-bold shadow-lg\",\n                  style: {\n                    background: userLeagueInfo.type === 'podium' ? 'linear-gradient(135deg, #FFD700, #FFA500)' : 'linear-gradient(135deg, #3B82F6, #8B5CF6)',\n                    color: userLeagueInfo.type === 'podium' ? '#1F2937' : '#FFFFFF',\n                    boxShadow: '0 4px 15px rgba(59, 130, 246, 0.3)',\n                    fontSize: window.innerWidth < 768 ? '0.9rem' : '1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n                    className: \"w-5 h-5 md:w-6 md:h-6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1345,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: userLeagueInfo.type === 'podium' ? `🏆 Podium #${userLeagueInfo.position}` : `${userLeagueInfo.league} #${userLeagueInfo.position}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1346,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1332,\n                  columnNumber: 19\n                }, this), fullUserData && /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 20\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  className: \"bg-gradient-to-br from-blue-600/20 via-purple-600/20 to-indigo-600/20 backdrop-blur-lg rounded-2xl p-4 border border-blue-400/30 shadow-2xl max-w-sm\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-shrink-0\",\n                      children: /*#__PURE__*/_jsxDEV(ProfilePicture, {\n                        user: fullUserData,\n                        size: \"xl\",\n                        showOnlineStatus: true,\n                        style: {\n                          border: '3px solid #facc15',\n                          boxShadow: '0 10px 25px rgba(0,0,0,0.15)'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1364,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1363,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-grow\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-lg font-bold text-white mb-2 truncate\",\n                        children: fullUserData.name || fullUserData.username || 'User'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1377,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"grid grid-cols-2 gap-2 text-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"bg-green-500/20 rounded-lg p-2 text-center\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-green-300 text-xs\",\n                            children: \"Total XP\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1384,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-white font-bold\",\n                            children: (() => {\n                              // Try multiple XP field names for migrated users\n                              const xp = fullUserData.totalXP || fullUserData.xp || fullUserData.points || fullUserData.totalPoints || 0;\n                              return xp.toLocaleString();\n                            })()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1385,\n                            columnNumber: 29\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1383,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"bg-purple-500/20 rounded-lg p-2 text-center\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-purple-300 text-xs\",\n                            children: \"Rank\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1395,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-white font-bold\",\n                            children: (() => {\n                              // Try to find user in ranking data\n                              const userInRanking = rankingData.find(u => String(u._id) === String(fullUserData._id));\n                              return userInRanking ? `#${userInRanking.rank}` : currentUserRank ? `#${currentUserRank}` : 'N/A';\n                            })()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1396,\n                            columnNumber: 29\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1394,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"bg-blue-500/20 rounded-lg p-2 text-center\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-blue-300 text-xs\",\n                            children: \"League\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1406,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-white font-bold text-xs\",\n                            children: (() => {\n                              // Find user's league with icon - try multiple XP sources\n                              const userXP = fullUserData.totalXP || fullUserData.xp || fullUserData.points || fullUserData.totalPoints || 0;\n                              for (const [leagueKey, leagueData] of Object.entries(leagueGroups)) {\n                                var _leagueData$users2;\n                                const userInLeague = (_leagueData$users2 = leagueData.users) === null || _leagueData$users2 === void 0 ? void 0 : _leagueData$users2.find(u => String(u._id) === String(fullUserData._id));\n                                if (userInLeague) {\n                                  const leagueInfo = getUserLeague(userXP);\n                                  return `${leagueInfo.leagueIcon} ${leagueKey.toUpperCase()}`;\n                                }\n                              }\n                              // Fallback: calculate league from XP even if not in league data\n                              if (userXP > 0) {\n                                const leagueInfo = getUserLeague(userXP);\n                                return `${leagueInfo.leagueIcon} ${leagueInfo.league.toUpperCase()}`;\n                              }\n                              return '🔰 Unranked';\n                            })()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1407,\n                            columnNumber: 29\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1405,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"bg-orange-500/20 rounded-lg p-2 text-center\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-orange-300 text-xs\",\n                            children: \"Quizzes\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1429,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-white font-bold\",\n                            children: (() => {\n                              // Try multiple quiz count field names\n                              return fullUserData.quizzesCompleted || fullUserData.totalQuizzesTaken || fullUserData.quizzesTaken || fullUserData.totalQuizzes || 0;\n                            })()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1430,\n                            columnNumber: 29\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1428,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1382,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"grid grid-cols-3 gap-2 mt-2 text-xs\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"bg-yellow-500/20 rounded-lg p-1.5 text-center\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-yellow-300 text-xs\",\n                            children: \"Level\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1442,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-white font-bold\",\n                            children: fullUserData.currentLevel || fullUserData.level || 1\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1443,\n                            columnNumber: 29\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1441,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"bg-red-500/20 rounded-lg p-1.5 text-center\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-red-300 text-xs\",\n                            children: \"Streak\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1449,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-white font-bold\",\n                            children: fullUserData.currentStreak || fullUserData.streak || 0\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1450,\n                            columnNumber: 29\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1448,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"bg-cyan-500/20 rounded-lg p-1.5 text-center\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-cyan-300 text-xs\",\n                            children: \"Avg Score\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1456,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-white font-bold\",\n                            children: [(() => {\n                              const avgScore = fullUserData.averageScore || fullUserData.avgScore || 0;\n                              return Math.round(avgScore);\n                            })(), \"%\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1457,\n                            columnNumber: 29\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1455,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1440,\n                        columnNumber: 25\n                      }, this), (() => {\n                        // Find user's position in their league\n                        for (const [leagueKey, leagueData] of Object.entries(leagueGroups)) {\n                          var _leagueData$users3;\n                          const userIndex = (_leagueData$users3 = leagueData.users) === null || _leagueData$users3 === void 0 ? void 0 : _leagueData$users3.findIndex(u => String(u._id) === String(fullUserData._id));\n                          if (userIndex !== -1 && userIndex !== undefined) {\n                            return /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"mt-2 text-center\",\n                              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"bg-gradient-to-r from-yellow-400/20 to-orange-400/20 rounded-lg p-1.5\",\n                                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"text-yellow-300 text-xs\",\n                                  children: \"League Position\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1475,\n                                  columnNumber: 37\n                                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"text-white font-bold text-sm\",\n                                  children: [\"#\", userIndex + 1, \" of \", leagueData.users.length]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1476,\n                                  columnNumber: 37\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1474,\n                                columnNumber: 35\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1473,\n                              columnNumber: 33\n                            }, this);\n                          }\n                        }\n                        return null;\n                      })()]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1376,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1361,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1356,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex flex-col items-center gap-4 bg-white/5 backdrop-blur-lg rounded-2xl p-6 border border-white/10 max-w-4xl mx-auto\",\n                  children: [/*#__PURE__*/_jsxDEV(motion.h3, {\n                    className: \"text-2xl md:text-3xl font-black mb-2\",\n                    style: {\n                      background: 'linear-gradient(45deg, #FFD700, #FFA500, #FF6B35)',\n                      WebkitBackgroundClip: 'text',\n                      WebkitTextFillColor: 'transparent',\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      filter: 'drop-shadow(0 0 10px #FFD700)'\n                    },\n                    animate: {\n                      scale: [1, 1.02, 1]\n                    },\n                    transition: {\n                      duration: 3,\n                      repeat: Infinity\n                    },\n                    children: \"\\uD83C\\uDFC6 LEAGUES \\uD83C\\uDFC6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1504,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-wrap items-center justify-center gap-3 md:gap-4\",\n                    children: getOrderedLeagues().map(leagueKey => {\n                      var _leagueGroups$leagueK2;\n                      const league = leagueSystem[leagueKey];\n                      const isSelected = selectedLeague === leagueKey;\n                      const userCount = ((_leagueGroups$leagueK2 = leagueGroups[leagueKey]) === null || _leagueGroups$leagueK2 === void 0 ? void 0 : _leagueGroups$leagueK2.users.length) || 0;\n                      return /*#__PURE__*/_jsxDEV(motion.div, {\n                        className: \"flex flex-col items-center gap-2\",\n                        whileHover: {\n                          scale: 1.05\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                          whileHover: {\n                            scale: 1.1,\n                            y: -3\n                          },\n                          whileTap: {\n                            scale: 0.95\n                          },\n                          onClick: () => handleLeagueSelect(leagueKey),\n                          className: `relative flex items-center justify-center w-16 h-16 md:w-20 md:h-20 rounded-2xl transition-all duration-300 ${isSelected ? 'ring-4 ring-yellow-400 ring-opacity-100 shadow-2xl' : 'hover:ring-2 hover:ring-white/30'}`,\n                          style: {\n                            background: isSelected ? `linear-gradient(135deg, ${league.borderColor}80, ${league.textColor}50, ${league.borderColor}80)` : `linear-gradient(135deg, ${league.borderColor}60, ${league.textColor}30)`,\n                            border: `3px solid ${isSelected ? '#FFD700' : league.borderColor + '80'}`,\n                            boxShadow: isSelected ? `0 0 30px ${league.shadowColor}80, 0 0 60px #FFD70080, 0 6px 30px ${league.shadowColor}80` : `0 4px 15px ${league.shadowColor}40`,\n                            transform: isSelected ? 'scale(1.1)' : 'scale(1)',\n                            filter: isSelected ? 'brightness(1.3) saturate(1.2)' : 'brightness(1)'\n                          },\n                          animate: isSelected ? {\n                            boxShadow: [`0 0 30px ${league.shadowColor}80, 0 0 60px #FFD70080`, `0 0 40px ${league.shadowColor}100, 0 0 80px #FFD700A0`, `0 0 30px ${league.shadowColor}80, 0 0 60px #FFD70080`],\n                            scale: [1.1, 1.15, 1.1]\n                          } : {},\n                          transition: {\n                            duration: 2,\n                            repeat: isSelected ? Infinity : 0,\n                            ease: \"easeInOut\"\n                          },\n                          title: `Click to view ${league.title} League (${userCount} users)`,\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-3xl md:text-4xl\",\n                            children: league.leagueIcon\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1567,\n                            columnNumber: 29\n                          }, this), isSelected && /*#__PURE__*/_jsxDEV(motion.div, {\n                            initial: {\n                              scale: 0,\n                              rotate: -360,\n                              opacity: 0\n                            },\n                            animate: {\n                              scale: [1, 1.3, 1],\n                              rotate: [0, 360, 720],\n                              opacity: 1,\n                              boxShadow: ['0 0 15px rgba(255, 215, 0, 1), 0 0 30px rgba(255, 215, 0, 0.8)', '0 0 25px rgba(255, 215, 0, 1), 0 0 50px rgba(255, 215, 0, 1)', '0 0 15px rgba(255, 215, 0, 1), 0 0 30px rgba(255, 215, 0, 0.8)']\n                            },\n                            transition: {\n                              scale: {\n                                duration: 2,\n                                repeat: Infinity,\n                                ease: \"easeInOut\"\n                              },\n                              rotate: {\n                                duration: 4,\n                                repeat: Infinity,\n                                ease: \"linear\"\n                              },\n                              boxShadow: {\n                                duration: 1.5,\n                                repeat: Infinity,\n                                ease: \"easeInOut\"\n                              },\n                              opacity: {\n                                duration: 0.3\n                              }\n                            },\n                            className: \"absolute -top-3 -right-3 w-8 h-8 bg-gradient-to-r from-yellow-400 via-orange-400 to-yellow-500 rounded-full flex items-center justify-center border-3 border-white shadow-lg\",\n                            style: {\n                              background: 'linear-gradient(45deg, #FFD700, #FFA500, #FFD700)',\n                              border: '3px solid white',\n                              zIndex: 10\n                            },\n                            children: /*#__PURE__*/_jsxDEV(motion.span, {\n                              className: \"text-sm font-black text-gray-900\",\n                              animate: {\n                                scale: [1, 1.2, 1],\n                                rotate: [0, -10, 10, 0]\n                              },\n                              transition: {\n                                duration: 1,\n                                repeat: Infinity,\n                                ease: \"easeInOut\"\n                              },\n                              children: \"\\u2713\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1594,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1569,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"absolute -bottom-2 -right-2 w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold border-2 border-white\",\n                            style: {\n                              background: league.borderColor,\n                              color: '#FFFFFF',\n                              fontSize: '11px'\n                            },\n                            children: userCount\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1610,\n                            columnNumber: 29\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1532,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                          className: \"text-center\",\n                          whileHover: {\n                            scale: 1.05\n                          },\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-xs md:text-sm font-bold px-2 py-1 rounded-lg\",\n                            style: {\n                              color: league.nameColor,\n                              textShadow: `1px 1px 2px ${league.shadowColor}`,\n                              background: `${league.borderColor}20`,\n                              border: `1px solid ${league.borderColor}40`\n                            },\n                            children: league.title\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1627,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1623,\n                          columnNumber: 27\n                        }, this)]\n                      }, leagueKey, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1527,\n                        columnNumber: 25\n                      }, this);\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1520,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-white/70 text-sm text-center mt-2\",\n                    children: \"Click any league to view its members and scroll to their section\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1644,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1502,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05,\n                    rotate: 180\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: fetchRankingData,\n                  disabled: loading,\n                  className: \"flex items-center gap-3 px-6 py-3 md:py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 w-full sm:w-auto\",\n                  style: {\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TbRefresh, {\n                    className: `w-5 h-5 md:w-6 md:h-6 ${loading ? 'animate-spin' : ''}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1662,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Refresh\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1663,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1652,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1314,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1313,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1312,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1306,\n          columnNumber: 9\n        }, this), false && ((user === null || user === void 0 ? void 0 : user.role) === 'admin' || (user === null || user === void 0 ? void 0 : user.isAdmin)) && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: -20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6\n          },\n          className: \"px-3 sm:px-4 md:px-6 lg:px-8 mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-r from-purple-500/20 to-blue-500/20 backdrop-blur-lg rounded-xl p-4 border border-purple-300/30\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-white font-bold text-sm\",\n                    children: \"\\uD83D\\uDC51\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1682,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1681,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"font-bold text-white\",\n                    children: \"Admin View\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1685,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-white/80\",\n                    children: \"You're viewing as an admin. Admin accounts are excluded from student rankings.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1686,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1684,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1680,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1679,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1678,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1672,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: -50\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 1,\n            ease: \"easeOut\"\n          },\n          className: \"relative overflow-hidden mb-8\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-br from-blue-600 via-indigo-500 via-purple-500 via-cyan-500 to-teal-500 relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 bg-gradient-to-t from-black/40 via-black/20 to-transparent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1705,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1706,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative z-10 px-3 sm:px-4 md:px-6 lg:px-8 py-6 sm:py-8 md:py-12 lg:py-20\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"max-w-7xl mx-auto text-center\",\n                children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                  animate: {\n                    scale: [1, 1.02, 1],\n                    rotateY: [0, 5, 0]\n                  },\n                  transition: {\n                    duration: 4,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  },\n                  className: \"mb-6 md:mb-8\",\n                  children: /*#__PURE__*/_jsxDEV(\"h1\", {\n                    className: \"text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-black mb-2 md:mb-4 tracking-tight\",\n                    children: [/*#__PURE__*/_jsxDEV(motion.span, {\n                      animate: {\n                        backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']\n                      },\n                      transition: {\n                        duration: 4,\n                        repeat: Infinity,\n                        ease: \"linear\"\n                      },\n                      className: \"bg-gradient-to-r from-yellow-300 via-pink-300 via-cyan-300 via-purple-300 to-yellow-300 bg-clip-text text-transparent bg-400%\",\n                      style: {\n                        backgroundSize: '400% 400%',\n                        WebkitBackgroundClip: 'text',\n                        WebkitTextFillColor: 'transparent',\n                        filter: 'drop-shadow(2px 2px 4px rgba(0,0,0,0.8))'\n                      },\n                      children: \"HALL OF\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1726,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1745,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(motion.span, {\n                      animate: {\n                        textShadow: ['0 0 20px rgba(255,215,0,0.8), 0 0 40px rgba(255,215,0,0.6)', '0 0 30px rgba(255,215,0,1), 0 0 60px rgba(255,215,0,0.8)', '0 0 20px rgba(255,215,0,0.8), 0 0 40px rgba(255,215,0,0.6)']\n                      },\n                      transition: {\n                        duration: 2.5,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      },\n                      style: {\n                        color: '#FFD700',\n                        fontWeight: '900',\n                        textShadow: '3px 3px 6px rgba(0,0,0,0.9)'\n                      },\n                      children: \"CHAMPIONS\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1746,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1725,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1713,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n                  initial: {\n                    opacity: 0,\n                    y: 20\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  transition: {\n                    delay: 0.5,\n                    duration: 0.8\n                  },\n                  className: \"text-sm sm:text-base md:text-lg lg:text-xl font-semibold mb-4 md:mb-6 max-w-4xl mx-auto leading-relaxed px-2\",\n                  style: {\n                    color: '#F3F4F6',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    background: 'linear-gradient(45deg, #F3F4F6, #E5E7EB)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent'\n                  },\n                  children: \"\\u2728 Where legends are born and greatness is celebrated \\u2728\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1771,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    scale: 0.9\n                  },\n                  animate: {\n                    opacity: 1,\n                    scale: 1\n                  },\n                  transition: {\n                    delay: 0.8,\n                    duration: 0.8\n                  },\n                  className: \"mb-6 md:mb-8\",\n                  children: /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm sm:text-base md:text-lg font-medium text-yellow-200 bg-black/20 backdrop-blur-sm rounded-xl px-4 py-3 max-w-3xl mx-auto border border-yellow-400/30\",\n                    style: {\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      fontStyle: 'italic'\n                    },\n                    children: motivationalQuote\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1794,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1788,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 30\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  transition: {\n                    delay: 1,\n                    duration: 0.8\n                  },\n                  className: \"grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4 md:gap-6 max-w-4xl mx-auto\",\n                  children: [{\n                    icon: TbUsers,\n                    value: rankingData.length,\n                    label: 'Champions',\n                    bgGradient: 'from-blue-600/20 via-indigo-600/20 to-purple-600/20',\n                    iconColor: '#60A5FA',\n                    borderColor: '#3B82F6'\n                  }, {\n                    icon: TbTrophy,\n                    value: topPerformers.length,\n                    label: 'Top Performers',\n                    bgGradient: 'from-yellow-600/20 via-orange-600/20 to-red-600/20',\n                    iconColor: '#FBBF24',\n                    borderColor: '#F59E0B'\n                  }, {\n                    icon: TbFlame,\n                    value: rankingData.filter(u => u.currentStreak > 0).length,\n                    label: 'Active Streaks',\n                    bgGradient: 'from-red-600/20 via-pink-600/20 to-rose-600/20',\n                    iconColor: '#F87171',\n                    borderColor: '#EF4444'\n                  }, {\n                    icon: TbStar,\n                    value: rankingData.reduce((sum, u) => sum + (u.totalXP || 0), 0).toLocaleString(),\n                    label: 'Total XP',\n                    bgGradient: 'from-green-600/20 via-emerald-600/20 to-teal-600/20',\n                    iconColor: '#34D399',\n                    borderColor: '#10B981'\n                  }].map((stat, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                    initial: {\n                      opacity: 0,\n                      scale: 0.8\n                    },\n                    animate: {\n                      opacity: 1,\n                      scale: 1\n                    },\n                    transition: {\n                      delay: 1.2 + index * 0.1,\n                      duration: 0.6\n                    },\n                    whileHover: {\n                      scale: 1.05,\n                      y: -5\n                    },\n                    className: `bg-gradient-to-br ${stat.bgGradient} backdrop-blur-lg rounded-xl p-3 md:p-4 text-center relative overflow-hidden`,\n                    style: {\n                      border: `2px solid ${stat.borderColor}40`,\n                      boxShadow: `0 8px 32px ${stat.borderColor}20`\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1856,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(stat.icon, {\n                      className: \"w-6 h-6 md:w-8 md:h-8 mx-auto mb-2 relative z-10\",\n                      style: {\n                        color: stat.iconColor,\n                        filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.5))'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1857,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-lg sm:text-xl md:text-2xl lg:text-3xl font-black mb-1 relative z-10\",\n                      style: {\n                        color: stat.iconColor,\n                        textShadow: `3px 3px 6px rgba(0,0,0,0.9)`,\n                        filter: 'drop-shadow(0 0 10px currentColor)',\n                        fontSize: 'clamp(1rem, 4vw, 2.5rem)'\n                      },\n                      children: stat.value\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1861,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs sm:text-sm font-bold relative z-10\",\n                      style: {\n                        color: '#FFFFFF',\n                        textShadow: '2px 2px 4px rgba(0,0,0,0.9)',\n                        fontSize: 'clamp(0.75rem, 2vw, 1rem)'\n                      },\n                      children: stat.label\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1872,\n                      columnNumber: 23\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1844,\n                    columnNumber: 21\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1804,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1710,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1709,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1704,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1697,\n          columnNumber: 9\n        }, this), loading && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          className: \"flex flex-col items-center justify-center py-20\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            animate: {\n              rotate: 360\n            },\n            transition: {\n              duration: 2,\n              repeat: Infinity,\n              ease: \"linear\"\n            },\n            className: \"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1897,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-white/80 text-lg font-medium\",\n            children: \"Loading champions...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1902,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1892,\n          columnNumber: 11\n        }, this), !loading && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 30\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: 0.3,\n            duration: 0.8\n          },\n          className: \"px-4 sm:px-6 md:px-8 lg:px-12 pb-20 md:pb-24 lg:pb-32\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [topPerformers.length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                delay: 0.5,\n                duration: 0.8\n              },\n              className: \"mb-12\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-black text-center mb-6 md:mb-8 lg:mb-12 px-4\",\n                style: {\n                  background: 'linear-gradient(45deg, #FFD700, #FFA500, #FF6B35)',\n                  WebkitBackgroundClip: 'text',\n                  WebkitTextFillColor: 'transparent',\n                  textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                  filter: 'drop-shadow(0 0 15px #FFD700)'\n                },\n                children: \"\\uD83C\\uDFC6 CHAMPIONS PODIUM \\uD83C\\uDFC6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1924,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-end justify-center gap-4 sm:gap-6 md:gap-8 max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 mb-8\",\n                children: [topPerformers[1] && /*#__PURE__*/_jsxDEV(motion.div, {\n                  ref: user && String(topPerformers[1]._id) === String(user._id) ? podiumUserRef : null,\n                  \"data-user-id\": topPerformers[1]._id,\n                  \"data-user-rank\": 2,\n                  initial: {\n                    opacity: 0,\n                    x: -100,\n                    y: 50\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0,\n                    y: 0,\n                    scale: [1, 1.02, 1],\n                    rotateY: [0, 5, 0]\n                  },\n                  transition: {\n                    delay: 0.8,\n                    duration: 1.2,\n                    scale: {\n                      duration: 4,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    },\n                    rotateY: {\n                      duration: 6,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    }\n                  },\n                  whileHover: {\n                    scale: 1.05,\n                    y: -10\n                  },\n                  className: `relative order-1 ${shouldHighlightUser(topPerformers[1]._id) ? 'ring-8 ring-yellow-400 ring-opacity-100' : ''}`,\n                  style: {\n                    height: '280px',\n                    transform: shouldHighlightUser(topPerformers[1]._id) ? 'scale(1.08)' : 'scale(1)',\n                    filter: shouldHighlightUser(topPerformers[1]._id) ? 'brightness(1.3) saturate(1.2) drop-shadow(0 0 30px rgba(255, 215, 0, 1))' : 'none',\n                    transition: 'all 0.3s ease',\n                    border: shouldHighlightUser(topPerformers[1]._id) ? '4px solid #FFD700' : 'none',\n                    borderRadius: isCurrentUser(topPerformers[1]._id) ? '20px' : '0px',\n                    background: isCurrentUser(topPerformers[1]._id) ? 'linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1))' : 'transparent'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute bottom-0 w-full h-20 bg-gradient-to-t from-gray-400 to-gray-300 rounded-t-lg border-2 border-gray-500 flex items-center justify-center z-10\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-2xl font-black text-gray-800 relative z-20\",\n                      children: \"2nd\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1977,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1976,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `relative bg-gradient-to-br ${topPerformers[1].tier.color} p-1 rounded-xl ${topPerformers[1].tier.glow} shadow-xl mb-20`,\n                    style: {\n                      boxShadow: `0 6px 20px ${topPerformers[1].tier.shadowColor}50`,\n                      width: '200px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `${topPerformers[1].tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`,\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1991,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute -top-3 left-1/2 transform -translate-x-1/2 w-10 h-10 bg-gradient-to-br from-gray-300 to-gray-500 rounded-full flex items-center justify-center font-black text-lg shadow-lg relative z-20\",\n                        style: {\n                          color: '#1f2937',\n                          border: '2px solid #FFFFFF'\n                        },\n                        children: \"\\uD83E\\uDD48\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1994,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `relative mx-auto mb-3 ${user && topPerformers[1]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`,\n                        children: [/*#__PURE__*/_jsxDEV(ProfilePicture, {\n                          user: topPerformers[1],\n                          size: \"md\",\n                          showOnlineStatus: true,\n                          style: {\n                            width: '40px',\n                            height: '40px'\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2007,\n                          columnNumber: 31\n                        }, this), console.log('🥈 Second place user:', topPerformers[1])]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2005,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-sm font-bold mb-2 truncate\",\n                        style: {\n                          color: topPerformers[1].tier.nameColor\n                        },\n                        children: topPerformers[1].name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2021,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-lg font-black mb-2\",\n                        style: {\n                          color: topPerformers[1].tier.textColor\n                        },\n                        children: [topPerformers[1].totalXP.toLocaleString(), \" XP\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2028,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex justify-center gap-3 text-xs\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            color: topPerformers[1].tier.textColor\n                          },\n                          children: [\"\\uD83E\\uDDE0 \", topPerformers[1].totalQuizzesTaken]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2033,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            color: topPerformers[1].tier.textColor\n                          },\n                          children: [\"\\uD83D\\uDD25 \", topPerformers[1].currentStreak]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2036,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2032,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1988,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1981,\n                    columnNumber: 25\n                  }, this)]\n                }, `second-${topPerformers[1]._id}`, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1938,\n                  columnNumber: 23\n                }, this), topPerformers[0] && /*#__PURE__*/_jsxDEV(motion.div, {\n                  ref: user && String(topPerformers[0]._id) === String(user._id) ? podiumUserRef : null,\n                  \"data-user-id\": topPerformers[0]._id,\n                  \"data-user-rank\": 1,\n                  initial: {\n                    opacity: 0,\n                    y: -100,\n                    scale: 0.8\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0,\n                    scale: 1,\n                    rotateY: [0, 10, -10, 0],\n                    y: [0, -10, 0]\n                  },\n                  transition: {\n                    delay: 0.5,\n                    duration: 1.5,\n                    rotateY: {\n                      duration: 8,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    },\n                    y: {\n                      duration: 4,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    }\n                  },\n                  whileHover: {\n                    scale: 1.08,\n                    y: -15\n                  },\n                  className: `relative order-2 z-10 ${shouldHighlightUser(topPerformers[0]._id) ? 'ring-8 ring-yellow-400 ring-opacity-100' : ''}`,\n                  style: {\n                    height: '320px',\n                    transform: shouldHighlightUser(topPerformers[0]._id) ? 'scale(1.08)' : 'scale(1)',\n                    filter: shouldHighlightUser(topPerformers[0]._id) ? 'brightness(1.3) saturate(1.2) drop-shadow(0 0 30px rgba(255, 215, 0, 1))' : 'none',\n                    transition: 'all 0.3s ease',\n                    border: shouldHighlightUser(topPerformers[0]._id) ? '4px solid #FFD700' : 'none',\n                    borderRadius: isCurrentUser(topPerformers[0]._id) ? '20px' : '0px',\n                    background: isCurrentUser(topPerformers[0]._id) ? 'linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1))' : 'transparent'\n                  },\n                  \"data-section\": \"podium\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute bottom-0 w-full h-32 bg-gradient-to-t from-yellow-500 to-yellow-300 rounded-t-lg border-2 border-yellow-600 flex items-center justify-center z-10\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-3xl font-black text-yellow-900 relative z-20\",\n                      children: \"1st\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2088,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2087,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                    animate: {\n                      rotate: [0, 10, -10, 0],\n                      y: [0, -5, 0]\n                    },\n                    transition: {\n                      duration: 3,\n                      repeat: Infinity\n                    },\n                    className: \"absolute -top-16 left-1/2 transform -translate-x-1/2 z-30\",\n                    children: /*#__PURE__*/_jsxDEV(TbCrown, {\n                      className: \"w-16 h-16 text-yellow-400 drop-shadow-lg\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2097,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2092,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `relative bg-gradient-to-br ${topPerformers[0].tier.color} p-1.5 rounded-2xl ${topPerformers[0].tier.glow} shadow-2xl mb-32 transform scale-110`,\n                    style: {\n                      boxShadow: `0 8px 32px ${topPerformers[0].tier.shadowColor}60, 0 0 0 1px rgba(255,255,255,0.1)`,\n                      width: '240px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `${topPerformers[0].tier.bgColor} backdrop-blur-lg rounded-xl p-6 text-center relative overflow-hidden`,\n                      style: {\n                        background: `${topPerformers[0].tier.bgColor}, radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)`\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-white/10 to-transparent rounded-xl\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2114,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute -top-4 left-1/2 transform -translate-x-1/2 w-12 h-12 bg-gradient-to-br from-yellow-300 to-yellow-500 rounded-full flex items-center justify-center font-black text-xl shadow-lg relative z-20\",\n                        style: {\n                          color: '#1f2937',\n                          textShadow: '1px 1px 2px rgba(0,0,0,0.3)',\n                          border: '2px solid #FFFFFF'\n                        },\n                        children: \"\\uD83D\\uDC51\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2117,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `relative mx-auto mb-4 ${user && topPerformers[0]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`,\n                        children: [/*#__PURE__*/_jsxDEV(ProfilePicture, {\n                          user: topPerformers[0],\n                          size: \"lg\",\n                          showOnlineStatus: true,\n                          style: {\n                            width: '48px',\n                            height: '48px'\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2131,\n                          columnNumber: 31\n                        }, this), console.log('🥇 First place user:', topPerformers[0]), user && topPerformers[0]._id === user._id && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"absolute -bottom-2 -right-2 rounded-full p-2 animate-pulse\",\n                          style: {\n                            background: 'linear-gradient(45deg, #fbbf24, #f59e0b)',\n                            boxShadow: '0 2px 8px rgba(0,0,0,0.3)'\n                          },\n                          children: /*#__PURE__*/_jsxDEV(TbStar, {\n                            className: \"w-6 h-6 text-gray-900\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2150,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2143,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2129,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center justify-center gap-2 mb-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                          className: \"text-lg font-black truncate\",\n                          style: {\n                            color: topPerformers[0].tier.nameColor,\n                            textShadow: `2px 2px 4px ${topPerformers[0].tier.shadowColor}`,\n                            filter: 'drop-shadow(0 0 8px currentColor)'\n                          },\n                          children: topPerformers[0].name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2157,\n                          columnNumber: 31\n                        }, this), isCurrentUser(topPerformers[0]._id) && /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"px-2 py-1 rounded-full text-xs font-black animate-pulse\",\n                          style: {\n                            background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                            color: '#1f2937',\n                            boxShadow: '0 2px 8px rgba(255,215,0,0.8)',\n                            border: '1px solid #FFFFFF',\n                            fontSize: '10px'\n                          },\n                          children: \"\\uD83C\\uDFAF YOU\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2168,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2156,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r ${topPerformers[0].tier.color} rounded-full text-sm font-black mb-3 relative z-10`,\n                        style: {\n                          background: `linear-gradient(135deg, ${topPerformers[0].tier.borderColor}, ${topPerformers[0].tier.textColor})`,\n                          color: '#FFFFFF',\n                          textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                          boxShadow: `0 4px 15px ${topPerformers[0].tier.shadowColor}60`,\n                          border: '2px solid rgba(255,255,255,0.2)'\n                        },\n                        children: [topPerformers[0].tier.icon && /*#__PURE__*/React.createElement(topPerformers[0].tier.icon, {\n                          className: \"w-4 h-4\",\n                          style: {\n                            color: '#FFFFFF'\n                          }\n                        }), /*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            color: '#FFFFFF'\n                          },\n                          children: topPerformers[0].tier.title\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2197,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2183,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"space-y-2 relative z-10\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-xl font-black\",\n                          style: {\n                            color: topPerformers[0].tier.nameColor,\n                            textShadow: `2px 2px 4px ${topPerformers[0].tier.shadowColor}`,\n                            filter: 'drop-shadow(0 0 8px currentColor)'\n                          },\n                          children: [topPerformers[0].totalXP.toLocaleString(), \" XP\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2202,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex justify-center gap-4 text-sm\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-center\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex items-center gap-1 justify-center\",\n                              children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                                className: \"w-4 h-4\",\n                                style: {\n                                  color: topPerformers[0].tier.textColor\n                                }\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2213,\n                                columnNumber: 37\n                              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"font-bold\",\n                                style: {\n                                  color: topPerformers[0].tier.textColor\n                                },\n                                children: topPerformers[0].totalQuizzesTaken\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2214,\n                                columnNumber: 37\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2212,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"text-xs opacity-80\",\n                              style: {\n                                color: topPerformers[0].tier.textColor\n                              },\n                              children: \"Quizzes\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2218,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2211,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-center\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex items-center gap-1 justify-center\",\n                              children: [/*#__PURE__*/_jsxDEV(TbFlame, {\n                                className: \"w-4 h-4\",\n                                style: {\n                                  color: '#FF6B35'\n                                }\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2222,\n                                columnNumber: 37\n                              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"font-bold\",\n                                style: {\n                                  color: topPerformers[0].tier.textColor\n                                },\n                                children: topPerformers[0].currentStreak\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2223,\n                                columnNumber: 37\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2221,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"text-xs opacity-80\",\n                              style: {\n                                color: topPerformers[0].tier.textColor\n                              },\n                              children: \"Streak\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2227,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2220,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2210,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2201,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2108,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2101,\n                    columnNumber: 25\n                  }, this)]\n                }, `first-${topPerformers[0]._id}`, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2047,\n                  columnNumber: 23\n                }, this), topPerformers[2] && /*#__PURE__*/_jsxDEV(motion.div, {\n                  ref: user && String(topPerformers[2]._id) === String(user._id) ? podiumUserRef : null,\n                  \"data-user-id\": topPerformers[2]._id,\n                  \"data-user-rank\": 3,\n                  initial: {\n                    opacity: 0,\n                    x: 100,\n                    y: 50\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0,\n                    y: 0,\n                    scale: [1, 1.02, 1],\n                    rotateY: [0, -5, 0]\n                  },\n                  transition: {\n                    delay: 1.0,\n                    duration: 1.2,\n                    scale: {\n                      duration: 4,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    },\n                    rotateY: {\n                      duration: 6,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    }\n                  },\n                  whileHover: {\n                    scale: 1.05,\n                    y: -10\n                  },\n                  className: `relative order-3 ${shouldHighlightUser(topPerformers[2]._id) ? 'ring-8 ring-yellow-400 ring-opacity-100' : ''}`,\n                  style: {\n                    height: '280px',\n                    transform: shouldHighlightUser(topPerformers[2]._id) ? 'scale(1.08)' : 'scale(1)',\n                    filter: shouldHighlightUser(topPerformers[2]._id) ? 'brightness(1.3) saturate(1.2) drop-shadow(0 0 30px rgba(255, 215, 0, 1))' : 'none',\n                    transition: 'all 0.3s ease',\n                    border: shouldHighlightUser(topPerformers[2]._id) ? '4px solid #FFD700' : 'none',\n                    borderRadius: isCurrentUser(topPerformers[2]._id) ? '20px' : '0px',\n                    background: isCurrentUser(topPerformers[2]._id) ? 'linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1))' : 'transparent'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute bottom-0 w-full h-16 bg-gradient-to-t from-amber-600 to-amber-400 rounded-t-lg border-2 border-amber-700 flex items-center justify-center z-10\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xl font-black text-amber-900 relative z-20\",\n                      children: \"3rd\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2277,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2276,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `relative bg-gradient-to-br ${topPerformers[2].tier.color} p-1 rounded-xl ${topPerformers[2].tier.glow} shadow-xl mb-16`,\n                    style: {\n                      boxShadow: `0 6px 20px ${topPerformers[2].tier.shadowColor}50`,\n                      width: '200px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `${topPerformers[2].tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`,\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2291,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute -top-3 left-1/2 transform -translate-x-1/2 w-10 h-10 bg-gradient-to-br from-amber-600 to-amber-800 rounded-full flex items-center justify-center font-black text-lg shadow-lg relative z-20\",\n                        style: {\n                          color: '#1f2937',\n                          border: '2px solid #FFFFFF'\n                        },\n                        children: \"\\uD83E\\uDD49\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2294,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `relative mx-auto mb-3 ${user && topPerformers[2]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`,\n                        children: /*#__PURE__*/_jsxDEV(ProfilePicture, {\n                          user: topPerformers[2],\n                          size: \"md\",\n                          showOnlineStatus: true,\n                          style: {\n                            width: '40px',\n                            height: '40px'\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2306,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2305,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-sm font-bold mb-2 truncate\",\n                        style: {\n                          color: topPerformers[2].tier.nameColor\n                        },\n                        children: topPerformers[2].name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2318,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-lg font-black mb-2\",\n                        style: {\n                          color: topPerformers[2].tier.textColor\n                        },\n                        children: [topPerformers[2].totalXP.toLocaleString(), \" XP\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2325,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex justify-center gap-3 text-xs\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            color: topPerformers[2].tier.textColor\n                          },\n                          children: [\"\\uD83E\\uDDE0 \", topPerformers[2].totalQuizzesTaken]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2330,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            color: topPerformers[2].tier.textColor\n                          },\n                          children: [\"\\uD83D\\uDD25 \", topPerformers[2].currentStreak]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2333,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2329,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2288,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2281,\n                    columnNumber: 25\n                  }, this)]\n                }, `third-${topPerformers[2]._id}`, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2238,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1935,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1918,\n              columnNumber: 17\n            }, this), selectedLeague ? /* SELECTED LEAGUE VIEW */\n            leagueUsers.length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 1,\n                duration: 0.8\n              },\n              className: \"mt-16 main-ranking-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center mb-8 md:mb-12\",\n                children: [/*#__PURE__*/_jsxDEV(motion.h2, {\n                  className: \"text-2xl sm:text-3xl md:text-4xl font-black mb-3\",\n                  style: {\n                    background: `linear-gradient(45deg, ${leagueSystem[selectedLeague].borderColor}, ${leagueSystem[selectedLeague].textColor})`,\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    filter: `drop-shadow(0 0 12px ${leagueSystem[selectedLeague].borderColor})`\n                  },\n                  animate: {\n                    scale: [1, 1.01, 1]\n                  },\n                  transition: {\n                    duration: 4,\n                    repeat: Infinity\n                  },\n                  children: [leagueSystem[selectedLeague].leagueIcon, \" \", leagueSystem[selectedLeague].title, \" LEAGUE \", leagueSystem[selectedLeague].leagueIcon]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2365,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-white/70 text-sm md:text-base font-medium\",\n                  children: [leagueUsers.length, \" champions in this league\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2379,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: () => setSelectedLeague(null),\n                  className: \"mt-4 px-6 py-2 bg-gradient-to-r from-gray-600 to-gray-700 text-white rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300\",\n                  children: \"\\u2190 Back to All Leagues\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2382,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2364,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"max-w-6xl mx-auto px-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid gap-3 md:gap-4\",\n                  children: leagueUsers.map((champion, index) => {\n                    const actualRank = index + 1;\n                    const isCurrentUser = user && String(champion._id) === String(user._id);\n                    return /*#__PURE__*/_jsxDEV(motion.div, {\n                      ref: isCurrentUser ? listUserRef : null,\n                      \"data-user-id\": champion._id,\n                      \"data-user-rank\": actualRank,\n                      initial: {\n                        opacity: 0,\n                        y: 20\n                      },\n                      animate: {\n                        opacity: 1,\n                        y: 0\n                      },\n                      transition: {\n                        delay: 0.1 + index * 0.05,\n                        duration: 0.4\n                      },\n                      whileHover: {\n                        scale: 1.01,\n                        y: -2\n                      },\n                      className: `ranking-card group relative ${shouldHighlightUser(champion._id) ? 'ring-8 ring-yellow-400 ring-opacity-100' : ''}`,\n                      style: {\n                        transform: shouldHighlightUser(champion._id) ? 'scale(1.05)' : 'scale(1)',\n                        filter: shouldHighlightUser(champion._id) ? 'brightness(1.25) saturate(1.3) drop-shadow(0 0 25px rgba(255, 215, 0, 1))' : 'none',\n                        transition: 'all 0.3s ease',\n                        border: shouldHighlightUser(champion._id) ? '4px solid #FFD700' : 'none',\n                        borderRadius: shouldHighlightUser(champion._id) ? '16px' : '0px',\n                        background: isCurrentUser ? 'linear-gradient(135deg, rgba(255, 215, 0, 0.15), rgba(255, 165, 0, 0.15))' : 'transparent',\n                        position: 'relative',\n                        zIndex: isCurrentUser ? 10 : 1\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `bg-gradient-to-r ${champion.tier.color} p-0.5 rounded-2xl ${champion.tier.glow} transition-all duration-300 group-hover:scale-[1.01]`,\n                        style: {\n                          boxShadow: `0 4px 20px ${champion.tier.shadowColor}40`\n                        },\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: `${champion.tier.bgColor} backdrop-blur-xl rounded-2xl p-4 flex items-center gap-4 relative overflow-hidden`,\n                          style: {\n                            border: `1px solid ${champion.tier.borderColor}30`\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-r from-white/3 to-transparent rounded-2xl\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2441,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex items-center gap-3 flex-shrink-0\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"relative\",\n                              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center font-black text-sm shadow-lg relative z-10\",\n                                style: {\n                                  color: '#FFFFFF',\n                                  textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                                  border: '2px solid rgba(255,255,255,0.2)',\n                                  boxShadow: '0 2px 8px rgba(0,0,0,0.3)'\n                                },\n                                children: [\"#\", actualRank]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2447,\n                                columnNumber: 39\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2446,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"relative\",\n                              children: [/*#__PURE__*/_jsxDEV(ProfilePicture, {\n                                user: champion,\n                                size: \"sm\",\n                                showOnlineStatus: true,\n                                style: {\n                                  width: '32px',\n                                  height: '32px'\n                                }\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2462,\n                                columnNumber: 39\n                              }, this), isCurrentUser && /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"absolute -top-1 -right-1 w-4 h-4 rounded-full flex items-center justify-center animate-pulse\",\n                                style: {\n                                  background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                  boxShadow: '0 2px 6px rgba(255,215,0,0.6)'\n                                },\n                                children: /*#__PURE__*/_jsxDEV(TbStar, {\n                                  className: \"w-2.5 h-2.5 text-gray-900\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2480,\n                                  columnNumber: 43\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2473,\n                                columnNumber: 41\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2461,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2444,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex-1 min-w-0 px-2\",\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"space-y-1\",\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"flex items-center gap-2 mb-1\",\n                                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                                  className: \"text-base md:text-lg font-bold truncate\",\n                                  style: {\n                                    color: champion.tier.nameColor,\n                                    textShadow: `1px 1px 2px ${champion.tier.shadowColor}`,\n                                    filter: 'drop-shadow(0 0 4px currentColor)'\n                                  },\n                                  children: champion.name\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2491,\n                                  columnNumber: 41\n                                }, this), isCurrentUser && /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: \"px-3 py-1 rounded-full text-sm font-black animate-pulse\",\n                                  style: {\n                                    background: 'linear-gradient(45deg, #FFD700, #FFA500, #FFD700)',\n                                    color: '#1f2937',\n                                    boxShadow: '0 4px 12px rgba(255,215,0,0.8), 0 0 20px rgba(255,215,0,0.6)',\n                                    border: '2px solid #FFFFFF',\n                                    textShadow: '1px 1px 2px rgba(0,0,0,0.3)',\n                                    fontSize: '12px',\n                                    fontWeight: '900'\n                                  },\n                                  children: \"\\uD83C\\uDFAF YOU\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2502,\n                                  columnNumber: 43\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2490,\n                                columnNumber: 39\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"text-xs text-white/70 mt-0.5\",\n                                children: [champion.level, \" \\u2022 Class \", champion.class]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2520,\n                                columnNumber: 39\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2488,\n                              columnNumber: 37\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2487,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex flex-col items-end gap-1 flex-shrink-0\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"text-lg md:text-xl font-black mb-2\",\n                              style: {\n                                color: champion.tier.nameColor,\n                                textShadow: `1px 1px 2px ${champion.tier.shadowColor}`,\n                                filter: 'drop-shadow(0 0 6px currentColor)'\n                              },\n                              children: [champion.totalXP.toLocaleString(), \" XP\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2529,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex items-center gap-3 text-xs\",\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"flex items-center gap-1 px-2 py-1 rounded-md\",\n                                style: {\n                                  backgroundColor: `${champion.tier.borderColor}20`,\n                                  color: champion.tier.textColor\n                                },\n                                children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                                  className: \"w-3 h-3\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2549,\n                                  columnNumber: 41\n                                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: \"font-medium\",\n                                  children: champion.totalQuizzesTaken\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2550,\n                                  columnNumber: 41\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2542,\n                                columnNumber: 39\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"flex items-center gap-1 px-2 py-1 rounded-md\",\n                                style: {\n                                  backgroundColor: '#FF6B3520',\n                                  color: '#FF6B35'\n                                },\n                                children: [/*#__PURE__*/_jsxDEV(TbFlame, {\n                                  className: \"w-3 h-3\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2559,\n                                  columnNumber: 41\n                                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: \"font-medium\",\n                                  children: champion.currentStreak\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2560,\n                                  columnNumber: 41\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2552,\n                                columnNumber: 39\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2541,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2527,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2434,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2428,\n                        columnNumber: 31\n                      }, this)\n                    }, champion._id, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2400,\n                      columnNumber: 29\n                    }, this);\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2394,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2393,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2357,\n              columnNumber: 19\n            }, this) : /* ALL LEAGUES GROUPED VIEW */\n            Object.keys(leagueGroups).length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 1,\n                duration: 0.8\n              },\n              className: \"mt-16 main-ranking-section\",\n              id: \"grouped-leagues-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center mb-8 md:mb-12\",\n                children: [/*#__PURE__*/_jsxDEV(motion.h2, {\n                  className: \"text-2xl sm:text-3xl md:text-4xl font-black mb-3\",\n                  style: {\n                    background: 'linear-gradient(45deg, #8B5CF6, #06B6D4, #10B981)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    filter: 'drop-shadow(0 0 12px #8B5CF6)'\n                  },\n                  animate: {\n                    scale: [1, 1.01, 1]\n                  },\n                  transition: {\n                    duration: 4,\n                    repeat: Infinity\n                  },\n                  children: \"\\uD83C\\uDFC6 LEAGUE RANKINGS \\uD83C\\uDFC6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2585,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-white/70 text-sm md:text-base font-medium\",\n                  children: \"Click on any league icon above to see its members\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2599,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2584,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"max-w-6xl mx-auto px-4 space-y-8\",\n                children: getOrderedLeagues().map(leagueKey => {\n                  const league = leagueSystem[leagueKey];\n                  const leagueData = leagueGroups[leagueKey];\n                  const topUsers = leagueData.users.slice(0, 3); // Show top 3 from each league\n\n                  return /*#__PURE__*/_jsxDEV(motion.div, {\n                    ref: el => leagueRefs.current[leagueKey] = el,\n                    initial: {\n                      opacity: 0,\n                      y: 20\n                    },\n                    animate: {\n                      opacity: 1,\n                      y: 0\n                    },\n                    transition: {\n                      delay: 0.2,\n                      duration: 0.6\n                    },\n                    className: \"bg-gradient-to-r from-white/5 to-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/10\",\n                    id: `league-${leagueKey}`,\n                    \"data-league\": leagueKey,\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center justify-between mb-6\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-16 h-16 rounded-xl flex items-center justify-center text-3xl\",\n                          style: {\n                            background: `linear-gradient(135deg, ${league.borderColor}40, ${league.textColor}20)`,\n                            border: `2px solid ${league.borderColor}60`,\n                            boxShadow: `0 4px 20px ${league.shadowColor}40`\n                          },\n                          children: league.leagueIcon\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2625,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                            className: \"text-2xl font-black mb-1\",\n                            style: {\n                              color: league.nameColor,\n                              textShadow: `2px 2px 4px ${league.shadowColor}`,\n                              filter: 'drop-shadow(0 0 8px currentColor)'\n                            },\n                            children: [league.title, \" LEAGUE\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2636,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-white/70 text-sm\",\n                            children: [leagueData.users.length, \" champions \\u2022 \", league.description]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2646,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2635,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2624,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                        whileHover: {\n                          scale: 1.05\n                        },\n                        whileTap: {\n                          scale: 0.95\n                        },\n                        onClick: () => handleLeagueSelect(leagueKey),\n                        className: \"px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300\",\n                        children: [\"View All (\", leagueData.users.length, \")\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2651,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2623,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4\",\n                      children: topUsers.map((champion, index) => {\n                        const isCurrentUser = user && champion._id === user._id;\n                        const leagueRank = index + 1;\n                        return /*#__PURE__*/_jsxDEV(motion.div, {\n                          \"data-user-id\": champion._id,\n                          \"data-user-rank\": leagueRank,\n                          initial: {\n                            opacity: 0,\n                            scale: 0.9\n                          },\n                          animate: {\n                            opacity: 1,\n                            scale: 1\n                          },\n                          transition: {\n                            delay: 0.3 + index * 0.1,\n                            duration: 0.4\n                          },\n                          whileHover: {\n                            scale: 1.02,\n                            y: -2\n                          },\n                          className: `relative ${shouldHighlightUser(champion._id) ? 'ring-2 ring-yellow-400/60' : ''}`,\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: `bg-gradient-to-br ${champion.tier.color} p-0.5 rounded-xl ${champion.tier.glow} shadow-lg`,\n                            style: {\n                              boxShadow: `0 4px 15px ${champion.tier.shadowColor}30`\n                            },\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: `${champion.tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`,\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2691,\n                                columnNumber: 41\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"absolute -top-2 -right-2 w-6 h-6 rounded-full flex items-center justify-center font-black text-xs\",\n                                style: {\n                                  background: league.borderColor,\n                                  color: '#FFFFFF',\n                                  border: '2px solid #FFFFFF'\n                                },\n                                children: [\"#\", leagueRank]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2694,\n                                columnNumber: 41\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: `relative mx-auto mb-3 ${isCurrentUser ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`,\n                                children: [/*#__PURE__*/_jsxDEV(ProfilePicture, {\n                                  user: champion,\n                                  size: \"md\",\n                                  showOnlineStatus: true,\n                                  style: {\n                                    width: '40px',\n                                    height: '40px'\n                                  }\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2711,\n                                  columnNumber: 43\n                                }, this), isCurrentUser && /*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"absolute -bottom-1 -right-1 w-4 h-4 rounded-full flex items-center justify-center animate-pulse\",\n                                  style: {\n                                    background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                    boxShadow: '0 2px 6px rgba(255,215,0,0.6)'\n                                  },\n                                  children: /*#__PURE__*/_jsxDEV(TbStar, {\n                                    className: \"w-2.5 h-2.5 text-gray-900\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 2728,\n                                    columnNumber: 47\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2721,\n                                  columnNumber: 45\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2706,\n                                columnNumber: 41\n                              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                                className: \"text-sm font-bold mb-2 truncate\",\n                                style: {\n                                  color: champion.tier.nameColor\n                                },\n                                children: [champion.name, isCurrentUser && /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: \"ml-1 text-xs text-yellow-400\",\n                                  children: \"\\uD83D\\uDC51\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2740,\n                                  columnNumber: 45\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2734,\n                                columnNumber: 41\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"text-lg font-black mb-2\",\n                                style: {\n                                  color: champion.tier.textColor\n                                },\n                                children: [champion.totalXP.toLocaleString(), \" XP\"]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2744,\n                                columnNumber: 41\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"flex justify-center gap-3 text-xs\",\n                                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                  style: {\n                                    color: champion.tier.textColor\n                                  },\n                                  children: [\"\\uD83E\\uDDE0 \", champion.totalQuizzesTaken]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2749,\n                                  columnNumber: 43\n                                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                  style: {\n                                    color: champion.tier.textColor\n                                  },\n                                  children: [\"\\uD83D\\uDD25 \", champion.currentStreak]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2752,\n                                  columnNumber: 43\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2748,\n                                columnNumber: 41\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2688,\n                              columnNumber: 39\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2682,\n                            columnNumber: 37\n                          }, this)\n                        }, champion._id, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2668,\n                          columnNumber: 35\n                        }, this);\n                      })\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2662,\n                      columnNumber: 29\n                    }, this), leagueData.users.length > 3 && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-center mt-4\",\n                      children: /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-white/60 text-sm\",\n                        children: [\"+\", leagueData.users.length - 3, \" more champions in this league\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2766,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2765,\n                      columnNumber: 31\n                    }, this)]\n                  }, leagueKey, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2612,\n                    columnNumber: 27\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2605,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2576,\n              columnNumber: 19\n            }, this), rankingData.length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 1.8,\n                duration: 0.8\n              },\n              className: \"mt-12 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-green-500/20 backdrop-blur-lg rounded-2xl p-6 border border-blue-400/30\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-xl font-bold mb-4\",\n                  style: {\n                    color: '#60A5FA',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  },\n                  children: \"\\uD83D\\uDCCA Real User Data Integration\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2791,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-green-500/20 rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-green-400 font-bold text-lg\",\n                      children: rankingData.filter(u => u.dataSource === 'reports').length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2798,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-white/80\",\n                      children: \"\\uD83D\\uDCCA Live Quiz Data\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2801,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2797,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-blue-500/20 rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-blue-400 font-bold text-lg\",\n                      children: rankingData.filter(u => u.dataSource === 'legacy_points').length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2804,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-white/80\",\n                      children: \"\\uD83D\\uDCC8 Legacy Points\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2807,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2803,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-purple-500/20 rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-purple-400 font-bold text-lg\",\n                      children: rankingData.filter(u => u.dataSource === 'estimated').length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2810,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-white/80\",\n                      children: \"\\uD83D\\uDD2E Estimated Stats\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2813,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2809,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2796,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-white/70 text-sm mt-4\",\n                  children: \"Using real database users (admins excluded) with intelligent data processing\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2816,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2790,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2784,\n              columnNumber: 17\n            }, this), currentUserRank && currentUserRank > 3 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                delay: 1.5,\n                duration: 0.8\n              },\n              className: \"mt-12 bg-gradient-to-r from-yellow-500/20 via-orange-500/20 to-red-500/20 backdrop-blur-lg rounded-2xl p-6 border border-yellow-400/30\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-2xl font-bold mb-2\",\n                  style: {\n                    color: '#ffffff',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  },\n                  children: \"Your Current Position\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2832,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-6xl font-black mb-2\",\n                  style: {\n                    color: '#fbbf24',\n                    textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                    fontWeight: '900'\n                  },\n                  children: [\"#\", currentUserRank]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2837,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-lg\",\n                  style: {\n                    color: '#e5e7eb',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                    fontWeight: '600'\n                  },\n                  children: \"You're doing amazing! Keep pushing forward to reach the podium! \\uD83D\\uDE80\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2842,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2831,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2825,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 2,\n                duration: 0.8\n              },\n              className: \"mt-16 text-center\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gradient-to-r from-purple-600/20 via-pink-600/20 to-indigo-600/20 backdrop-blur-lg rounded-2xl p-8 border border-white/10\",\n                children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                  animate: {\n                    scale: [1, 1.05, 1]\n                  },\n                  transition: {\n                    duration: 3,\n                    repeat: Infinity\n                  },\n                  children: /*#__PURE__*/_jsxDEV(TbRocket, {\n                    className: \"w-16 h-16 text-yellow-400 mx-auto mb-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2865,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2861,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-3xl font-bold mb-4\",\n                  style: {\n                    color: '#ffffff',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  },\n                  children: \"Ready to Rise Higher?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2867,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xl mb-6 max-w-2xl mx-auto\",\n                  style: {\n                    color: '#e5e7eb',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                    fontWeight: '600'\n                  },\n                  children: \"Every quiz you take, every challenge you conquer, brings you closer to greatness. Your journey to the top starts with the next question!\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2872,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  className: \"bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300\",\n                  onClick: () => window.location.href = '/user/quiz',\n                  children: \"Take a Quiz Now! \\uD83C\\uDFAF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2880,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2860,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2854,\n              columnNumber: 15\n            }, this), rankingData.length === 0 && !loading && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              className: \"text-center py-20\",\n              children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n                className: \"w-24 h-24 text-white/30 mx-auto mb-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2898,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-2xl font-bold mb-4\",\n                style: {\n                  color: '#ffffff',\n                  textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                  fontWeight: '800'\n                },\n                children: \"No Champions Yet\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2899,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-lg\",\n                style: {\n                  color: '#e5e7eb',\n                  textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                  fontWeight: '600'\n                },\n                children: \"Be the first to take a quiz and claim your spot in the Hall of Champions!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2904,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2893,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1914,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1908,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1304,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1258,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(AmazingRankingPage, \"2oJgcCnITao5T8TtrsxZjKrFxvY=\", false, function () {\n  return [useSelector, useNavigate];\n});\n_c = AmazingRankingPage;\nexport default AmazingRankingPage;\nvar _c;\n$RefreshReg$(_c, \"AmazingRankingPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "motion", "AnimatePresence", "useSelector", "useNavigate", "message", "TbTrophy", "TbCrown", "TbStar", "TbFlame", "TbBrain", "TbHome", "TbRefresh", "TbMedal", "TbRocket", "TbDiamond", "TbAward", "TbShield", "TbUsers", "getAllReportsForRanking", "getXPLeaderboard", "getUserRanking", "getAllUsers", "ProfilePicture", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AmazingRankingPage", "_s", "userState", "state", "users", "reduxUser", "user", "localStorageUser", "userData", "localStorage", "getItem", "JSON", "parse", "tokenUser", "token", "payload", "atob", "split", "fullUserData", "setFullUserData", "console", "log", "redux", "final", "userId", "navigate", "rankingData", "setRankingData", "loading", "setLoading", "currentUserRank", "setCurrentUserRank", "viewMode", "setViewMode", "showStats", "setShowStats", "animationPhase", "setAnimationPhase", "motivationalQuote", "setMotivationalQuote", "currentUserLeague", "setCurrentUserLeague", "leagueUsers", "setLeagueUsers", "showLeagueView", "setShowLeagueView", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedLeague", "leagueGroups", "setLeagueGroups", "userHasBeenShown", "setUserHasBeenShown", "autoScrollCompleted", "setAutoScrollCompleted", "leagueRefs", "headerRef", "currentUserRef", "podiumUserRef", "listUserRef", "motivationalQuotes", "leagueSystem", "mythic", "min", "color", "bgColor", "textColor", "nameColor", "shadowColor", "glow", "icon", "title", "description", "borderColor", "effect", "leagueIcon", "promotionXP", "relegationXP", "maxUsers", "legendary", "diamond", "platinum", "gold", "silver", "bronze", "rookie", "getUserLeague", "xp", "league", "config", "Object", "entries", "groupUsersByLeague", "leagues", "for<PERSON>ach", "userLeague", "totalXP", "push", "tier", "keys", "leagueKey", "sort", "a", "b", "getCurrentUserLeagueData", "allUsers", "currentUser", "filter", "userRank", "findIndex", "u", "_id", "totalInLeague", "length", "handleLeagueSelect", "_leagueGroups$leagueK", "setTimeout", "leagueElement", "document", "querySelector", "getElementById", "current", "scrollIntoView", "behavior", "block", "inline", "style", "transform", "transition", "boxShadow", "getOrderedLeagues", "leagueOrder", "fetchRankingData", "forceRefresh", "xpLeaderboardResponse", "limit", "levelFilter", "level", "includeInactive", "_t", "Date", "now", "success", "data", "filteredData", "totalQuizzesTaken", "slice", "map", "name", "profileImage", "profilePicture", "hasProfileData", "transformedData", "index", "email", "class", "averageScore", "currentStreak", "bestStreak", "subscriptionStatus", "rank", "isRealUser", "rankingScore", "currentLevel", "xpToNextLevel", "lifetimeXP", "seasonXP", "achievements", "dataSource", "userRankIndex", "item", "userLeagueData", "grouped", "xpError", "rankingResponse", "usersResponse", "error", "userError", "userReportsMap", "_item$user", "reports", "userReports", "totalQuizzes", "totalScore", "reduce", "sum", "report", "score", "Math", "round", "totalPoints", "estimatedQuizzes", "max", "floor", "estimatedAverage", "tempStreak", "pointsPerQuiz", "originalPoints", "hasReports", "String", "process", "env", "NODE_ENV", "userIdType", "isAdmin", "role", "userXP", "userRankPosition", "totalRankedUsers", "firstFewUserIds", "id", "type", "exactMatch", "find", "stringMatch", "nameMatch", "dataSources", "legacy_points", "estimated", "quizzes", "avg", "source", "warning", "fetchFullUserData", "response", "userDataWithProfile", "userDataAlt", "includes", "userInRanking", "randomQuote", "random", "animationTimer", "setInterval", "prev", "handleWindowFocus", "handleRankingUpdate", "event", "detail", "removeItem", "refreshWithRetry", "attempts", "i", "_event$detail", "newTotalXP", "updatedUser", "Promise", "resolve", "window", "addEventListener", "clearInterval", "removeEventListener", "leagueData", "userInLeague", "topPerformers", "otherPerformers", "getUserLeagueInfo", "isInPodium", "some", "performer", "podiumPosition", "position", "_leagueData$users", "totalUsers", "userLeagueInfo", "isCurrentUser", "shouldHighlightUser", "handlePageClick", "rankingData<PERSON>ength", "<PERSON><PERSON>ser", "completed", "hasData", "scrollToUser", "podiumSection", "userElement", "timer", "clearTimeout", "getSubscriptionBadge", "subscriptionEndDate", "subscriptionPlan", "activePlanTitle", "userIndex", "endDate", "isActive", "text", "RankingSkeleton", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Array", "_", "onClick", "div", "initial", "opacity", "y", "animate", "exit", "x", "duration", "repeat", "Infinity", "delay", "left", "top", "button", "whileHover", "scale", "whileTap", "fontSize", "innerWidth", "background", "size", "showOnlineStatus", "border", "username", "points", "toLocaleString", "_leagueData$users2", "leagueInfo", "toUpperCase", "quizzesCompleted", "quizzesTaken", "streak", "avgScore", "_leagueData$users3", "undefined", "h3", "WebkitBackgroundClip", "WebkitTextFillColor", "textShadow", "_leagueGroups$leagueK2", "isSelected", "userCount", "ease", "rotate", "zIndex", "span", "disabled", "rotateY", "backgroundPosition", "backgroundSize", "fontWeight", "p", "fontStyle", "value", "label", "bgGradient", "iconColor", "stat", "ref", "height", "borderRadius", "width", "createElement", "h2", "champion", "actualRank", "backgroundColor", "topUsers", "el", "leagueRank", "location", "href", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Ranking/index.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useSelector } from 'react-redux';\nimport { useNavigate } from 'react-router-dom';\nimport { message } from 'antd';\nimport {\n  TbTrophy,\n  TbCrown,\n  TbStar,\n  TbFlame,\n  TbBrain,\n  TbHome,\n  TbRefresh,\n  TbMedal,\n  TbRocket,\n  TbDiamond,\n  TbAward,\n  TbShield,\n  TbUsers\n} from 'react-icons/tb';\nimport { getAllReportsForRanking, getXPLeaderboard, getUserRanking } from '../../../apicalls/reports';\nimport { getAllUsers } from '../../../apicalls/users';\nimport ProfilePicture from '../../../components/common/ProfilePicture';\n\nconst AmazingRankingPage = () => {\n  const userState = useSelector((state) => state.users || {});\n  const reduxUser = userState.user || null;\n\n  // Try multiple sources for user data\n  const localStorageUser = (() => {\n    try {\n      const userData = localStorage.getItem('user');\n      return userData ? JSON.parse(userData) : null;\n    } catch {\n      return null;\n    }\n  })();\n\n  const tokenUser = (() => {\n    try {\n      const token = localStorage.getItem('token');\n      if (token) {\n        const payload = JSON.parse(atob(token.split('.')[1]));\n        return payload;\n      }\n      return null;\n    } catch {\n      return null;\n    }\n  })();\n\n  // Use the first available user data\n  const user = reduxUser || localStorageUser || tokenUser;\n\n  // State for full user data\n  const [fullUserData, setFullUserData] = useState(null);\n\n  // Debug: Log all user sources\n  console.log('🔍 User Data Sources:', {\n    redux: reduxUser,\n    localStorage: localStorageUser,\n    token: tokenUser,\n    final: user\n  });\n\n  // Debug: Log user data structure for migrated users (simplified)\n  if (user && !fullUserData) {\n    console.log('🔍 Loading user data for:', user.userId);\n  }\n  const navigate = useNavigate();\n  const [rankingData, setRankingData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [currentUserRank, setCurrentUserRank] = useState(null);\n  const [viewMode, setViewMode] = useState('global');\n  const [showStats, setShowStats] = useState(true);\n  const [animationPhase, setAnimationPhase] = useState(0);\n  const [motivationalQuote, setMotivationalQuote] = useState('');\n\n  const [currentUserLeague, setCurrentUserLeague] = useState(null);\n  const [leagueUsers, setLeagueUsers] = useState([]);\n  const [showLeagueView, setShowLeagueView] = useState(false);\n  const [selectedLeague, setSelectedLeague] = useState(null);\n  const [leagueGroups, setLeagueGroups] = useState({});\n  const [userHasBeenShown, setUserHasBeenShown] = useState(false);\n  const [autoScrollCompleted, setAutoScrollCompleted] = useState(false);\n\n  // Refs for league sections\n  const leagueRefs = useRef({});\n  const headerRef = useRef(null);\n  const currentUserRef = useRef(null);\n  const podiumUserRef = useRef(null);\n  const listUserRef = useRef(null);\n\n  // Motivational quotes for different performance levels\n  const motivationalQuotes = [\n    \"🚀 Every expert was once a beginner. Keep climbing!\",\n    \"⭐ Your potential is endless. Show them what you're made of!\",\n    \"🔥 Champions are made in the moments when nobody's watching.\",\n    \"💎 Pressure makes diamonds. You're becoming brilliant!\",\n    \"🎯 Success is not final, failure is not fatal. Keep going!\",\n    \"⚡ The only impossible journey is the one you never begin.\",\n    \"🌟 Believe in yourself and all that you are capable of!\",\n    \"🏆 Greatness is not about being better than others, it's about being better than yesterday.\",\n    \"💪 Your only limit is your mind. Break through it!\",\n    \"🎨 Paint your success with the colors of determination!\"\n  ];\n\n  // Enhanced League System with Duolingo-style progression\n  const leagueSystem = {\n    mythic: {\n      min: 50000,\n      color: 'from-purple-300 via-pink-300 via-red-300 to-orange-300',\n      bgColor: 'bg-gradient-to-br from-purple-900/50 via-pink-900/50 to-red-900/50',\n      textColor: '#FFD700',\n      nameColor: '#FF1493',\n      shadowColor: 'rgba(255, 20, 147, 0.9)',\n      glow: 'shadow-pink-500/90',\n      icon: TbCrown,\n      title: 'MYTHIC',\n      description: 'Legendary Master',\n      borderColor: '#FF1493',\n      effect: 'mythic-aura',\n      leagueIcon: '👑',\n      promotionXP: 0, // Max league\n      relegationXP: 40000,\n      maxUsers: 10\n    },\n    legendary: {\n      min: 25000,\n      color: 'from-purple-400 via-indigo-400 via-blue-400 to-cyan-400',\n      bgColor: 'bg-gradient-to-br from-purple-900/40 via-indigo-900/40 to-blue-900/40',\n      textColor: '#8A2BE2',\n      nameColor: '#9370DB',\n      shadowColor: 'rgba(138, 43, 226, 0.9)',\n      glow: 'shadow-purple-500/80',\n      icon: TbDiamond,\n      title: 'LEGENDARY',\n      description: 'Elite Champion',\n      borderColor: '#8A2BE2',\n      effect: 'legendary-sparkle',\n      leagueIcon: '💎',\n      promotionXP: 50000,\n      relegationXP: 20000,\n      maxUsers: 25\n    },\n    diamond: {\n      min: 12000,\n      color: 'from-cyan-300 via-blue-300 via-indigo-300 to-purple-300',\n      bgColor: 'bg-gradient-to-br from-cyan-900/40 via-blue-900/40 to-indigo-900/40',\n      textColor: '#00CED1',\n      nameColor: '#40E0D0',\n      shadowColor: 'rgba(0, 206, 209, 0.9)',\n      glow: 'shadow-cyan-400/80',\n      icon: TbShield,\n      title: 'DIAMOND',\n      description: 'Expert Level',\n      borderColor: '#00CED1',\n      effect: 'diamond-shine',\n      leagueIcon: '🛡️',\n      promotionXP: 25000,\n      relegationXP: 8000,\n      maxUsers: 50\n    },\n    platinum: {\n      min: 6000,\n      color: 'from-slate-300 via-gray-300 via-zinc-300 to-stone-300',\n      bgColor: 'bg-gradient-to-br from-slate-800/40 via-gray-800/40 to-zinc-800/40',\n      textColor: '#C0C0C0',\n      nameColor: '#D3D3D3',\n      shadowColor: 'rgba(192, 192, 192, 0.9)',\n      glow: 'shadow-slate-400/80',\n      icon: TbAward,\n      title: 'PLATINUM',\n      description: 'Advanced',\n      borderColor: '#C0C0C0',\n      effect: 'platinum-gleam',\n      leagueIcon: '🏆',\n      promotionXP: 12000,\n      relegationXP: 4000,\n      maxUsers: 100\n    },\n    gold: {\n      min: 3000,\n      color: 'from-yellow-300 via-amber-300 via-orange-300 to-red-300',\n      bgColor: 'bg-gradient-to-br from-yellow-900/40 via-amber-900/40 to-orange-900/40',\n      textColor: '#FFD700',\n      nameColor: '#FFA500',\n      shadowColor: 'rgba(255, 215, 0, 0.9)',\n      glow: 'shadow-yellow-400/80',\n      icon: TbTrophy,\n      title: 'GOLD',\n      description: 'Skilled',\n      borderColor: '#FFD700',\n      effect: 'gold-glow',\n      leagueIcon: '🥇',\n      promotionXP: 6000,\n      relegationXP: 2000,\n      maxUsers: 200\n    },\n    silver: {\n      min: 1500,\n      color: 'from-gray-300 via-slate-300 via-zinc-300 to-gray-300',\n      bgColor: 'bg-gradient-to-br from-gray-800/40 via-slate-800/40 to-zinc-800/40',\n      textColor: '#C0C0C0',\n      nameColor: '#B8B8B8',\n      shadowColor: 'rgba(192, 192, 192, 0.9)',\n      glow: 'shadow-gray-400/80',\n      icon: TbMedal,\n      title: 'SILVER',\n      description: 'Improving',\n      borderColor: '#C0C0C0',\n      effect: 'silver-shimmer',\n      leagueIcon: '🥈',\n      promotionXP: 3000,\n      relegationXP: 800,\n      maxUsers: 300\n    },\n    bronze: {\n      min: 500,\n      color: 'from-orange-300 via-amber-300 via-yellow-300 to-orange-300',\n      bgColor: 'bg-gradient-to-br from-orange-900/40 via-amber-900/40 to-yellow-900/40',\n      textColor: '#CD7F32',\n      nameColor: '#D2691E',\n      shadowColor: 'rgba(205, 127, 50, 0.9)',\n      glow: 'shadow-orange-400/80',\n      icon: TbStar,\n      title: 'BRONZE',\n      description: 'Learning',\n      borderColor: '#CD7F32',\n      effect: 'bronze-warm',\n      leagueIcon: '🥉',\n      promotionXP: 1500,\n      relegationXP: 200,\n      maxUsers: 500\n    },\n    rookie: {\n      min: 0,\n      color: 'from-green-300 via-emerald-300 via-teal-300 to-cyan-300',\n      bgColor: 'bg-gradient-to-br from-green-900/40 via-emerald-900/40 to-teal-900/40',\n      textColor: '#32CD32',\n      nameColor: '#90EE90',\n      shadowColor: 'rgba(50, 205, 50, 0.9)',\n      glow: 'shadow-green-400/80',\n      icon: TbRocket,\n      title: 'ROOKIE',\n      description: 'Starting Out',\n      borderColor: '#32CD32',\n      effect: 'rookie-glow',\n      leagueIcon: '🚀',\n      promotionXP: 500,\n      relegationXP: 0, // Can't be relegated from rookie\n      maxUsers: 1000\n    }\n  };\n\n  // Get user's league based on XP with enhanced progression\n  const getUserLeague = (xp) => {\n    for (const [league, config] of Object.entries(leagueSystem)) {\n      if (xp >= config.min) return { league, ...config };\n    }\n    return { league: 'rookie', ...leagueSystem.rookie };\n  };\n\n  // Group users by their leagues for better organization\n  const groupUsersByLeague = (users) => {\n    const leagues = {};\n\n    users.forEach(user => {\n      const userLeague = getUserLeague(user.totalXP);\n      if (!leagues[userLeague.league]) {\n        leagues[userLeague.league] = {\n          config: userLeague,\n          users: []\n        };\n      }\n      leagues[userLeague.league].users.push({\n        ...user,\n        tier: userLeague // Update to use league instead of tier\n      });\n    });\n\n    // Sort users within each league by XP\n    Object.keys(leagues).forEach(leagueKey => {\n      leagues[leagueKey].users.sort((a, b) => b.totalXP - a.totalXP);\n    });\n\n    return leagues;\n  };\n\n  // Get current user's league and friends in the same league\n  const getCurrentUserLeagueData = (allUsers, currentUser) => {\n    if (!currentUser) return null;\n\n    const userLeague = getUserLeague(currentUser.totalXP || 0);\n    const leagueUsers = allUsers.filter(user => {\n      const league = getUserLeague(user.totalXP);\n      return league.league === userLeague.league;\n    }).sort((a, b) => b.totalXP - a.totalXP);\n\n    return {\n      league: userLeague,\n      users: leagueUsers,\n      userRank: leagueUsers.findIndex(u => u._id === currentUser._id) + 1,\n      totalInLeague: leagueUsers.length\n    };\n  };\n\n  // Handle league selection with unique visual effect\n  const handleLeagueSelect = (leagueKey) => {\n    console.log('🎯 League selected:', leagueKey);\n\n    // Set selected league with unique visual effect\n    setSelectedLeague(leagueKey);\n    setShowLeagueView(true);\n    setLeagueUsers(leagueGroups[leagueKey]?.users || []);\n\n    // Scroll to league section with smooth animation\n    setTimeout(() => {\n      const leagueElement = document.querySelector(`[data-league=\"${leagueKey}\"]`) ||\n                           document.getElementById(`league-${leagueKey}`) ||\n                           leagueRefs.current[leagueKey];\n\n      if (leagueElement) {\n        leagueElement.scrollIntoView({\n          behavior: 'smooth',\n          block: 'center',\n          inline: 'nearest'\n        });\n\n        // Add unique visual effect - pulse animation\n        leagueElement.style.transform = 'scale(1.02)';\n        leagueElement.style.transition = 'all 0.3s ease';\n        leagueElement.style.boxShadow = '0 0 30px rgba(59, 130, 246, 0.5)';\n\n        setTimeout(() => {\n          leagueElement.style.transform = 'scale(1)';\n          leagueElement.style.boxShadow = '';\n        }, 600);\n      }\n    }, 100);\n  };\n\n  // Get ordered league keys from best to worst\n  const getOrderedLeagues = () => {\n    const leagueOrder = ['mythic', 'legendary', 'diamond', 'platinum', 'gold', 'silver', 'bronze', 'rookie'];\n    return leagueOrder.filter(league => leagueGroups[league] && leagueGroups[league].users.length > 0);\n  };\n\n\n\n  // Fetch ranking data using enhanced XP system\n  const fetchRankingData = async (forceRefresh = false) => {\n    try {\n      setLoading(true);\n      console.log('🚀 Fetching enhanced XP ranking data...', forceRefresh ? '(Force Refresh)' : '');\n\n      // Try the new XP-based leaderboard first\n      try {\n        console.log('📊 Fetching XP leaderboard...');\n        const xpLeaderboardResponse = await getXPLeaderboard({\n          limit: 1000,\n          levelFilter: user?.level || 'all',\n          includeInactive: false,\n          // Add timestamp for cache busting when force refreshing\n          ...(forceRefresh && { _t: Date.now() })\n        });\n\n        console.log('✨ XP Leaderboard response:', xpLeaderboardResponse);\n\n        if (xpLeaderboardResponse && xpLeaderboardResponse.success && xpLeaderboardResponse.data) {\n          console.log('🎯 Using enhanced XP ranking data');\n\n          // Filter to only include users who have actually taken quizzes and earned XP\n          const filteredData = xpLeaderboardResponse.data.filter(userData =>\n            (userData.totalXP && userData.totalXP > 0) ||\n            (userData.totalQuizzesTaken && userData.totalQuizzesTaken > 0)\n          );\n\n          // Debug: Check first few users' profile data\n          console.log('🔍 First 3 users profile data:', filteredData.slice(0, 3).map(u => ({\n            _id: u._id,\n            name: u.name,\n            profileImage: u.profileImage,\n            profilePicture: u.profilePicture,\n            hasProfileData: !!(u.profileImage || u.profilePicture)\n          })));\n\n          const transformedData = filteredData.map((userData, index) => ({\n            _id: userData._id,\n            name: userData.name || 'Anonymous Champion',\n            email: userData.email || '',\n            class: userData.class || '',\n            level: userData.level || '',\n            profilePicture: userData.profileImage || userData.profilePicture || '',\n            profileImage: userData.profileImage || userData.profilePicture || '',\n            totalXP: userData.totalXP || 0,\n            totalQuizzesTaken: userData.totalQuizzesTaken || 0,\n            averageScore: userData.averageScore || 0,\n            currentStreak: userData.currentStreak || 0,\n            bestStreak: userData.bestStreak || 0,\n            subscriptionStatus: userData.subscriptionStatus || 'free',\n            rank: index + 1,\n            tier: getUserLeague(userData.totalXP || 0),\n            isRealUser: true,\n            rankingScore: userData.rankingScore || 0,\n            // Enhanced XP data\n            currentLevel: userData.currentLevel || 1,\n            xpToNextLevel: userData.xpToNextLevel || 100,\n            lifetimeXP: userData.lifetimeXP || 0,\n            seasonXP: userData.seasonXP || 0,\n            achievements: userData.achievements || [],\n            dataSource: 'enhanced_xp'\n          }));\n\n          // Debug: Check final transformed data for top 3 users\n          console.log('🏆 Top 3 transformed users:', transformedData.slice(0, 3).map(u => ({\n            _id: u._id,\n            name: u.name,\n            profileImage: u.profileImage,\n            profilePicture: u.profilePicture,\n            hasProfileData: !!(u.profileImage || u.profilePicture)\n          })));\n\n          setRankingData(transformedData);\n\n          // Find current user's rank\n          const userRankIndex = transformedData.findIndex(item => item._id === user?._id);\n          setCurrentUserRank(userRankIndex >= 0 ? userRankIndex + 1 : null);\n\n          // Set up league data for current user\n          if (user) {\n            const userLeagueData = getCurrentUserLeagueData(transformedData, user);\n            setCurrentUserLeague(userLeagueData);\n            setLeagueUsers(userLeagueData?.users || []);\n          }\n\n          // Group all users by their leagues\n          const grouped = groupUsersByLeague(transformedData);\n          setLeagueGroups(grouped);\n\n          setLoading(false);\n          return;\n        }\n      } catch (xpError) {\n        console.log('⚠️ XP leaderboard failed, trying fallback:', xpError);\n      }\n\n      // Fallback to legacy system if XP leaderboard fails\n      console.log('🔄 Falling back to legacy ranking system...');\n\n      let rankingResponse, usersResponse;\n\n      try {\n        console.log('📊 Fetching legacy ranking reports...');\n        rankingResponse = await getAllReportsForRanking();\n        console.log('👥 Fetching all users...');\n        usersResponse = await getAllUsers();\n      } catch (error) {\n        console.log('⚡ Error fetching legacy data:', error);\n        try {\n          usersResponse = await getAllUsers();\n        } catch (userError) {\n          console.log('❌ Failed to fetch users:', userError);\n        }\n      }\n\n      let transformedData = [];\n\n      if (usersResponse && usersResponse.success && usersResponse.data) {\n        console.log('🔄 Processing legacy user data...');\n\n        // Create a map of user reports for quick lookup\n        const userReportsMap = {};\n        if (rankingResponse && rankingResponse.success && rankingResponse.data) {\n          rankingResponse.data.forEach(item => {\n            const userId = item.user?._id || item.userId;\n            if (userId) {\n              userReportsMap[userId] = item.reports || [];\n            }\n          });\n        }\n\n        transformedData = usersResponse.data\n          .filter(userData => userData && userData._id) // Filter out invalid users only (admins included for testing)\n          .map((userData, index) => {\n            // Get reports for this user\n            const userReports = userReportsMap[userData._id] || [];\n\n            // Use existing user data or calculate from reports\n            let totalQuizzes = userReports.length || userData.totalQuizzesTaken || 0;\n            let totalScore = userReports.reduce((sum, report) => sum + (report.score || 0), 0);\n            let averageScore = totalQuizzes > 0 ? Math.round(totalScore / totalQuizzes) : userData.averageScore || 0;\n\n            // For existing users with old data, make intelligent assumptions\n            if (!userReports.length && userData.totalPoints) {\n              // Assume higher points = more exams and better performance\n              const estimatedQuizzes = Math.max(1, Math.floor(userData.totalPoints / 100)); // Assume ~100 points per quiz\n              const estimatedAverage = Math.min(95, Math.max(60, 60 + (userData.totalPoints / estimatedQuizzes / 10))); // Scale average based on points\n\n              totalQuizzes = estimatedQuizzes;\n              averageScore = Math.round(estimatedAverage);\n              totalScore = Math.round(averageScore * totalQuizzes);\n\n              console.log(`📊 Estimated stats for ${userData.name}: ${estimatedQuizzes} quizzes, ${estimatedAverage}% avg from ${userData.totalPoints} points`);\n            }\n\n            // Calculate XP based on performance (enhanced calculation)\n            let totalXP = userData.totalXP || 0;\n\n            if (!totalXP) {\n              // Calculate XP from available data\n              if (userData.totalPoints) {\n                // Use existing points as base XP with bonuses\n                totalXP = Math.floor(\n                  userData.totalPoints + // Base points\n                  (totalQuizzes * 25) + // Participation bonus\n                  (averageScore > 80 ? totalQuizzes * 15 : 0) + // Excellence bonus\n                  (averageScore > 90 ? totalQuizzes * 10 : 0) // Mastery bonus\n                );\n              } else if (totalQuizzes > 0) {\n                // Calculate from quiz performance\n                totalXP = Math.floor(\n                  (averageScore * totalQuizzes * 8) + // Base XP from scores\n                  (totalQuizzes * 40) + // Participation bonus\n                  (averageScore > 80 ? totalQuizzes * 20 : 0) // Excellence bonus\n                );\n              }\n            }\n\n            // Calculate streaks (enhanced logic)\n            let currentStreak = userData.currentStreak || 0;\n            let bestStreak = userData.bestStreak || 0;\n\n            if (userReports.length > 0) {\n              // Calculate from actual reports\n              let tempStreak = 0;\n              userReports.forEach(report => {\n                if (report.score >= 60) { // Passing score\n                  tempStreak++;\n                  bestStreak = Math.max(bestStreak, tempStreak);\n                } else {\n                  tempStreak = 0;\n                }\n              });\n              currentStreak = tempStreak;\n            } else if (userData.totalPoints && !currentStreak) {\n              // Estimate streaks from points (higher points = likely better streaks)\n              const pointsPerQuiz = totalQuizzes > 0 ? userData.totalPoints / totalQuizzes : 0;\n              if (pointsPerQuiz > 80) {\n                currentStreak = Math.min(totalQuizzes, Math.floor(pointsPerQuiz / 20)); // Estimate current streak\n                bestStreak = Math.max(currentStreak, Math.floor(pointsPerQuiz / 15)); // Estimate best streak\n              }\n            }\n\n            return {\n              _id: userData._id,\n              name: userData.name || 'Anonymous Champion',\n              email: userData.email || '',\n              class: userData.class || '',\n              level: userData.level || '',\n              profilePicture: userData.profileImage || userData.profilePicture || '',\n              profileImage: userData.profileImage || userData.profilePicture || '',\n              totalXP: totalXP,\n              totalQuizzesTaken: totalQuizzes,\n              averageScore: averageScore,\n              currentStreak: currentStreak,\n              bestStreak: bestStreak,\n              subscriptionStatus: userData.subscriptionStatus || 'free',\n              rank: index + 1,\n              tier: getUserLeague(totalXP),\n              isRealUser: true,\n              // Additional tracking fields for future updates\n              originalPoints: userData.totalPoints || 0,\n              hasReports: userReports.length > 0,\n              dataSource: userReports.length > 0 ? 'reports' : userData.totalPoints ? 'legacy_points' : 'estimated'\n            };\n          });\n\n        // Sort by XP descending\n        transformedData.sort((a, b) => b.totalXP - a.totalXP);\n        \n        // Update ranks after sorting\n        transformedData.forEach((user, index) => {\n          user.rank = index + 1;\n        });\n\n        setRankingData(transformedData);\n        \n        // Find current user's rank with multiple matching strategies\n        let userRank = -1;\n        if (user) {\n          // Try exact ID match first\n          userRank = transformedData.findIndex(item => item._id === user._id);\n\n          // If not found, try string comparison (in case of type differences)\n          if (userRank === -1) {\n            userRank = transformedData.findIndex(item => String(item._id) === String(user._id));\n          }\n\n          // If still not found, try matching by name (as fallback)\n          if (userRank === -1 && user.name) {\n            userRank = transformedData.findIndex(item => item.name === user.name);\n          }\n        }\n\n        setCurrentUserRank(userRank >= 0 ? userRank + 1 : null);\n\n        // Set up league data for current user\n        if (user) {\n          const userLeagueData = getCurrentUserLeagueData(transformedData, user);\n          setCurrentUserLeague(userLeagueData);\n          setLeagueUsers(userLeagueData?.users || []);\n        }\n\n        // Group all users by their leagues\n        const grouped = groupUsersByLeague(transformedData);\n        setLeagueGroups(grouped);\n\n        // Enhanced debug logging for user ranking (development only)\n        if (process.env.NODE_ENV === 'development') {\n          console.log('🔍 Enhanced User ranking debug:', {\n            currentUser: user?.name,\n            userId: user?._id,\n            userIdType: typeof user?._id,\n            isAdmin: user?.role === 'admin' || user?.isAdmin,\n            userXP: user?.totalXP,\n            userRankIndex: userRank,\n            userRankPosition: userRank >= 0 ? userRank + 1 : null,\n            totalRankedUsers: transformedData.length,\n            firstFewUserIds: transformedData.slice(0, 5).map(u => ({ id: u._id, type: typeof u._id, name: u.name })),\n            exactMatch: transformedData.find(item => item._id === user?._id),\n            stringMatch: transformedData.find(item => String(item._id) === String(user?._id)),\n            nameMatch: transformedData.find(item => item.name === user?.name)\n          });\n        }\n\n        // Log data sources for transparency\n        const dataSources = {\n          reports: transformedData.filter(u => u.dataSource === 'reports').length,\n          legacy_points: transformedData.filter(u => u.dataSource === 'legacy_points').length,\n          estimated: transformedData.filter(u => u.dataSource === 'estimated').length\n        };\n\n        console.log('🎉 Amazing ranking data loaded!', transformedData.length, 'real champions');\n        console.log('📊 Data sources:', dataSources);\n        console.log('🏆 Top 5 champions:', transformedData.slice(0, 5).map(u => ({\n          name: u.name,\n          xp: u.totalXP,\n          quizzes: u.totalQuizzesTaken,\n          avg: u.averageScore,\n          source: u.dataSource\n        })));\n      } else {\n        console.log('⚠️ No user data available');\n        setRankingData([]);\n        setCurrentUserRank(null);\n        message.warning('No ranking data available. Please check your connection.');\n      }\n    } catch (error) {\n      console.error('💥 Error fetching ranking data:', error);\n      message.error('Failed to load the leaderboard. But champions never give up!');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch full user data\n  const fetchFullUserData = async () => {\n    if (!user?.userId) {\n      console.log('❌ No userId available:', user);\n      return;\n    }\n\n    try {\n      console.log('🔍 Fetching full user data for userId:', user.userId);\n      const response = await getAllUsers();\n      console.log('📋 getAllUsers response:', response);\n\n      if (response.success) {\n        console.log('📊 Total users found:', response.data.length);\n        console.log('🔍 Looking for userId:', user.userId);\n        console.log('📝 First 5 user IDs:', response.data.slice(0, 5).map(u => ({ id: u._id, name: u.name })));\n\n        const userData = response.data.find(u => String(u._id) === String(user.userId));\n        if (userData) {\n          console.log('✅ Found full user data:', userData);\n          // Ensure profile picture properties are set\n          const userDataWithProfile = {\n            ...userData,\n            profilePicture: userData.profileImage || userData.profilePicture || '',\n            profileImage: userData.profileImage || userData.profilePicture || ''\n          };\n          setFullUserData(userDataWithProfile);\n        } else {\n          console.log('❌ User not found in users list');\n          console.log('🔍 Trying alternative search methods...');\n\n          // Try different ID formats\n          const userDataAlt = response.data.find(u =>\n            u._id === user.userId ||\n            u.id === user.userId ||\n            String(u._id).includes(user.userId) ||\n            String(user.userId).includes(u._id)\n          );\n\n          if (userDataAlt) {\n            console.log('✅ Found user with alternative method:', userDataAlt);\n            // Ensure profile picture properties are set\n            const userDataWithProfile = {\n              ...userDataAlt,\n              profilePicture: userDataAlt.profileImage || userDataAlt.profilePicture || '',\n              profileImage: userDataAlt.profileImage || userDataAlt.profilePicture || ''\n            };\n            setFullUserData(userDataWithProfile);\n          } else {\n            console.log('❌ User not found with any method');\n          }\n        }\n      } else {\n        console.log('❌ getAllUsers failed:', response);\n      }\n    } catch (error) {\n      console.error('❌ Error fetching user data:', error);\n    }\n  };\n\n  // Try to find user in ranking data as fallback\n  useEffect(() => {\n    if (!fullUserData && user?.userId && rankingData.length > 0) {\n      console.log('🔍 Trying to find user in ranking data...');\n      const userInRanking = rankingData.find(u => String(u._id) === String(user.userId));\n      if (userInRanking) {\n        console.log('✅ Found user in ranking data:', userInRanking);\n        // Ensure profile picture properties are set\n        const userDataWithProfile = {\n          ...userInRanking,\n          profilePicture: userInRanking.profileImage || userInRanking.profilePicture || '',\n          profileImage: userInRanking.profileImage || userInRanking.profilePicture || ''\n        };\n        setFullUserData(userDataWithProfile);\n      } else {\n        console.log('❌ User not found in ranking data either');\n      }\n    }\n  }, [rankingData, user, fullUserData]);\n\n  // Initialize component\n  useEffect(() => {\n    fetchRankingData();\n    fetchFullUserData(); // Fetch full user data\n\n    // Set random motivational quote\n    const randomQuote = motivationalQuotes[Math.floor(Math.random() * motivationalQuotes.length)];\n    setMotivationalQuote(randomQuote);\n\n    // Start animation sequence\n    const animationTimer = setInterval(() => {\n      setAnimationPhase(prev => (prev + 1) % 4);\n    }, 3000);\n\n    // Auto-refresh disabled to prevent interference with Find Me functionality\n    // const refreshTimer = setInterval(() => {\n    //   console.log('🔄 Auto-refreshing ranking data...');\n    //   fetchRankingData();\n    // }, 30000);\n\n    // Refresh when user comes back from quiz (window focus)\n    const handleWindowFocus = () => {\n      console.log('🎯 Window focused - refreshing ranking data...');\n      fetchRankingData(true); // Force refresh when returning from quiz\n    };\n\n    // Listen for real-time ranking updates from quiz completion\n    const handleRankingUpdate = (event) => {\n      console.log('🚀 Real-time ranking update triggered:', event.detail);\n\n      // Clear any cached data to ensure fresh fetch\n      localStorage.removeItem('rankingCache');\n      localStorage.removeItem('userRankingPosition');\n      localStorage.removeItem('leaderboardData');\n\n      // Immediate refresh after quiz completion with multiple attempts\n      const refreshWithRetry = async (attempts = 3) => {\n        for (let i = 0; i < attempts; i++) {\n          try {\n            console.log(`🔄 Refreshing ranking data (attempt ${i + 1}/${attempts})`);\n            await fetchRankingData(true); // Force refresh to bypass cache\n\n            // Verify the XP was updated by checking if user's XP matches the event data\n            if (event.detail?.newTotalXP && user) {\n              const updatedUser = rankingData.find(u => String(u._id) === String(user._id));\n              if (updatedUser && updatedUser.totalXP >= event.detail.newTotalXP) {\n                console.log('✅ XP update confirmed in ranking data');\n                break;\n              }\n            }\n\n            // Wait before retry\n            if (i < attempts - 1) {\n              await new Promise(resolve => setTimeout(resolve, 1500));\n            }\n          } catch (error) {\n            console.error(`❌ Ranking refresh attempt ${i + 1} failed:`, error);\n            if (i < attempts - 1) {\n              await new Promise(resolve => setTimeout(resolve, 1500));\n            }\n          }\n        }\n      };\n\n      // Start refresh with delay to ensure server processing\n      setTimeout(() => {\n        refreshWithRetry();\n      }, 1000);\n    };\n\n    window.addEventListener('focus', handleWindowFocus);\n    window.addEventListener('rankingUpdate', handleRankingUpdate);\n\n    return () => {\n      clearInterval(animationTimer);\n      // clearInterval(refreshTimer); // Commented out since refreshTimer is disabled\n      window.removeEventListener('focus', handleWindowFocus);\n      window.removeEventListener('rankingUpdate', handleRankingUpdate);\n    };\n  }, []);\n\n  // Auto-select user's current league when data loads\n  useEffect(() => {\n    if (user && leagueGroups && Object.keys(leagueGroups).length > 0 && !selectedLeague) {\n      // Find user's current league\n      for (const [leagueKey, leagueData] of Object.entries(leagueGroups)) {\n        const userInLeague = leagueData.users.find(u => String(u._id) === String(user._id));\n        if (userInLeague) {\n          console.log('🎯 Auto-selecting user league:', leagueKey);\n          setSelectedLeague(leagueKey);\n          setShowLeagueView(true);\n          setLeagueUsers(leagueData.users);\n          break;\n        }\n      }\n    }\n  }, [user, leagueGroups, selectedLeague]);\n\n  // Get top performers for special display (no filtering)\n  const topPerformers = rankingData.slice(0, 3);\n  const otherPerformers = rankingData.slice(3);\n\n\n\n  // Get user's current league information\n  const getUserLeagueInfo = () => {\n    if (!user?._id) return null;\n\n    // Check if user is in top 3 (podium)\n    const isInPodium = topPerformers.some(performer => String(performer._id) === String(user._id));\n    if (isInPodium) {\n      const podiumPosition = topPerformers.findIndex(performer => String(performer._id) === String(user._id)) + 1;\n      return {\n        type: 'podium',\n        position: podiumPosition,\n        league: 'Champion Podium',\n        leagueKey: 'podium'\n      };\n    }\n\n    // Find user's league\n    for (const [leagueKey, leagueData] of Object.entries(leagueGroups)) {\n      const userInLeague = leagueData.users?.find(u => String(u._id) === String(user._id));\n      if (userInLeague) {\n        const position = leagueData.users.findIndex(u => String(u._id) === String(user._id)) + 1;\n        return {\n          type: 'league',\n          position: position,\n          league: leagueData.title,\n          leagueKey: leagueKey,\n          totalUsers: leagueData.users.length\n        };\n      }\n    }\n\n    return null;\n  };\n\n  const userLeagueInfo = getUserLeagueInfo();\n\n  // Helper function to check if a user is the current user\n  const isCurrentUser = (userId) => {\n    return user && String(userId) === String(user._id);\n  };\n\n  // Helper function to check if user should be highlighted (only before they've been shown)\n  const shouldHighlightUser = (userId) => {\n    return isCurrentUser(userId) && !userHasBeenShown;\n  };\n\n  // Allow users to click anywhere to disable highlighting\n  const handlePageClick = () => {\n    if (!userHasBeenShown) {\n      setUserHasBeenShown(true);\n      console.log('👆 User clicked - highlighting disabled');\n    }\n  };\n\n  // Reset highlighting when user or league changes\n  useEffect(() => {\n    setUserHasBeenShown(false);\n    setAutoScrollCompleted(false); // Reset auto-scroll state\n  }, [user?._id, selectedLeague]);\n\n  // Auto-scroll to user position ONLY on first visit\n  useEffect(() => {\n    console.log('🔄 Auto-scroll check:', {\n      userId: user?._id,\n      autoScrollCompleted,\n      rankingDataLength: rankingData.length\n    });\n\n    // Only scroll if user exists, hasn't been scrolled yet, and we have data\n    if (!user?._id || autoScrollCompleted || rankingData.length === 0) {\n      console.log('❌ Auto-scroll skipped:', {\n        hasUser: !!user?._id,\n        completed: autoScrollCompleted,\n        hasData: rankingData.length > 0\n      });\n      return;\n    }\n\n    const scrollToUser = () => {\n      console.log('🎯 Starting auto-scroll for user:', user._id);\n\n      // First, try to find user in any ranking data\n      const userInRanking = rankingData.find(u => String(u._id) === String(user._id));\n      if (!userInRanking) {\n        console.log('❌ User not found in ranking data');\n        setAutoScrollCompleted(true); // Mark as completed even if not found\n        return;\n      }\n\n      console.log('✅ User found in ranking at position:', userInRanking.rank);\n\n      // Check if user is in top 3 (podium)\n      const isInPodium = userInRanking.rank <= 3;\n      console.log('🏆 Is user in podium?', isInPodium);\n\n      if (isInPodium) {\n        // Scroll to podium section\n        console.log('📍 Scrolling to podium section...');\n        const podiumSection = document.querySelector('[data-section=\"podium\"]');\n        console.log('🎪 Podium section found:', !!podiumSection);\n        if (podiumSection) {\n          setTimeout(() => {\n            podiumSection.scrollIntoView({\n              behavior: 'smooth',\n              block: 'center',\n              inline: 'nearest'\n            });\n            console.log('✅ Scrolled to podium');\n            // Mark as completed after scroll\n            setTimeout(() => {\n              setUserHasBeenShown(true);\n              setAutoScrollCompleted(true);\n              console.log('✅ Auto-scroll completed');\n            }, 1000);\n          }, 500);\n        } else {\n          setAutoScrollCompleted(true);\n        }\n      } else {\n        // Look for user element in the ranking list\n        console.log('📍 Looking for user element with ID:', user._id);\n        const userElement = document.querySelector(`[data-user-id=\"${user._id}\"]`);\n        console.log('🎯 User element found:', !!userElement);\n        if (userElement) {\n          setTimeout(() => {\n            userElement.scrollIntoView({\n              behavior: 'smooth',\n              block: 'center',\n              inline: 'nearest'\n            });\n            console.log('✅ Scrolled to user position');\n            // Mark as completed after scroll\n            setTimeout(() => {\n              setUserHasBeenShown(true);\n              setAutoScrollCompleted(true);\n              console.log('✅ Auto-scroll completed');\n            }, 1000);\n          }, 500);\n        } else {\n          console.log('❌ User element not found in DOM');\n          setAutoScrollCompleted(true);\n        }\n      }\n    };\n\n    // Delay to ensure DOM is ready, but not too long\n    const timer = setTimeout(scrollToUser, 2000);\n    return () => clearTimeout(timer);\n  }, [user?._id, rankingData, autoScrollCompleted]);\n\n  // Get subscription status badge - simplified to only ACTIVATED and EXPIRED\n  const getSubscriptionBadge = (subscriptionStatus, subscriptionEndDate, subscriptionPlan, activePlanTitle, userIndex = 0) => {\n    const now = new Date();\n    const endDate = subscriptionEndDate ? new Date(subscriptionEndDate) : null;\n\n    console.log('Subscription Debug:', {\n      subscriptionStatus,\n      subscriptionEndDate,\n      subscriptionPlan,\n      activePlanTitle,\n      endDate,\n      now,\n      isActive: endDate && endDate > now,\n      userIndex\n    });\n\n    // Check if user has an active subscription\n    if (subscriptionStatus === 'active' || subscriptionStatus === 'premium') {\n      // Check if subscription is still valid (not expired)\n      if (!endDate || endDate > now) {\n        // User has active plan - show ACTIVATED\n        return {\n          text: 'ACTIVATED',\n          color: '#10B981', // Green\n          bgColor: 'rgba(16, 185, 129, 0.2)',\n          borderColor: '#10B981'\n        };\n      } else {\n        // Subscription status is active but end date has passed - show EXPIRED\n        return {\n          text: 'EXPIRED',\n          color: '#EF4444', // Red\n          bgColor: 'rgba(239, 68, 68, 0.2)',\n          borderColor: '#EF4444'\n        };\n      }\n    } else {\n      // No active subscription - show EXPIRED\n      return {\n        text: 'EXPIRED',\n        color: '#EF4444', // Red\n        bgColor: 'rgba(239, 68, 68, 0.2)',\n        borderColor: '#EF4444'\n      };\n    }\n  };\n\n  // Skeleton loading component\n  const RankingSkeleton = () => (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\">\n      <div className=\"container mx-auto px-4 py-8\">\n        {/* Header Skeleton */}\n        <div className=\"text-center mb-12\">\n          <div className=\"h-12 bg-white/10 rounded-lg w-96 mx-auto mb-4 animate-pulse\"></div>\n          <div className=\"h-6 bg-white/5 rounded w-64 mx-auto animate-pulse\"></div>\n        </div>\n\n        {/* Podium Skeleton */}\n        <div className=\"flex justify-center items-end mb-16 space-x-8\">\n          {[2, 1, 3].map((position) => (\n            <div key={position} className={`text-center ${position === 1 ? 'order-2' : position === 2 ? 'order-1' : 'order-3'}`}>\n              <div className={`w-20 h-20 bg-gradient-to-br from-yellow-400/20 to-yellow-600/20 rounded-full mx-auto mb-4 animate-pulse`}></div>\n              <div className=\"h-4 bg-white/10 rounded w-16 mx-auto mb-2 animate-pulse\"></div>\n              <div className=\"h-3 bg-white/5 rounded w-12 mx-auto animate-pulse\"></div>\n            </div>\n          ))}\n        </div>\n\n        {/* List Skeleton */}\n        <div className=\"space-y-4 max-w-4xl mx-auto\">\n          {[...Array(8)].map((_, i) => (\n            <div key={i} className=\"bg-white/5 rounded-xl p-4 animate-pulse\">\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"w-12 h-12 bg-white/10 rounded-full\"></div>\n                <div className=\"flex-1\">\n                  <div className=\"h-4 bg-white/10 rounded w-32 mb-2\"></div>\n                  <div className=\"h-3 bg-white/5 rounded w-24\"></div>\n                </div>\n                <div className=\"h-6 bg-white/10 rounded w-16\"></div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n\n  // Show skeleton only on initial load\n  if (loading && rankingData.length === 0) {\n    return <RankingSkeleton />;\n  }\n\n  return (\n    <>\n      <style>{`\n        /* Dark background for better color visibility */\n        body {\n          background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%) !important;\n          min-height: 100vh;\n          color: #ffffff !important;\n        }\n\n        .ranking-page-container {\n          background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);\n          min-height: 100vh;\n          color: #ffffff;\n        }\n\n        /* Fix black text visibility - Enhanced */\n        .ranking-page-container * {\n          color: inherit;\n        }\n\n        .ranking-page-container .text-black,\n        .ranking-page-container .text-gray-900,\n        .ranking-page-container h1,\n        .ranking-page-container h2,\n        .ranking-page-container h3,\n        .ranking-page-container h4,\n        .ranking-page-container h5,\n        .ranking-page-container h6,\n        .ranking-page-container p,\n        .ranking-page-container span,\n        .ranking-page-container div {\n          color: #ffffff !important;\n        }\n\n        .ranking-page-container [style*=\"color: #000000\"],\n        .ranking-page-container [style*=\"color: black\"],\n        .ranking-page-container [style*=\"color:#000000\"],\n        .ranking-page-container [style*=\"color:black\"],\n        .ranking-page-container [style*=\"color: #1f2937\"],\n        .ranking-page-container [style*=\"color:#1f2937\"] {\n          color: #ffffff !important;\n        }\n\n        /* Force white text for names and content */\n        .ranking-page-container .font-bold,\n        .ranking-page-container .font-black,\n        .ranking-page-container .font-semibold,\n        .ranking-page-container .font-medium {\n          color: #ffffff !important;\n        }\n\n\n        /* Enhanced hover effects for ranking cards */\n        .ranking-card {\n          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n        }\n\n        .ranking-card:hover {\n          transform: translateY(-2px) scale(1.01);\n        }\n\n        /* Smooth animations for league badges */\n        .league-badge {\n          transition: all 0.2s ease-in-out;\n        }\n\n        .league-badge:hover {\n          transform: scale(1.05);\n        }\n\n        /* Gradient text animations */\n        @keyframes gradientShift {\n          0%, 100% { background-position: 0% 50%; }\n          50% { background-position: 100% 50%; }\n        }\n\n        .animated-gradient {\n          background-size: 200% 200%;\n          animation: gradientShift 3s ease infinite;\n        }\n\n        /* League-specific animations */\n        .mythic-aura {\n          animation: mythicPulse 2s ease-in-out infinite alternate;\n        }\n\n        .legendary-sparkle {\n          animation: legendarySparkle 3s ease-in-out infinite;\n        }\n\n        .diamond-shine {\n          animation: diamondShine 2.5s ease-in-out infinite;\n        }\n\n        .platinum-gleam {\n          animation: platinumGleam 3s ease-in-out infinite;\n        }\n\n        .gold-glow {\n          animation: goldGlow 2s ease-in-out infinite alternate;\n        }\n\n        .silver-shimmer {\n          animation: silverShimmer 2.5s ease-in-out infinite;\n        }\n\n        .bronze-warm {\n          animation: bronzeWarm 3s ease-in-out infinite;\n        }\n\n        .rookie-glow {\n          animation: rookieGlow 2s ease-in-out infinite alternate;\n        }\n\n        @keyframes mythicPulse {\n          0% { box-shadow: 0 0 20px rgba(255, 20, 147, 0.5); }\n          100% { box-shadow: 0 0 40px rgba(255, 20, 147, 0.8), 0 0 60px rgba(138, 43, 226, 0.6); }\n        }\n\n        @keyframes legendarySparkle {\n          0%, 100% { filter: brightness(1) hue-rotate(0deg); }\n          50% { filter: brightness(1.2) hue-rotate(10deg); }\n        }\n\n        @keyframes diamondShine {\n          0%, 100% { filter: brightness(1) saturate(1); }\n          50% { filter: brightness(1.3) saturate(1.2); }\n        }\n\n        @keyframes platinumGleam {\n          0%, 100% { filter: brightness(1); }\n          50% { filter: brightness(1.1) contrast(1.1); }\n        }\n\n        @keyframes goldGlow {\n          0% { filter: brightness(1) drop-shadow(0 0 10px #FFD700); }\n          100% { filter: brightness(1.2) drop-shadow(0 0 20px #FFD700); }\n        }\n\n        @keyframes silverShimmer {\n          0%, 100% { filter: brightness(1); }\n          50% { filter: brightness(1.15) contrast(1.05); }\n        }\n\n        @keyframes bronzeWarm {\n          0%, 100% { filter: brightness(1) hue-rotate(0deg); }\n          50% { filter: brightness(1.1) hue-rotate(5deg); }\n        }\n\n        @keyframes rookieGlow {\n          0% { filter: brightness(1) drop-shadow(0 0 5px #32CD32); }\n          100% { filter: brightness(1.15) drop-shadow(0 0 15px #32CD32); }\n        }\n\n        /* Horizontal podium animations */\n        .podium-animation {\n          animation: podiumFloat 4s ease-in-out infinite;\n        }\n\n        @keyframes podiumFloat {\n          0%, 100% { transform: translateY(0px); }\n          50% { transform: translateY(-5px); }\n        }\n      `}</style>\n      <div className=\"ranking-page-container ranking-page min-h-screen bg-gradient-to-br from-slate-900 via-gray-900 to-black relative overflow-hidden\" onClick={handlePageClick}>\n\n      {/* Highlighting Notification */}\n      {!userHasBeenShown && user && (\n        <motion.div\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          exit={{ opacity: 0, y: -20 }}\n          className=\"fixed top-4 left-1/2 transform -translate-x-1/2 z-50 bg-yellow-500/90 backdrop-blur-sm text-black px-4 py-2 rounded-lg shadow-lg text-sm font-medium\"\n        >\n          🎯 Finding your position... Click anywhere to stop highlighting\n        </motion.div>\n      )}\n\n      {/* Animated Background Elements - Darker Theme */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute -top-40 -right-40 w-80 h-80 bg-purple-600 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-blob\"></div>\n        <div className=\"absolute -bottom-40 -left-40 w-80 h-80 bg-blue-600 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-blob animation-delay-2000\"></div>\n        <div className=\"absolute top-40 left-40 w-80 h-80 bg-indigo-600 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-blob animation-delay-4000\"></div>\n        <div className=\"absolute top-1/2 right-1/3 w-60 h-60 bg-cyan-600 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-blob animation-delay-6000\"></div>\n      </div>\n\n      {/* Floating Particles */}\n      <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n        {[...Array(20)].map((_, i) => (\n          <motion.div\n            key={i}\n            className=\"absolute w-2 h-2 bg-white rounded-full opacity-20\"\n            animate={{\n              y: [0, -100, 0],\n              x: [0, Math.random() * 100 - 50, 0],\n              opacity: [0.2, 0.8, 0.2]\n            }}\n            transition={{\n              duration: 3 + Math.random() * 2,\n              repeat: Infinity,\n              delay: Math.random() * 2\n            }}\n            style={{\n              left: `${Math.random() * 100}%`,\n              top: `${Math.random() * 100}%`\n            }}\n          />\n        ))}\n      </div>\n\n      <div className=\"relative z-10\">\n        {/* TOP CONTROLS */}\n        <motion.div\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          className=\"px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6 md:py-8 lg:py-12\"\n        >\n          <div className=\"max-w-7xl mx-auto\">\n            <div className=\"bg-white/5 backdrop-blur-lg rounded-xl sm:rounded-2xl md:rounded-3xl p-3 sm:p-4 md:p-6 lg:p-8 border border-white/10\">\n              <div className=\"flex flex-col sm:flex-row gap-2 sm:gap-3 md:gap-4 lg:gap-6 items-center justify-center\">\n\n                {/* Hub Button */}\n                <motion.button\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={() => navigate('/user/hub')}\n                  className=\"flex items-center gap-2 md:gap-3 px-4 md:px-6 py-3 md:py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-bold shadow-lg hover:shadow-xl transition-all duration-300 w-full sm:w-auto\"\n                  style={{\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  }}\n                >\n                  <TbHome className=\"w-5 h-5 md:w-6 md:h-6\" />\n                  <span>Hub</span>\n                </motion.button>\n\n                {/* User League Info Display */}\n                {userLeagueInfo && (\n                  <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    className=\"flex items-center gap-2 md:gap-3 px-4 md:px-6 py-3 md:py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl font-bold shadow-lg\"\n                    style={{\n                      background: userLeagueInfo.type === 'podium'\n                        ? 'linear-gradient(135deg, #FFD700, #FFA500)'\n                        : 'linear-gradient(135deg, #3B82F6, #8B5CF6)',\n                      color: userLeagueInfo.type === 'podium' ? '#1F2937' : '#FFFFFF',\n                      boxShadow: '0 4px 15px rgba(59, 130, 246, 0.3)',\n                      fontSize: window.innerWidth < 768 ? '0.9rem' : '1rem'\n                    }}\n                  >\n                    <TbTrophy className=\"w-5 h-5 md:w-6 md:h-6\" />\n                    <span>\n                      {userLeagueInfo.type === 'podium'\n                        ? `🏆 Podium #${userLeagueInfo.position}`\n                        : `${userLeagueInfo.league} #${userLeagueInfo.position}`}\n                    </span>\n                  </motion.div>\n                )}\n\n                {/* User Profile Window */}\n                {fullUserData && (\n                  <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    className=\"bg-gradient-to-br from-blue-600/20 via-purple-600/20 to-indigo-600/20 backdrop-blur-lg rounded-2xl p-4 border border-blue-400/30 shadow-2xl max-w-sm\"\n                  >\n                    <div className=\"flex items-center gap-4\">\n                      {/* Profile Picture with Online Status */}\n                      <div className=\"flex-shrink-0\">\n                        <ProfilePicture\n                          user={fullUserData}\n                          size=\"xl\"\n                          showOnlineStatus={true}\n                          style={{\n                            border: '3px solid #facc15',\n                            boxShadow: '0 10px 25px rgba(0,0,0,0.15)'\n                          }}\n                        />\n                      </div>\n\n                      {/* User Details */}\n                      <div className=\"flex-grow\">\n                        <h3 className=\"text-lg font-bold text-white mb-2 truncate\">\n                          {fullUserData.name || fullUserData.username || 'User'}\n                        </h3>\n\n                        {/* Stats Grid */}\n                        <div className=\"grid grid-cols-2 gap-2 text-sm\">\n                          <div className=\"bg-green-500/20 rounded-lg p-2 text-center\">\n                            <div className=\"text-green-300 text-xs\">Total XP</div>\n                            <div className=\"text-white font-bold\">\n                              {(() => {\n                                // Try multiple XP field names for migrated users\n                                const xp = fullUserData.totalXP || fullUserData.xp || fullUserData.points || fullUserData.totalPoints || 0;\n                                return xp.toLocaleString();\n                              })()}\n                            </div>\n                          </div>\n\n                          <div className=\"bg-purple-500/20 rounded-lg p-2 text-center\">\n                            <div className=\"text-purple-300 text-xs\">Rank</div>\n                            <div className=\"text-white font-bold\">\n                              {(() => {\n                                // Try to find user in ranking data\n                                const userInRanking = rankingData.find(u => String(u._id) === String(fullUserData._id));\n                                return userInRanking ? `#${userInRanking.rank}` : (currentUserRank ? `#${currentUserRank}` : 'N/A');\n                              })()}\n                            </div>\n                          </div>\n\n                          <div className=\"bg-blue-500/20 rounded-lg p-2 text-center\">\n                            <div className=\"text-blue-300 text-xs\">League</div>\n                            <div className=\"text-white font-bold text-xs\">\n                              {(() => {\n                                // Find user's league with icon - try multiple XP sources\n                                const userXP = fullUserData.totalXP || fullUserData.xp || fullUserData.points || fullUserData.totalPoints || 0;\n                                for (const [leagueKey, leagueData] of Object.entries(leagueGroups)) {\n                                  const userInLeague = leagueData.users?.find(u => String(u._id) === String(fullUserData._id));\n                                  if (userInLeague) {\n                                    const leagueInfo = getUserLeague(userXP);\n                                    return `${leagueInfo.leagueIcon} ${leagueKey.toUpperCase()}`;\n                                  }\n                                }\n                                // Fallback: calculate league from XP even if not in league data\n                                if (userXP > 0) {\n                                  const leagueInfo = getUserLeague(userXP);\n                                  return `${leagueInfo.leagueIcon} ${leagueInfo.league.toUpperCase()}`;\n                                }\n                                return '🔰 Unranked';\n                              })()}\n                            </div>\n                          </div>\n\n                          <div className=\"bg-orange-500/20 rounded-lg p-2 text-center\">\n                            <div className=\"text-orange-300 text-xs\">Quizzes</div>\n                            <div className=\"text-white font-bold\">\n                              {(() => {\n                                // Try multiple quiz count field names\n                                return fullUserData.quizzesCompleted || fullUserData.totalQuizzesTaken || fullUserData.quizzesTaken || fullUserData.totalQuizzes || 0;\n                              })()}\n                            </div>\n                          </div>\n                        </div>\n\n                        {/* Additional Stats Row */}\n                        <div className=\"grid grid-cols-3 gap-2 mt-2 text-xs\">\n                          <div className=\"bg-yellow-500/20 rounded-lg p-1.5 text-center\">\n                            <div className=\"text-yellow-300 text-xs\">Level</div>\n                            <div className=\"text-white font-bold\">\n                              {fullUserData.currentLevel || fullUserData.level || 1}\n                            </div>\n                          </div>\n\n                          <div className=\"bg-red-500/20 rounded-lg p-1.5 text-center\">\n                            <div className=\"text-red-300 text-xs\">Streak</div>\n                            <div className=\"text-white font-bold\">\n                              {fullUserData.currentStreak || fullUserData.streak || 0}\n                            </div>\n                          </div>\n\n                          <div className=\"bg-cyan-500/20 rounded-lg p-1.5 text-center\">\n                            <div className=\"text-cyan-300 text-xs\">Avg Score</div>\n                            <div className=\"text-white font-bold\">\n                              {(() => {\n                                const avgScore = fullUserData.averageScore || fullUserData.avgScore || 0;\n                                return Math.round(avgScore);\n                              })()}%\n                            </div>\n                          </div>\n                        </div>\n\n                        {/* League Position */}\n                        {(() => {\n                          // Find user's position in their league\n                          for (const [leagueKey, leagueData] of Object.entries(leagueGroups)) {\n                            const userIndex = leagueData.users?.findIndex(u => String(u._id) === String(fullUserData._id));\n                            if (userIndex !== -1 && userIndex !== undefined) {\n                              return (\n                                <div className=\"mt-2 text-center\">\n                                  <div className=\"bg-gradient-to-r from-yellow-400/20 to-orange-400/20 rounded-lg p-1.5\">\n                                    <div className=\"text-yellow-300 text-xs\">League Position</div>\n                                    <div className=\"text-white font-bold text-sm\">\n                                      #{userIndex + 1} of {leagueData.users.length}\n                                    </div>\n                                  </div>\n                                </div>\n                              );\n                            }\n                          }\n                          return null;\n                        })()}\n                      </div>\n                    </div>\n                  </motion.div>\n                )}\n\n\n\n\n\n\n\n\n\n\n\n                {/* League Selection Section */}\n                <div className=\"flex flex-col items-center gap-4 bg-white/5 backdrop-blur-lg rounded-2xl p-6 border border-white/10 max-w-4xl mx-auto\">\n                  {/* LEAGUES Title */}\n                  <motion.h3\n                    className=\"text-2xl md:text-3xl font-black mb-2\"\n                    style={{\n                      background: 'linear-gradient(45deg, #FFD700, #FFA500, #FF6B35)',\n                      WebkitBackgroundClip: 'text',\n                      WebkitTextFillColor: 'transparent',\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      filter: 'drop-shadow(0 0 10px #FFD700)'\n                    }}\n                    animate={{ scale: [1, 1.02, 1] }}\n                    transition={{ duration: 3, repeat: Infinity }}\n                  >\n                    🏆 LEAGUES 🏆\n                  </motion.h3>\n\n                  {/* League Icons */}\n                  <div className=\"flex flex-wrap items-center justify-center gap-3 md:gap-4\">\n                    {getOrderedLeagues().map((leagueKey) => {\n                      const league = leagueSystem[leagueKey];\n                      const isSelected = selectedLeague === leagueKey;\n                      const userCount = leagueGroups[leagueKey]?.users.length || 0;\n\n                      return (\n                        <motion.div\n                          key={leagueKey}\n                          className=\"flex flex-col items-center gap-2\"\n                          whileHover={{ scale: 1.05 }}\n                        >\n                          <motion.button\n                            whileHover={{ scale: 1.1, y: -3 }}\n                            whileTap={{ scale: 0.95 }}\n                            onClick={() => handleLeagueSelect(leagueKey)}\n                            className={`relative flex items-center justify-center w-16 h-16 md:w-20 md:h-20 rounded-2xl transition-all duration-300 ${\n                              isSelected\n                                ? 'ring-4 ring-yellow-400 ring-opacity-100 shadow-2xl'\n                                : 'hover:ring-2 hover:ring-white/30'\n                            }`}\n                            style={{\n                              background: isSelected\n                                ? `linear-gradient(135deg, ${league.borderColor}80, ${league.textColor}50, ${league.borderColor}80)`\n                                : `linear-gradient(135deg, ${league.borderColor}60, ${league.textColor}30)`,\n                              border: `3px solid ${isSelected ? '#FFD700' : league.borderColor + '80'}`,\n                              boxShadow: isSelected\n                                ? `0 0 30px ${league.shadowColor}80, 0 0 60px #FFD70080, 0 6px 30px ${league.shadowColor}80`\n                                : `0 4px 15px ${league.shadowColor}40`,\n                              transform: isSelected ? 'scale(1.1)' : 'scale(1)',\n                              filter: isSelected ? 'brightness(1.3) saturate(1.2)' : 'brightness(1)'\n                            }}\n                            animate={isSelected ? {\n                              boxShadow: [\n                                `0 0 30px ${league.shadowColor}80, 0 0 60px #FFD70080`,\n                                `0 0 40px ${league.shadowColor}100, 0 0 80px #FFD700A0`,\n                                `0 0 30px ${league.shadowColor}80, 0 0 60px #FFD70080`\n                              ],\n                              scale: [1.1, 1.15, 1.1]\n                            } : {}}\n                            transition={{\n                              duration: 2,\n                              repeat: isSelected ? Infinity : 0,\n                              ease: \"easeInOut\"\n                            }}\n                            title={`Click to view ${league.title} League (${userCount} users)`}\n                          >\n                            <span className=\"text-3xl md:text-4xl\">{league.leagueIcon}</span>\n                            {isSelected && (\n                              <motion.div\n                                initial={{ scale: 0, rotate: -360, opacity: 0 }}\n                                animate={{\n                                  scale: [1, 1.3, 1],\n                                  rotate: [0, 360, 720],\n                                  opacity: 1,\n                                  boxShadow: [\n                                    '0 0 15px rgba(255, 215, 0, 1), 0 0 30px rgba(255, 215, 0, 0.8)',\n                                    '0 0 25px rgba(255, 215, 0, 1), 0 0 50px rgba(255, 215, 0, 1)',\n                                    '0 0 15px rgba(255, 215, 0, 1), 0 0 30px rgba(255, 215, 0, 0.8)'\n                                  ]\n                                }}\n                                transition={{\n                                  scale: { duration: 2, repeat: Infinity, ease: \"easeInOut\" },\n                                  rotate: { duration: 4, repeat: Infinity, ease: \"linear\" },\n                                  boxShadow: { duration: 1.5, repeat: Infinity, ease: \"easeInOut\" },\n                                  opacity: { duration: 0.3 }\n                                }}\n                                className=\"absolute -top-3 -right-3 w-8 h-8 bg-gradient-to-r from-yellow-400 via-orange-400 to-yellow-500 rounded-full flex items-center justify-center border-3 border-white shadow-lg\"\n                                style={{\n                                  background: 'linear-gradient(45deg, #FFD700, #FFA500, #FFD700)',\n                                  border: '3px solid white',\n                                  zIndex: 10\n                                }}\n                              >\n                                <motion.span\n                                  className=\"text-sm font-black text-gray-900\"\n                                  animate={{\n                                    scale: [1, 1.2, 1],\n                                    rotate: [0, -10, 10, 0]\n                                  }}\n                                  transition={{\n                                    duration: 1,\n                                    repeat: Infinity,\n                                    ease: \"easeInOut\"\n                                  }}\n                                >\n                                  ✓\n                                </motion.span>\n                              </motion.div>\n                            )}\n                            <div\n                              className=\"absolute -bottom-2 -right-2 w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold border-2 border-white\"\n                              style={{\n                                background: league.borderColor,\n                                color: '#FFFFFF',\n                                fontSize: '11px'\n                              }}\n                            >\n                              {userCount}\n                            </div>\n                          </motion.button>\n\n                          {/* League Name */}\n                          <motion.div\n                            className=\"text-center\"\n                            whileHover={{ scale: 1.05 }}\n                          >\n                            <div\n                              className=\"text-xs md:text-sm font-bold px-2 py-1 rounded-lg\"\n                              style={{\n                                color: league.nameColor,\n                                textShadow: `1px 1px 2px ${league.shadowColor}`,\n                                background: `${league.borderColor}20`,\n                                border: `1px solid ${league.borderColor}40`\n                              }}\n                            >\n                              {league.title}\n                            </div>\n                          </motion.div>\n                        </motion.div>\n                      );\n                    })}\n                  </div>\n\n                  <p className=\"text-white/70 text-sm text-center mt-2\">\n                    Click any league to view its members and scroll to their section\n                  </p>\n                </div>\n\n\n\n                {/* Refresh Button */}\n                <motion.button\n                  whileHover={{ scale: 1.05, rotate: 180 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={fetchRankingData}\n                  disabled={loading}\n                  className=\"flex items-center gap-3 px-6 py-3 md:py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 w-full sm:w-auto\"\n                  style={{\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  }}\n                >\n                  <TbRefresh className={`w-5 h-5 md:w-6 md:h-6 ${loading ? 'animate-spin' : ''}`} />\n                  <span>Refresh</span>\n                </motion.button>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Admin Notice - DISABLED FOR TESTING */}\n        {false && (user?.role === 'admin' || user?.isAdmin) && (\n          <motion.div\n            initial={{ opacity: 0, y: -20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            className=\"px-3 sm:px-4 md:px-6 lg:px-8 mb-6\"\n          >\n            <div className=\"max-w-7xl mx-auto\">\n              <div className=\"bg-gradient-to-r from-purple-500/20 to-blue-500/20 backdrop-blur-lg rounded-xl p-4 border border-purple-300/30\">\n                <div className=\"flex items-center gap-3\">\n                  <div className=\"w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center\">\n                    <span className=\"text-white font-bold text-sm\">👑</span>\n                  </div>\n                  <div>\n                    <h3 className=\"font-bold text-white\">Admin View</h3>\n                    <p className=\"text-sm text-white/80\">\n                      You're viewing as an admin. Admin accounts are excluded from student rankings.\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        )}\n\n        {/* SPECTACULAR RANKING HEADER */}\n        <motion.div\n          initial={{ opacity: 0, y: -50 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 1, ease: \"easeOut\" }}\n          className=\"relative overflow-hidden mb-8\"\n        >\n          {/* Header Background with Modern Gradient */}\n          <div className=\"bg-gradient-to-br from-blue-600 via-indigo-500 via-purple-500 via-cyan-500 to-teal-500 relative\">\n            <div className=\"absolute inset-0 bg-gradient-to-t from-black/40 via-black/20 to-transparent\"></div>\n            <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent\"></div>\n\n            {/* Animated Header Content */}\n            <div className=\"relative z-10 px-3 sm:px-4 md:px-6 lg:px-8 py-6 sm:py-8 md:py-12 lg:py-20\">\n              <div className=\"max-w-7xl mx-auto text-center\">\n\n                {/* Main Title with Epic Animation */}\n                <motion.div\n                  animate={{\n                    scale: [1, 1.02, 1],\n                    rotateY: [0, 5, 0]\n                  }}\n                  transition={{\n                    duration: 4,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  }}\n                  className=\"mb-6 md:mb-8\"\n                >\n                  <h1 className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-black mb-2 md:mb-4 tracking-tight\">\n                    <motion.span\n                      animate={{\n                        backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']\n                      }}\n                      transition={{\n                        duration: 4,\n                        repeat: Infinity,\n                        ease: \"linear\"\n                      }}\n                      className=\"bg-gradient-to-r from-yellow-300 via-pink-300 via-cyan-300 via-purple-300 to-yellow-300 bg-clip-text text-transparent bg-400%\"\n                      style={{\n                        backgroundSize: '400% 400%',\n                        WebkitBackgroundClip: 'text',\n                        WebkitTextFillColor: 'transparent',\n                        filter: 'drop-shadow(2px 2px 4px rgba(0,0,0,0.8))'\n                      }}\n                    >\n                      HALL OF\n                    </motion.span>\n                    <br />\n                    <motion.span\n                      animate={{\n                        textShadow: [\n                          '0 0 20px rgba(255,215,0,0.8), 0 0 40px rgba(255,215,0,0.6)',\n                          '0 0 30px rgba(255,215,0,1), 0 0 60px rgba(255,215,0,0.8)',\n                          '0 0 20px rgba(255,215,0,0.8), 0 0 40px rgba(255,215,0,0.6)'\n                        ]\n                      }}\n                      transition={{\n                        duration: 2.5,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }}\n                      style={{\n                        color: '#FFD700',\n                        fontWeight: '900',\n                        textShadow: '3px 3px 6px rgba(0,0,0,0.9)'\n                      }}\n                    >\n                      CHAMPIONS\n                    </motion.span>\n                  </h1>\n                </motion.div>\n\n                {/* Epic Subtitle */}\n                <motion.p\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.5, duration: 0.8 }}\n                  className=\"text-sm sm:text-base md:text-lg lg:text-xl font-semibold mb-4 md:mb-6 max-w-4xl mx-auto leading-relaxed px-2\"\n                  style={{\n                    color: '#F3F4F6',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    background: 'linear-gradient(45deg, #F3F4F6, #E5E7EB)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent'\n                  }}\n                >\n                  ✨ Where legends are born and greatness is celebrated ✨\n                </motion.p>\n\n                {/* Motivational Quote */}\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 0.8, duration: 0.8 }}\n                  className=\"mb-6 md:mb-8\"\n                >\n                  <p className=\"text-sm sm:text-base md:text-lg font-medium text-yellow-200 bg-black/20 backdrop-blur-sm rounded-xl px-4 py-3 max-w-3xl mx-auto border border-yellow-400/30\"\n                     style={{\n                       textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                       fontStyle: 'italic'\n                     }}>\n                    {motivationalQuote}\n                  </p>\n                </motion.div>\n\n                {/* Enhanced Stats Grid */}\n                <motion.div\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 1, duration: 0.8 }}\n                  className=\"grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4 md:gap-6 max-w-4xl mx-auto\"\n                >\n                  {[\n                    {\n                      icon: TbUsers,\n                      value: rankingData.length,\n                      label: 'Champions',\n                      bgGradient: 'from-blue-600/20 via-indigo-600/20 to-purple-600/20',\n                      iconColor: '#60A5FA',\n                      borderColor: '#3B82F6'\n                    },\n                    {\n                      icon: TbTrophy,\n                      value: topPerformers.length,\n                      label: 'Top Performers',\n                      bgGradient: 'from-yellow-600/20 via-orange-600/20 to-red-600/20',\n                      iconColor: '#FBBF24',\n                      borderColor: '#F59E0B'\n                    },\n                    {\n                      icon: TbFlame,\n                      value: rankingData.filter(u => u.currentStreak > 0).length,\n                      label: 'Active Streaks',\n                      bgGradient: 'from-red-600/20 via-pink-600/20 to-rose-600/20',\n                      iconColor: '#F87171',\n                      borderColor: '#EF4444'\n                    },\n                    {\n                      icon: TbStar,\n                      value: rankingData.reduce((sum, u) => sum + (u.totalXP || 0), 0).toLocaleString(),\n                      label: 'Total XP',\n                      bgGradient: 'from-green-600/20 via-emerald-600/20 to-teal-600/20',\n                      iconColor: '#34D399',\n                      borderColor: '#10B981'\n                    }\n                  ].map((stat, index) => (\n                    <motion.div\n                      key={index}\n                      initial={{ opacity: 0, scale: 0.8 }}\n                      animate={{ opacity: 1, scale: 1 }}\n                      transition={{ delay: 1.2 + index * 0.1, duration: 0.6 }}\n                      whileHover={{ scale: 1.05, y: -5 }}\n                      className={`bg-gradient-to-br ${stat.bgGradient} backdrop-blur-lg rounded-xl p-3 md:p-4 text-center relative overflow-hidden`}\n                      style={{\n                        border: `2px solid ${stat.borderColor}40`,\n                        boxShadow: `0 8px 32px ${stat.borderColor}20`\n                      }}\n                    >\n                      <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"></div>\n                      <stat.icon\n                        className=\"w-6 h-6 md:w-8 md:h-8 mx-auto mb-2 relative z-10\"\n                        style={{ color: stat.iconColor, filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.5))' }}\n                      />\n                      <div\n                        className=\"text-lg sm:text-xl md:text-2xl lg:text-3xl font-black mb-1 relative z-10\"\n                        style={{\n                          color: stat.iconColor,\n                          textShadow: `3px 3px 6px rgba(0,0,0,0.9)`,\n                          filter: 'drop-shadow(0 0 10px currentColor)',\n                          fontSize: 'clamp(1rem, 4vw, 2.5rem)'\n                        }}\n                      >\n                        {stat.value}\n                      </div>\n                      <div\n                        className=\"text-xs sm:text-sm font-bold relative z-10\"\n                        style={{\n                          color: '#FFFFFF',\n                          textShadow: '2px 2px 4px rgba(0,0,0,0.9)',\n                          fontSize: 'clamp(0.75rem, 2vw, 1rem)'\n                        }}\n                      >\n                        {stat.label}\n                      </div>\n                    </motion.div>\n                  ))}\n                </motion.div>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* LOADING STATE */}\n        {loading && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            className=\"flex flex-col items-center justify-center py-20\"\n          >\n            <motion.div\n              animate={{ rotate: 360 }}\n              transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n              className=\"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4\"\n            />\n            <p className=\"text-white/80 text-lg font-medium\">Loading champions...</p>\n          </motion.div>\n        )}\n\n        {/* EPIC LEADERBOARD */}\n        {!loading && (\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.3, duration: 0.8 }}\n            className=\"px-4 sm:px-6 md:px-8 lg:px-12 pb-20 md:pb-24 lg:pb-32\"\n          >\n            <div className=\"max-w-7xl mx-auto\">\n\n              {/* TOP 3 PODIUM */}\n              {topPerformers.length > 0 && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 0.5, duration: 0.8 }}\n                  className=\"mb-12\"\n                >\n                  <h2 className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-black text-center mb-6 md:mb-8 lg:mb-12 px-4\" style={{\n                    background: 'linear-gradient(45deg, #FFD700, #FFA500, #FF6B35)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                    filter: 'drop-shadow(0 0 15px #FFD700)'\n                  }}>\n                    🏆 CHAMPIONS PODIUM 🏆\n                  </h2>\n\n                  {/* Horizontal Podium Layout with Moving Animations */}\n                  <div className=\"flex items-end justify-center gap-4 sm:gap-6 md:gap-8 max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 mb-8\">\n                    {/* Second Place - Left */}\n                    {topPerformers[1] && (\n                      <motion.div\n                        key={`second-${topPerformers[1]._id}`}\n                        ref={user && String(topPerformers[1]._id) === String(user._id) ? podiumUserRef : null}\n                        data-user-id={topPerformers[1]._id}\n                        data-user-rank={2}\n                        initial={{ opacity: 0, x: -100, y: 50 }}\n                        animate={{\n                          opacity: 1,\n                          x: 0,\n                          y: 0,\n                          scale: [1, 1.02, 1],\n                          rotateY: [0, 5, 0]\n                        }}\n                        transition={{\n                          delay: 0.8,\n                          duration: 1.2,\n                          scale: { duration: 4, repeat: Infinity, ease: \"easeInOut\" },\n                          rotateY: { duration: 6, repeat: Infinity, ease: \"easeInOut\" }\n                        }}\n                        whileHover={{ scale: 1.05, y: -10 }}\n                        className={`relative order-1 ${\n                          shouldHighlightUser(topPerformers[1]._id)\n                            ? 'ring-8 ring-yellow-400 ring-opacity-100'\n                            : ''\n                        }`}\n                        style={{\n                          height: '280px',\n                          transform: shouldHighlightUser(topPerformers[1]._id) ? 'scale(1.08)' : 'scale(1)',\n                          filter: shouldHighlightUser(topPerformers[1]._id)\n                            ? 'brightness(1.3) saturate(1.2) drop-shadow(0 0 30px rgba(255, 215, 0, 1))'\n                            : 'none',\n                          transition: 'all 0.3s ease',\n                          border: shouldHighlightUser(topPerformers[1]._id) ? '4px solid #FFD700' : 'none',\n                          borderRadius: isCurrentUser(topPerformers[1]._id) ? '20px' : '0px',\n                          background: isCurrentUser(topPerformers[1]._id) ? 'linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1))' : 'transparent'\n                        }}\n                      >\n                        {/* Second Place Podium Base */}\n                        <div className=\"absolute bottom-0 w-full h-20 bg-gradient-to-t from-gray-400 to-gray-300 rounded-t-lg border-2 border-gray-500 flex items-center justify-center z-10\">\n                          <span className=\"text-2xl font-black text-gray-800 relative z-20\">2nd</span>\n                        </div>\n\n                        {/* Second Place Champion Card */}\n                        <div\n                          className={`relative bg-gradient-to-br ${topPerformers[1].tier.color} p-1 rounded-xl ${topPerformers[1].tier.glow} shadow-xl mb-20`}\n                          style={{\n                            boxShadow: `0 6px 20px ${topPerformers[1].tier.shadowColor}50`,\n                            width: '200px'\n                          }}\n                        >\n                          <div\n                            className={`${topPerformers[1].tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`}\n                          >\n                            <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"></div>\n\n                            {/* Silver Medal */}\n                            <div\n                              className=\"absolute -top-3 left-1/2 transform -translate-x-1/2 w-10 h-10 bg-gradient-to-br from-gray-300 to-gray-500 rounded-full flex items-center justify-center font-black text-lg shadow-lg relative z-20\"\n                              style={{\n                                color: '#1f2937',\n                                border: '2px solid #FFFFFF'\n                              }}\n                            >\n                              🥈\n                            </div>\n\n                            {/* Profile Picture */}\n                            <div className={`relative mx-auto mb-3 ${user && topPerformers[1]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`}>\n\n                              <ProfilePicture\n                                user={topPerformers[1]}\n                                size=\"md\"\n                                showOnlineStatus={true}\n                                style={{\n                                  width: '40px',\n                                  height: '40px'\n                                }}\n                              />\n                              {/* Debug: Show user data */}\n                              {console.log('🥈 Second place user:', topPerformers[1])}\n                            </div>\n\n                            {/* Name and Stats */}\n                            <h3\n                              className=\"text-sm font-bold mb-2 truncate\"\n                              style={{ color: topPerformers[1].tier.nameColor }}\n                            >\n                              {topPerformers[1].name}\n                            </h3>\n\n                            <div className=\"text-lg font-black mb-2\" style={{ color: topPerformers[1].tier.textColor }}>\n                              {topPerformers[1].totalXP.toLocaleString()} XP\n                            </div>\n\n                            <div className=\"flex justify-center gap-3 text-xs\">\n                              <span style={{ color: topPerformers[1].tier.textColor }}>\n                                🧠 {topPerformers[1].totalQuizzesTaken}\n                              </span>\n                              <span style={{ color: topPerformers[1].tier.textColor }}>\n                                🔥 {topPerformers[1].currentStreak}\n                              </span>\n                            </div>\n                          </div>\n                        </div>\n                      </motion.div>\n                    )}\n\n                    {/* First Place - Center and Elevated */}\n                    {topPerformers[0] && (\n                      <motion.div\n                        key={`first-${topPerformers[0]._id}`}\n                        ref={user && String(topPerformers[0]._id) === String(user._id) ? podiumUserRef : null}\n                        data-user-id={topPerformers[0]._id}\n                        data-user-rank={1}\n                        initial={{ opacity: 0, y: -100, scale: 0.8 }}\n                        animate={{\n                          opacity: 1,\n                          y: 0,\n                          scale: 1,\n                          rotateY: [0, 10, -10, 0],\n                          y: [0, -10, 0]\n                        }}\n                        transition={{\n                          delay: 0.5,\n                          duration: 1.5,\n                          rotateY: { duration: 8, repeat: Infinity, ease: \"easeInOut\" },\n                          y: { duration: 4, repeat: Infinity, ease: \"easeInOut\" }\n                        }}\n                        whileHover={{ scale: 1.08, y: -15 }}\n                        className={`relative order-2 z-10 ${\n                          shouldHighlightUser(topPerformers[0]._id)\n                            ? 'ring-8 ring-yellow-400 ring-opacity-100'\n                            : ''\n                        }`}\n                        style={{\n                          height: '320px',\n                          transform: shouldHighlightUser(topPerformers[0]._id) ? 'scale(1.08)' : 'scale(1)',\n                          filter: shouldHighlightUser(topPerformers[0]._id)\n                            ? 'brightness(1.3) saturate(1.2) drop-shadow(0 0 30px rgba(255, 215, 0, 1))'\n                            : 'none',\n                          transition: 'all 0.3s ease',\n                          border: shouldHighlightUser(topPerformers[0]._id) ? '4px solid #FFD700' : 'none',\n                          borderRadius: isCurrentUser(topPerformers[0]._id) ? '20px' : '0px',\n                          background: isCurrentUser(topPerformers[0]._id) ? 'linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1))' : 'transparent'\n                        }}\n                        data-section=\"podium\"\n\n                      >\n                        {/* First Place Podium Base - Tallest */}\n                        <div className=\"absolute bottom-0 w-full h-32 bg-gradient-to-t from-yellow-500 to-yellow-300 rounded-t-lg border-2 border-yellow-600 flex items-center justify-center z-10\">\n                          <span className=\"text-3xl font-black text-yellow-900 relative z-20\">1st</span>\n                        </div>\n\n                        {/* Crown Animation */}\n                        <motion.div\n                          animate={{ rotate: [0, 10, -10, 0], y: [0, -5, 0] }}\n                          transition={{ duration: 3, repeat: Infinity }}\n                          className=\"absolute -top-16 left-1/2 transform -translate-x-1/2 z-30\"\n                        >\n                          <TbCrown className=\"w-16 h-16 text-yellow-400 drop-shadow-lg\" />\n                        </motion.div>\n\n                        {/* First Place Champion Card */}\n                        <div\n                          className={`relative bg-gradient-to-br ${topPerformers[0].tier.color} p-1.5 rounded-2xl ${topPerformers[0].tier.glow} shadow-2xl mb-32 transform scale-110`}\n                          style={{\n                            boxShadow: `0 8px 32px ${topPerformers[0].tier.shadowColor}60, 0 0 0 1px rgba(255,255,255,0.1)`,\n                            width: '240px'\n                          }}\n                        >\n                          <div\n                            className={`${topPerformers[0].tier.bgColor} backdrop-blur-lg rounded-xl p-6 text-center relative overflow-hidden`}\n                            style={{\n                              background: `${topPerformers[0].tier.bgColor}, radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)`\n                            }}\n                          >\n                            <div className=\"absolute inset-0 bg-gradient-to-br from-white/10 to-transparent rounded-xl\"></div>\n\n                            {/* Gold Medal */}\n                            <div\n                              className=\"absolute -top-4 left-1/2 transform -translate-x-1/2 w-12 h-12 bg-gradient-to-br from-yellow-300 to-yellow-500 rounded-full flex items-center justify-center font-black text-xl shadow-lg relative z-20\"\n                              style={{\n                                color: '#1f2937',\n                                textShadow: '1px 1px 2px rgba(0,0,0,0.3)',\n                                border: '2px solid #FFFFFF'\n                              }}\n                            >\n                              👑\n                            </div>\n\n                            {/* Profile Picture */}\n                            <div className={`relative mx-auto mb-4 ${user && topPerformers[0]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`}>\n\n                              <ProfilePicture\n                                user={topPerformers[0]}\n                                size=\"lg\"\n                                showOnlineStatus={true}\n                                style={{\n                                  width: '48px',\n                                  height: '48px'\n                                }}\n                              />\n                              {/* Debug: Show user data */}\n                              {console.log('🥇 First place user:', topPerformers[0])}\n                              {user && topPerformers[0]._id === user._id && (\n                                <div\n                                  className=\"absolute -bottom-2 -right-2 rounded-full p-2 animate-pulse\"\n                                  style={{\n                                    background: 'linear-gradient(45deg, #fbbf24, #f59e0b)',\n                                    boxShadow: '0 2px 8px rgba(0,0,0,0.3)'\n                                  }}\n                                >\n                                  <TbStar className=\"w-6 h-6 text-gray-900\" />\n                                </div>\n                              )}\n                            </div>\n\n                            {/* Champion Info */}\n                            <div className=\"flex items-center justify-center gap-2 mb-2\">\n                              <h3\n                                className=\"text-lg font-black truncate\"\n                                style={{\n                                  color: topPerformers[0].tier.nameColor,\n                                  textShadow: `2px 2px 4px ${topPerformers[0].tier.shadowColor}`,\n                                  filter: 'drop-shadow(0 0 8px currentColor)'\n                                }}\n                              >\n                                {topPerformers[0].name}\n                              </h3>\n                              {isCurrentUser(topPerformers[0]._id) && (\n                                <span\n                                  className=\"px-2 py-1 rounded-full text-xs font-black animate-pulse\"\n                                  style={{\n                                    background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                    color: '#1f2937',\n                                    boxShadow: '0 2px 8px rgba(255,215,0,0.8)',\n                                    border: '1px solid #FFFFFF',\n                                    fontSize: '10px'\n                                  }}\n                                >\n                                  🎯 YOU\n                                </span>\n                              )}\n                            </div>\n\n                            <div\n                              className={`inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r ${topPerformers[0].tier.color} rounded-full text-sm font-black mb-3 relative z-10`}\n                              style={{\n                                background: `linear-gradient(135deg, ${topPerformers[0].tier.borderColor}, ${topPerformers[0].tier.textColor})`,\n                                color: '#FFFFFF',\n                                textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                                boxShadow: `0 4px 15px ${topPerformers[0].tier.shadowColor}60`,\n                                border: '2px solid rgba(255,255,255,0.2)'\n                              }}\n                            >\n                              {topPerformers[0].tier.icon && React.createElement(topPerformers[0].tier.icon, {\n                                className: \"w-4 h-4\",\n                                style: { color: '#FFFFFF' }\n                              })}\n                              <span style={{ color: '#FFFFFF' }}>{topPerformers[0].tier.title}</span>\n                            </div>\n\n                            {/* Enhanced Stats */}\n                            <div className=\"space-y-2 relative z-10\">\n                              <div className=\"text-xl font-black\" style={{\n                                color: topPerformers[0].tier.nameColor,\n                                textShadow: `2px 2px 4px ${topPerformers[0].tier.shadowColor}`,\n                                filter: 'drop-shadow(0 0 8px currentColor)'\n                              }}>\n                                {topPerformers[0].totalXP.toLocaleString()} XP\n                              </div>\n\n                              <div className=\"flex justify-center gap-4 text-sm\">\n                                <div className=\"text-center\">\n                                  <div className=\"flex items-center gap-1 justify-center\">\n                                    <TbBrain className=\"w-4 h-4\" style={{ color: topPerformers[0].tier.textColor }} />\n                                    <span className=\"font-bold\" style={{ color: topPerformers[0].tier.textColor }}>\n                                      {topPerformers[0].totalQuizzesTaken}\n                                    </span>\n                                  </div>\n                                  <div className=\"text-xs opacity-80\" style={{ color: topPerformers[0].tier.textColor }}>Quizzes</div>\n                                </div>\n                                <div className=\"text-center\">\n                                  <div className=\"flex items-center gap-1 justify-center\">\n                                    <TbFlame className=\"w-4 h-4\" style={{ color: '#FF6B35' }} />\n                                    <span className=\"font-bold\" style={{ color: topPerformers[0].tier.textColor }}>\n                                      {topPerformers[0].currentStreak}\n                                    </span>\n                                  </div>\n                                  <div className=\"text-xs opacity-80\" style={{ color: topPerformers[0].tier.textColor }}>Streak</div>\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      </motion.div>\n                    )}\n\n                    {/* Third Place - Right */}\n                    {topPerformers[2] && (\n                      <motion.div\n                        key={`third-${topPerformers[2]._id}`}\n                        ref={user && String(topPerformers[2]._id) === String(user._id) ? podiumUserRef : null}\n                        data-user-id={topPerformers[2]._id}\n                        data-user-rank={3}\n                        initial={{ opacity: 0, x: 100, y: 50 }}\n                        animate={{\n                          opacity: 1,\n                          x: 0,\n                          y: 0,\n                          scale: [1, 1.02, 1],\n                          rotateY: [0, -5, 0]\n                        }}\n                        transition={{\n                          delay: 1.0,\n                          duration: 1.2,\n                          scale: { duration: 4, repeat: Infinity, ease: \"easeInOut\" },\n                          rotateY: { duration: 6, repeat: Infinity, ease: \"easeInOut\" }\n                        }}\n                        whileHover={{ scale: 1.05, y: -10 }}\n                        className={`relative order-3 ${\n                          shouldHighlightUser(topPerformers[2]._id)\n                            ? 'ring-8 ring-yellow-400 ring-opacity-100'\n                            : ''\n                        }`}\n                        style={{\n                          height: '280px',\n                          transform: shouldHighlightUser(topPerformers[2]._id) ? 'scale(1.08)' : 'scale(1)',\n                          filter: shouldHighlightUser(topPerformers[2]._id)\n                            ? 'brightness(1.3) saturate(1.2) drop-shadow(0 0 30px rgba(255, 215, 0, 1))'\n                            : 'none',\n                          transition: 'all 0.3s ease',\n                          border: shouldHighlightUser(topPerformers[2]._id) ? '4px solid #FFD700' : 'none',\n                          borderRadius: isCurrentUser(topPerformers[2]._id) ? '20px' : '0px',\n                          background: isCurrentUser(topPerformers[2]._id) ? 'linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1))' : 'transparent'\n                        }}\n                      >\n                        {/* Third Place Podium Base */}\n                        <div className=\"absolute bottom-0 w-full h-16 bg-gradient-to-t from-amber-600 to-amber-400 rounded-t-lg border-2 border-amber-700 flex items-center justify-center z-10\">\n                          <span className=\"text-xl font-black text-amber-900 relative z-20\">3rd</span>\n                        </div>\n\n                        {/* Third Place Champion Card */}\n                        <div\n                          className={`relative bg-gradient-to-br ${topPerformers[2].tier.color} p-1 rounded-xl ${topPerformers[2].tier.glow} shadow-xl mb-16`}\n                          style={{\n                            boxShadow: `0 6px 20px ${topPerformers[2].tier.shadowColor}50`,\n                            width: '200px'\n                          }}\n                        >\n                          <div\n                            className={`${topPerformers[2].tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`}\n                          >\n                            <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"></div>\n\n                            {/* Bronze Medal */}\n                            <div\n                              className=\"absolute -top-3 left-1/2 transform -translate-x-1/2 w-10 h-10 bg-gradient-to-br from-amber-600 to-amber-800 rounded-full flex items-center justify-center font-black text-lg shadow-lg relative z-20\"\n                              style={{\n                                color: '#1f2937',\n                                border: '2px solid #FFFFFF'\n                              }}\n                            >\n                              🥉\n                            </div>\n\n                            {/* Profile Picture */}\n                            <div className={`relative mx-auto mb-3 ${user && topPerformers[2]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`}>\n                              <ProfilePicture\n                                user={topPerformers[2]}\n                                size=\"md\"\n                                showOnlineStatus={true}\n                                style={{\n                                  width: '40px',\n                                  height: '40px'\n                                }}\n                              />\n                            </div>\n\n                            {/* Name and Stats */}\n                            <h3\n                              className=\"text-sm font-bold mb-2 truncate\"\n                              style={{ color: topPerformers[2].tier.nameColor }}\n                            >\n                              {topPerformers[2].name}\n                            </h3>\n\n                            <div className=\"text-lg font-black mb-2\" style={{ color: topPerformers[2].tier.textColor }}>\n                              {topPerformers[2].totalXP.toLocaleString()} XP\n                            </div>\n\n                            <div className=\"flex justify-center gap-3 text-xs\">\n                              <span style={{ color: topPerformers[2].tier.textColor }}>\n                                🧠 {topPerformers[2].totalQuizzesTaken}\n                              </span>\n                              <span style={{ color: topPerformers[2].tier.textColor }}>\n                                🔥 {topPerformers[2].currentStreak}\n                              </span>\n                            </div>\n                          </div>\n                        </div>\n                      </motion.div>\n                    )}\n                  </div>\n\n\n\n\n\n\n\n\n                </motion.div>\n              )}\n\n              {/* LEAGUE-BASED RANKING DISPLAY */}\n              {selectedLeague ? (\n                /* SELECTED LEAGUE VIEW */\n                leagueUsers.length > 0 && (\n                  <motion.div\n                    initial={{ opacity: 0, y: 30 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: 1, duration: 0.8 }}\n                    className=\"mt-16 main-ranking-section\"\n                  >\n                    {/* Selected League Header */}\n                    <div className=\"text-center mb-8 md:mb-12\">\n                      <motion.h2\n                        className=\"text-2xl sm:text-3xl md:text-4xl font-black mb-3\"\n                        style={{\n                          background: `linear-gradient(45deg, ${leagueSystem[selectedLeague].borderColor}, ${leagueSystem[selectedLeague].textColor})`,\n                          WebkitBackgroundClip: 'text',\n                          WebkitTextFillColor: 'transparent',\n                          textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                          filter: `drop-shadow(0 0 12px ${leagueSystem[selectedLeague].borderColor})`\n                        }}\n                        animate={{ scale: [1, 1.01, 1] }}\n                        transition={{ duration: 4, repeat: Infinity }}\n                      >\n                        {leagueSystem[selectedLeague].leagueIcon} {leagueSystem[selectedLeague].title} LEAGUE {leagueSystem[selectedLeague].leagueIcon}\n                      </motion.h2>\n                      <p className=\"text-white/70 text-sm md:text-base font-medium\">\n                        {leagueUsers.length} champions in this league\n                      </p>\n                      <motion.button\n                        whileHover={{ scale: 1.05 }}\n                        whileTap={{ scale: 0.95 }}\n                        onClick={() => setSelectedLeague(null)}\n                        className=\"mt-4 px-6 py-2 bg-gradient-to-r from-gray-600 to-gray-700 text-white rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300\"\n                      >\n                        ← Back to All Leagues\n                      </motion.button>\n                    </div>\n\n                    {/* Selected League Users */}\n                    <div className=\"max-w-6xl mx-auto px-4\">\n                      <div className=\"grid gap-3 md:gap-4\">\n                        {leagueUsers.map((champion, index) => {\n                          const actualRank = index + 1;\n                          const isCurrentUser = user && String(champion._id) === String(user._id);\n\n                          return (\n                            <motion.div\n                              key={champion._id}\n                              ref={isCurrentUser ? listUserRef : null}\n                              data-user-id={champion._id}\n                              data-user-rank={actualRank}\n                              initial={{ opacity: 0, y: 20 }}\n                              animate={{ opacity: 1, y: 0 }}\n                              transition={{ delay: 0.1 + index * 0.05, duration: 0.4 }}\n                              whileHover={{ scale: 1.01, y: -2 }}\n                              className={`ranking-card group relative ${\n                                shouldHighlightUser(champion._id)\n                                  ? 'ring-8 ring-yellow-400 ring-opacity-100'\n                                  : ''\n                              }`}\n                              style={{\n                                transform: shouldHighlightUser(champion._id) ? 'scale(1.05)' : 'scale(1)',\n                                filter: shouldHighlightUser(champion._id)\n                                  ? 'brightness(1.25) saturate(1.3) drop-shadow(0 0 25px rgba(255, 215, 0, 1))'\n                                  : 'none',\n                                transition: 'all 0.3s ease',\n                                border: shouldHighlightUser(champion._id) ? '4px solid #FFD700' : 'none',\n                                borderRadius: shouldHighlightUser(champion._id) ? '16px' : '0px',\n                                background: isCurrentUser ? 'linear-gradient(135deg, rgba(255, 215, 0, 0.15), rgba(255, 165, 0, 0.15))' : 'transparent',\n                                position: 'relative',\n                                zIndex: isCurrentUser ? 10 : 1\n                              }}\n                            >\n                              {/* League User Card */}\n                              <div\n                                className={`bg-gradient-to-r ${champion.tier.color} p-0.5 rounded-2xl ${champion.tier.glow} transition-all duration-300 group-hover:scale-[1.01]`}\n                                style={{\n                                  boxShadow: `0 4px 20px ${champion.tier.shadowColor}40`\n                                }}\n                              >\n                                <div\n                                  className={`${champion.tier.bgColor} backdrop-blur-xl rounded-2xl p-4 flex items-center gap-4 relative overflow-hidden`}\n                                  style={{\n                                    border: `1px solid ${champion.tier.borderColor}30`\n                                  }}\n                                >\n                                  {/* Subtle Background Gradient */}\n                                  <div className=\"absolute inset-0 bg-gradient-to-r from-white/3 to-transparent rounded-2xl\"></div>\n\n                                  {/* Left Section: Rank & Profile */}\n                                  <div className=\"flex items-center gap-3 flex-shrink-0\">\n                                    {/* Rank Badge */}\n                                    <div className=\"relative\">\n                                      <div\n                                        className=\"w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center font-black text-sm shadow-lg relative z-10\"\n                                        style={{\n                                          color: '#FFFFFF',\n                                          textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                                          border: '2px solid rgba(255,255,255,0.2)',\n                                          boxShadow: '0 2px 8px rgba(0,0,0,0.3)'\n                                        }}\n                                      >\n                                        #{actualRank}\n                                      </div>\n                                    </div>\n\n                                    {/* Profile Picture with Online Status */}\n                                    <div className=\"relative\">\n                                      <ProfilePicture\n                                        user={champion}\n                                        size=\"sm\"\n                                        showOnlineStatus={true}\n                                        style={{\n                                          width: '32px',\n                                          height: '32px'\n                                        }}\n                                      />\n                                      {/* Current User Indicator */}\n                                      {isCurrentUser && (\n                                        <div\n                                          className=\"absolute -top-1 -right-1 w-4 h-4 rounded-full flex items-center justify-center animate-pulse\"\n                                          style={{\n                                            background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                            boxShadow: '0 2px 6px rgba(255,215,0,0.6)'\n                                          }}\n                                        >\n                                          <TbStar className=\"w-2.5 h-2.5 text-gray-900\" />\n                                        </div>\n                                      )}\n                                    </div>\n                                  </div>\n\n                                  {/* Center Section: User Info */}\n                                  <div className=\"flex-1 min-w-0 px-2\">\n                                    <div className=\"space-y-1\">\n                                      {/* User Name */}\n                                      <div className=\"flex items-center gap-2 mb-1\">\n                                        <h3\n                                          className=\"text-base md:text-lg font-bold truncate\"\n                                          style={{\n                                            color: champion.tier.nameColor,\n                                            textShadow: `1px 1px 2px ${champion.tier.shadowColor}`,\n                                            filter: 'drop-shadow(0 0 4px currentColor)'\n                                          }}\n                                        >\n                                          {champion.name}\n                                        </h3>\n                                        {isCurrentUser && (\n                                          <span\n                                            className=\"px-3 py-1 rounded-full text-sm font-black animate-pulse\"\n                                            style={{\n                                              background: 'linear-gradient(45deg, #FFD700, #FFA500, #FFD700)',\n                                              color: '#1f2937',\n                                              boxShadow: '0 4px 12px rgba(255,215,0,0.8), 0 0 20px rgba(255,215,0,0.6)',\n                                              border: '2px solid #FFFFFF',\n                                              textShadow: '1px 1px 2px rgba(0,0,0,0.3)',\n                                              fontSize: '12px',\n                                              fontWeight: '900'\n                                            }}\n                                          >\n                                            🎯 YOU\n                                          </span>\n                                        )}\n                                      </div>\n\n                                      {/* Class Info */}\n                                      <div className=\"text-xs text-white/70 mt-0.5\">\n                                        {champion.level} • Class {champion.class}\n                                      </div>\n                                    </div>\n                                  </div>\n\n                                  {/* Right Section: Stats */}\n                                  <div className=\"flex flex-col items-end gap-1 flex-shrink-0\">\n                                    {/* XP Display */}\n                                    <div\n                                      className=\"text-lg md:text-xl font-black mb-2\"\n                                      style={{\n                                        color: champion.tier.nameColor,\n                                        textShadow: `1px 1px 2px ${champion.tier.shadowColor}`,\n                                        filter: 'drop-shadow(0 0 6px currentColor)'\n                                      }}\n                                    >\n                                      {champion.totalXP.toLocaleString()} XP\n                                    </div>\n\n                                    {/* Compact Stats */}\n                                    <div className=\"flex items-center gap-3 text-xs\">\n                                      <div\n                                        className=\"flex items-center gap-1 px-2 py-1 rounded-md\"\n                                        style={{\n                                          backgroundColor: `${champion.tier.borderColor}20`,\n                                          color: champion.tier.textColor\n                                        }}\n                                      >\n                                        <TbBrain className=\"w-3 h-3\" />\n                                        <span className=\"font-medium\">{champion.totalQuizzesTaken}</span>\n                                      </div>\n                                      <div\n                                        className=\"flex items-center gap-1 px-2 py-1 rounded-md\"\n                                        style={{\n                                          backgroundColor: '#FF6B3520',\n                                          color: '#FF6B35'\n                                        }}\n                                      >\n                                        <TbFlame className=\"w-3 h-3\" />\n                                        <span className=\"font-medium\">{champion.currentStreak}</span>\n                                      </div>\n                                    </div>\n                                  </div>\n                                </div>\n                              </div>\n                            </motion.div>\n                          );\n                        })}\n                      </div>\n                    </div>\n                  </motion.div>\n                )\n              ) : (\n                /* ALL LEAGUES GROUPED VIEW */\n                Object.keys(leagueGroups).length > 0 && (\n                  <motion.div\n                    initial={{ opacity: 0, y: 30 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: 1, duration: 0.8 }}\n                    className=\"mt-16 main-ranking-section\"\n                    id=\"grouped-leagues-section\"\n                  >\n                    {/* All Leagues Header */}\n                    <div className=\"text-center mb-8 md:mb-12\">\n                      <motion.h2\n                        className=\"text-2xl sm:text-3xl md:text-4xl font-black mb-3\"\n                        style={{\n                          background: 'linear-gradient(45deg, #8B5CF6, #06B6D4, #10B981)',\n                          WebkitBackgroundClip: 'text',\n                          WebkitTextFillColor: 'transparent',\n                          textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                          filter: 'drop-shadow(0 0 12px #8B5CF6)'\n                        }}\n                        animate={{ scale: [1, 1.01, 1] }}\n                        transition={{ duration: 4, repeat: Infinity }}\n                      >\n                        🏆 LEAGUE RANKINGS 🏆\n                      </motion.h2>\n                      <p className=\"text-white/70 text-sm md:text-base font-medium\">\n                        Click on any league icon above to see its members\n                      </p>\n                    </div>\n\n                    {/* Grouped Leagues Display */}\n                    <div className=\"max-w-6xl mx-auto px-4 space-y-8\">\n                      {getOrderedLeagues().map((leagueKey) => {\n                        const league = leagueSystem[leagueKey];\n                        const leagueData = leagueGroups[leagueKey];\n                        const topUsers = leagueData.users.slice(0, 3); // Show top 3 from each league\n\n                        return (\n                          <motion.div\n                            key={leagueKey}\n                            ref={(el) => (leagueRefs.current[leagueKey] = el)}\n                            initial={{ opacity: 0, y: 20 }}\n                            animate={{ opacity: 1, y: 0 }}\n                            transition={{ delay: 0.2, duration: 0.6 }}\n                            className=\"bg-gradient-to-r from-white/5 to-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/10\"\n                            id={`league-${leagueKey}`}\n                            data-league={leagueKey}\n                          >\n                            {/* League Header */}\n                            <div className=\"flex items-center justify-between mb-6\">\n                              <div className=\"flex items-center gap-4\">\n                                <div\n                                  className=\"w-16 h-16 rounded-xl flex items-center justify-center text-3xl\"\n                                  style={{\n                                    background: `linear-gradient(135deg, ${league.borderColor}40, ${league.textColor}20)`,\n                                    border: `2px solid ${league.borderColor}60`,\n                                    boxShadow: `0 4px 20px ${league.shadowColor}40`\n                                  }}\n                                >\n                                  {league.leagueIcon}\n                                </div>\n                                <div>\n                                  <h3\n                                    className=\"text-2xl font-black mb-1\"\n                                    style={{\n                                      color: league.nameColor,\n                                      textShadow: `2px 2px 4px ${league.shadowColor}`,\n                                      filter: 'drop-shadow(0 0 8px currentColor)'\n                                    }}\n                                  >\n                                    {league.title} LEAGUE\n                                  </h3>\n                                  <p className=\"text-white/70 text-sm\">\n                                    {leagueData.users.length} champions • {league.description}\n                                  </p>\n                                </div>\n                              </div>\n                              <motion.button\n                                whileHover={{ scale: 1.05 }}\n                                whileTap={{ scale: 0.95 }}\n                                onClick={() => handleLeagueSelect(leagueKey)}\n                                className=\"px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300\"\n                              >\n                                View All ({leagueData.users.length})\n                              </motion.button>\n                            </div>\n\n                            {/* Top 3 Users from League */}\n                            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4\">\n                              {topUsers.map((champion, index) => {\n                                const isCurrentUser = user && champion._id === user._id;\n                                const leagueRank = index + 1;\n\n                                return (\n                                  <motion.div\n                                    key={champion._id}\n                                    data-user-id={champion._id}\n                                    data-user-rank={leagueRank}\n                                    initial={{ opacity: 0, scale: 0.9 }}\n                                    animate={{ opacity: 1, scale: 1 }}\n                                    transition={{ delay: 0.3 + index * 0.1, duration: 0.4 }}\n                                    whileHover={{ scale: 1.02, y: -2 }}\n                                    className={`relative ${\n                                      shouldHighlightUser(champion._id)\n                                        ? 'ring-2 ring-yellow-400/60'\n                                        : ''\n                                    }`}\n                                  >\n                                    <div\n                                      className={`bg-gradient-to-br ${champion.tier.color} p-0.5 rounded-xl ${champion.tier.glow} shadow-lg`}\n                                      style={{\n                                        boxShadow: `0 4px 15px ${champion.tier.shadowColor}30`\n                                      }}\n                                    >\n                                      <div\n                                        className={`${champion.tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`}\n                                      >\n                                        <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"></div>\n\n                                        {/* League Rank Badge */}\n                                        <div\n                                          className=\"absolute -top-2 -right-2 w-6 h-6 rounded-full flex items-center justify-center font-black text-xs\"\n                                          style={{\n                                            background: league.borderColor,\n                                            color: '#FFFFFF',\n                                            border: '2px solid #FFFFFF'\n                                          }}\n                                        >\n                                          #{leagueRank}\n                                        </div>\n\n                                        {/* Profile Picture */}\n                                        <div className={`relative mx-auto mb-3 ${\n                                          isCurrentUser\n                                            ? 'ring-1 ring-yellow-400 ring-opacity-80'\n                                            : ''\n                                        }`}>\n                                          <ProfilePicture\n                                            user={champion}\n                                            size=\"md\"\n                                            showOnlineStatus={true}\n                                            style={{\n                                              width: '40px',\n                                              height: '40px'\n                                            }}\n                                          />\n                                          {isCurrentUser && (\n                                            <div\n                                              className=\"absolute -bottom-1 -right-1 w-4 h-4 rounded-full flex items-center justify-center animate-pulse\"\n                                              style={{\n                                                background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                                boxShadow: '0 2px 6px rgba(255,215,0,0.6)'\n                                              }}\n                                            >\n                                              <TbStar className=\"w-2.5 h-2.5 text-gray-900\" />\n                                            </div>\n                                          )}\n                                        </div>\n\n                                        {/* Name and Stats */}\n                                        <h4\n                                          className=\"text-sm font-bold mb-2 truncate\"\n                                          style={{ color: champion.tier.nameColor }}\n                                        >\n                                          {champion.name}\n                                          {isCurrentUser && (\n                                            <span className=\"ml-1 text-xs text-yellow-400\">👑</span>\n                                          )}\n                                        </h4>\n\n                                        <div className=\"text-lg font-black mb-2\" style={{ color: champion.tier.textColor }}>\n                                          {champion.totalXP.toLocaleString()} XP\n                                        </div>\n\n                                        <div className=\"flex justify-center gap-3 text-xs\">\n                                          <span style={{ color: champion.tier.textColor }}>\n                                            🧠 {champion.totalQuizzesTaken}\n                                          </span>\n                                          <span style={{ color: champion.tier.textColor }}>\n                                            🔥 {champion.currentStreak}\n                                          </span>\n                                        </div>\n                                      </div>\n                                    </div>\n                                  </motion.div>\n                                );\n                              })}\n                            </div>\n\n                            {/* Show More Indicator */}\n                            {leagueData.users.length > 3 && (\n                              <div className=\"text-center mt-4\">\n                                <p className=\"text-white/60 text-sm\">\n                                  +{leagueData.users.length - 3} more champions in this league\n                                </p>\n                              </div>\n                            )}\n                          </motion.div>\n                        );\n                      })}\n                    </div>\n                  </motion.div>\n                )\n              )}\n\n\n\n\n              {/* DATA INTEGRATION STATUS */}\n              {rankingData.length > 0 && (\n                <motion.div\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 1.8, duration: 0.8 }}\n                  className=\"mt-12 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-green-500/20 backdrop-blur-lg rounded-2xl p-6 border border-blue-400/30\"\n                >\n                  <div className=\"text-center\">\n                    <h3 className=\"text-xl font-bold mb-4\" style={{\n                      color: '#60A5FA',\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      fontWeight: '800'\n                    }}>📊 Real User Data Integration</h3>\n                    <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm\">\n                      <div className=\"bg-green-500/20 rounded-lg p-3\">\n                        <div className=\"text-green-400 font-bold text-lg\">\n                          {rankingData.filter(u => u.dataSource === 'reports').length}\n                        </div>\n                        <div className=\"text-white/80\">📊 Live Quiz Data</div>\n                      </div>\n                      <div className=\"bg-blue-500/20 rounded-lg p-3\">\n                        <div className=\"text-blue-400 font-bold text-lg\">\n                          {rankingData.filter(u => u.dataSource === 'legacy_points').length}\n                        </div>\n                        <div className=\"text-white/80\">📈 Legacy Points</div>\n                      </div>\n                      <div className=\"bg-purple-500/20 rounded-lg p-3\">\n                        <div className=\"text-purple-400 font-bold text-lg\">\n                          {rankingData.filter(u => u.dataSource === 'estimated').length}\n                        </div>\n                        <div className=\"text-white/80\">🔮 Estimated Stats</div>\n                      </div>\n                    </div>\n                    <p className=\"text-white/70 text-sm mt-4\">\n                      Using real database users (admins excluded) with intelligent data processing\n                    </p>\n                  </div>\n                </motion.div>\n              )}\n\n              {/* CURRENT USER HIGHLIGHT */}\n              {currentUserRank && currentUserRank > 3 && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 1.5, duration: 0.8 }}\n                  className=\"mt-12 bg-gradient-to-r from-yellow-500/20 via-orange-500/20 to-red-500/20 backdrop-blur-lg rounded-2xl p-6 border border-yellow-400/30\"\n                >\n                  <div className=\"text-center\">\n                    <h3 className=\"text-2xl font-bold mb-2\" style={{\n                      color: '#ffffff',\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      fontWeight: '800'\n                    }}>Your Current Position</h3>\n                    <div className=\"text-6xl font-black mb-2\" style={{\n                      color: '#fbbf24',\n                      textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                      fontWeight: '900'\n                    }}>#{currentUserRank}</div>\n                    <p className=\"text-lg\" style={{\n                      color: '#e5e7eb',\n                      textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                      fontWeight: '600'\n                    }}>\n                      You're doing amazing! Keep pushing forward to reach the podium! 🚀\n                    </p>\n                  </div>\n                </motion.div>\n              )}\n\n              {/* MOTIVATIONAL FOOTER */}\n              <motion.div\n                initial={{ opacity: 0, y: 30 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 2, duration: 0.8 }}\n                className=\"mt-16 text-center\"\n              >\n                <div className=\"bg-gradient-to-r from-purple-600/20 via-pink-600/20 to-indigo-600/20 backdrop-blur-lg rounded-2xl p-8 border border-white/10\">\n                  <motion.div\n                    animate={{ scale: [1, 1.05, 1] }}\n                    transition={{ duration: 3, repeat: Infinity }}\n                  >\n                    <TbRocket className=\"w-16 h-16 text-yellow-400 mx-auto mb-4\" />\n                  </motion.div>\n                  <h3 className=\"text-3xl font-bold mb-4\" style={{\n                    color: '#ffffff',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  }}>Ready to Rise Higher?</h3>\n                  <p className=\"text-xl mb-6 max-w-2xl mx-auto\" style={{\n                    color: '#e5e7eb',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                    fontWeight: '600'\n                  }}>\n                    Every quiz you take, every challenge you conquer, brings you closer to greatness.\n                    Your journey to the top starts with the next question!\n                  </p>\n                  <motion.button\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    className=\"bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300\"\n                    onClick={() => window.location.href = '/user/quiz'}\n                  >\n                    Take a Quiz Now! 🎯\n                  </motion.button>\n                </div>\n              </motion.div>\n\n              {/* EMPTY STATE */}\n              {rankingData.length === 0 && !loading && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  className=\"text-center py-20\"\n                >\n                  <TbTrophy className=\"w-24 h-24 text-white/30 mx-auto mb-6\" />\n                  <h3 className=\"text-2xl font-bold mb-4\" style={{\n                    color: '#ffffff',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  }}>No Champions Yet</h3>\n                  <p className=\"text-lg\" style={{\n                    color: '#e5e7eb',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                    fontWeight: '600'\n                  }}>\n                    Be the first to take a quiz and claim your spot in the Hall of Champions!\n                  </p>\n                </motion.div>\n              )}\n            </div>\n          </motion.div>\n        )}\n      </div>\n    </div>\n    </>\n  );\n};\n\nexport default AmazingRankingPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,MAAM;AAC9B,SACEC,QAAQ,EACRC,OAAO,EACPC,MAAM,EACNC,OAAO,EACPC,OAAO,EACPC,MAAM,EACNC,SAAS,EACTC,OAAO,EACPC,QAAQ,EACRC,SAAS,EACTC,OAAO,EACPC,QAAQ,EACRC,OAAO,QACF,gBAAgB;AACvB,SAASC,uBAAuB,EAAEC,gBAAgB,EAAEC,cAAc,QAAQ,2BAA2B;AACrG,SAASC,WAAW,QAAQ,yBAAyB;AACrD,OAAOC,cAAc,MAAM,2CAA2C;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEvE,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAMC,SAAS,GAAG3B,WAAW,CAAE4B,KAAK,IAAKA,KAAK,CAACC,KAAK,IAAI,CAAC,CAAC,CAAC;EAC3D,MAAMC,SAAS,GAAGH,SAAS,CAACI,IAAI,IAAI,IAAI;;EAExC;EACA,MAAMC,gBAAgB,GAAG,CAAC,MAAM;IAC9B,IAAI;MACF,MAAMC,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAC7C,OAAOF,QAAQ,GAAGG,IAAI,CAACC,KAAK,CAACJ,QAAQ,CAAC,GAAG,IAAI;IAC/C,CAAC,CAAC,MAAM;MACN,OAAO,IAAI;IACb;EACF,CAAC,EAAE,CAAC;EAEJ,MAAMK,SAAS,GAAG,CAAC,MAAM;IACvB,IAAI;MACF,MAAMC,KAAK,GAAGL,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAII,KAAK,EAAE;QACT,MAAMC,OAAO,GAAGJ,IAAI,CAACC,KAAK,CAACI,IAAI,CAACF,KAAK,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrD,OAAOF,OAAO;MAChB;MACA,OAAO,IAAI;IACb,CAAC,CAAC,MAAM;MACN,OAAO,IAAI;IACb;EACF,CAAC,EAAE,CAAC;;EAEJ;EACA,MAAMT,IAAI,GAAGD,SAAS,IAAIE,gBAAgB,IAAIM,SAAS;;EAEvD;EACA,MAAM,CAACK,YAAY,EAAEC,eAAe,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;;EAEtD;EACAkD,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;IACnCC,KAAK,EAAEjB,SAAS;IAChBI,YAAY,EAAEF,gBAAgB;IAC9BO,KAAK,EAAED,SAAS;IAChBU,KAAK,EAAEjB;EACT,CAAC,CAAC;;EAEF;EACA,IAAIA,IAAI,IAAI,CAACY,YAAY,EAAE;IACzBE,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEf,IAAI,CAACkB,MAAM,CAAC;EACvD;EACA,MAAMC,QAAQ,GAAGjD,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACkD,WAAW,EAAEC,cAAc,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC0D,OAAO,EAAEC,UAAU,CAAC,GAAG3D,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC4D,eAAe,EAAEC,kBAAkB,CAAC,GAAG7D,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC8D,QAAQ,EAAEC,WAAW,CAAC,GAAG/D,QAAQ,CAAC,QAAQ,CAAC;EAClD,MAAM,CAACgE,SAAS,EAAEC,YAAY,CAAC,GAAGjE,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACkE,cAAc,EAAEC,iBAAiB,CAAC,GAAGnE,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAACoE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrE,QAAQ,CAAC,EAAE,CAAC;EAE9D,MAAM,CAACsE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGvE,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACwE,WAAW,EAAEC,cAAc,CAAC,GAAGzE,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC0E,cAAc,EAAEC,iBAAiB,CAAC,GAAG3E,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC4E,cAAc,EAAEC,iBAAiB,CAAC,GAAG7E,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC8E,YAAY,EAAEC,eAAe,CAAC,GAAG/E,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAACgF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjF,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACkF,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGnF,QAAQ,CAAC,KAAK,CAAC;;EAErE;EACA,MAAMoF,UAAU,GAAGlF,MAAM,CAAC,CAAC,CAAC,CAAC;EAC7B,MAAMmF,SAAS,GAAGnF,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMoF,cAAc,GAAGpF,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMqF,aAAa,GAAGrF,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMsF,WAAW,GAAGtF,MAAM,CAAC,IAAI,CAAC;;EAEhC;EACA,MAAMuF,kBAAkB,GAAG,CACzB,qDAAqD,EACrD,6DAA6D,EAC7D,8DAA8D,EAC9D,wDAAwD,EACxD,4DAA4D,EAC5D,2DAA2D,EAC3D,yDAAyD,EACzD,6FAA6F,EAC7F,oDAAoD,EACpD,yDAAyD,CAC1D;;EAED;EACA,MAAMC,YAAY,GAAG;IACnBC,MAAM,EAAE;MACNC,GAAG,EAAE,KAAK;MACVC,KAAK,EAAE,wDAAwD;MAC/DC,OAAO,EAAE,oEAAoE;MAC7EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,yBAAyB;MACtCC,IAAI,EAAE,oBAAoB;MAC1BC,IAAI,EAAE1F,OAAO;MACb2F,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,kBAAkB;MAC/BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,aAAa;MACrBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,CAAC;MAAE;MAChBC,YAAY,EAAE,KAAK;MACnBC,QAAQ,EAAE;IACZ,CAAC;IACDC,SAAS,EAAE;MACThB,GAAG,EAAE,KAAK;MACVC,KAAK,EAAE,yDAAyD;MAChEC,OAAO,EAAE,uEAAuE;MAChFC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,yBAAyB;MACtCC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAElF,SAAS;MACfmF,KAAK,EAAE,WAAW;MAClBC,WAAW,EAAE,gBAAgB;MAC7BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,mBAAmB;MAC3BC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,KAAK;MAClBC,YAAY,EAAE,KAAK;MACnBC,QAAQ,EAAE;IACZ,CAAC;IACDE,OAAO,EAAE;MACPjB,GAAG,EAAE,KAAK;MACVC,KAAK,EAAE,yDAAyD;MAChEC,OAAO,EAAE,qEAAqE;MAC9EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,wBAAwB;MACrCC,IAAI,EAAE,oBAAoB;MAC1BC,IAAI,EAAEhF,QAAQ;MACdiF,KAAK,EAAE,SAAS;MAChBC,WAAW,EAAE,cAAc;MAC3BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,eAAe;MACvBC,UAAU,EAAE,KAAK;MACjBC,WAAW,EAAE,KAAK;MAClBC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE;IACZ,CAAC;IACDG,QAAQ,EAAE;MACRlB,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,uDAAuD;MAC9DC,OAAO,EAAE,oEAAoE;MAC7EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,0BAA0B;MACvCC,IAAI,EAAE,qBAAqB;MAC3BC,IAAI,EAAEjF,OAAO;MACbkF,KAAK,EAAE,UAAU;MACjBC,WAAW,EAAE,UAAU;MACvBC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,gBAAgB;MACxBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,KAAK;MAClBC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE;IACZ,CAAC;IACDI,IAAI,EAAE;MACJnB,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,yDAAyD;MAChEC,OAAO,EAAE,wEAAwE;MACjFC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,wBAAwB;MACrCC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE3F,QAAQ;MACd4F,KAAK,EAAE,MAAM;MACbC,WAAW,EAAE,SAAS;MACtBC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,WAAW;MACnBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,IAAI;MACjBC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE;IACZ,CAAC;IACDK,MAAM,EAAE;MACNpB,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,sDAAsD;MAC7DC,OAAO,EAAE,oEAAoE;MAC7EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,0BAA0B;MACvCC,IAAI,EAAE,oBAAoB;MAC1BC,IAAI,EAAEpF,OAAO;MACbqF,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,WAAW;MACxBC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,gBAAgB;MACxBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,IAAI;MACjBC,YAAY,EAAE,GAAG;MACjBC,QAAQ,EAAE;IACZ,CAAC;IACDM,MAAM,EAAE;MACNrB,GAAG,EAAE,GAAG;MACRC,KAAK,EAAE,4DAA4D;MACnEC,OAAO,EAAE,wEAAwE;MACjFC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,yBAAyB;MACtCC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAEzF,MAAM;MACZ0F,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,UAAU;MACvBC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,aAAa;MACrBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,IAAI;MACjBC,YAAY,EAAE,GAAG;MACjBC,QAAQ,EAAE;IACZ,CAAC;IACDO,MAAM,EAAE;MACNtB,GAAG,EAAE,CAAC;MACNC,KAAK,EAAE,yDAAyD;MAChEC,OAAO,EAAE,uEAAuE;MAChFC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,wBAAwB;MACrCC,IAAI,EAAE,qBAAqB;MAC3BC,IAAI,EAAEnF,QAAQ;MACdoF,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,cAAc;MAC3BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,aAAa;MACrBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,GAAG;MAChBC,YAAY,EAAE,CAAC;MAAE;MACjBC,QAAQ,EAAE;IACZ;EACF,CAAC;;EAED;EACA,MAAMQ,aAAa,GAAIC,EAAE,IAAK;IAC5B,KAAK,MAAM,CAACC,MAAM,EAAEC,MAAM,CAAC,IAAIC,MAAM,CAACC,OAAO,CAAC9B,YAAY,CAAC,EAAE;MAC3D,IAAI0B,EAAE,IAAIE,MAAM,CAAC1B,GAAG,EAAE,OAAO;QAAEyB,MAAM;QAAE,GAAGC;MAAO,CAAC;IACpD;IACA,OAAO;MAAED,MAAM,EAAE,QAAQ;MAAE,GAAG3B,YAAY,CAACwB;IAAO,CAAC;EACrD,CAAC;;EAED;EACA,MAAMO,kBAAkB,GAAIvF,KAAK,IAAK;IACpC,MAAMwF,OAAO,GAAG,CAAC,CAAC;IAElBxF,KAAK,CAACyF,OAAO,CAACvF,IAAI,IAAI;MACpB,MAAMwF,UAAU,GAAGT,aAAa,CAAC/E,IAAI,CAACyF,OAAO,CAAC;MAC9C,IAAI,CAACH,OAAO,CAACE,UAAU,CAACP,MAAM,CAAC,EAAE;QAC/BK,OAAO,CAACE,UAAU,CAACP,MAAM,CAAC,GAAG;UAC3BC,MAAM,EAAEM,UAAU;UAClB1F,KAAK,EAAE;QACT,CAAC;MACH;MACAwF,OAAO,CAACE,UAAU,CAACP,MAAM,CAAC,CAACnF,KAAK,CAAC4F,IAAI,CAAC;QACpC,GAAG1F,IAAI;QACP2F,IAAI,EAAEH,UAAU,CAAC;MACnB,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACAL,MAAM,CAACS,IAAI,CAACN,OAAO,CAAC,CAACC,OAAO,CAACM,SAAS,IAAI;MACxCP,OAAO,CAACO,SAAS,CAAC,CAAC/F,KAAK,CAACgG,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACP,OAAO,GAAGM,CAAC,CAACN,OAAO,CAAC;IAChE,CAAC,CAAC;IAEF,OAAOH,OAAO;EAChB,CAAC;;EAED;EACA,MAAMW,wBAAwB,GAAGA,CAACC,QAAQ,EAAEC,WAAW,KAAK;IAC1D,IAAI,CAACA,WAAW,EAAE,OAAO,IAAI;IAE7B,MAAMX,UAAU,GAAGT,aAAa,CAACoB,WAAW,CAACV,OAAO,IAAI,CAAC,CAAC;IAC1D,MAAMrD,WAAW,GAAG8D,QAAQ,CAACE,MAAM,CAACpG,IAAI,IAAI;MAC1C,MAAMiF,MAAM,GAAGF,aAAa,CAAC/E,IAAI,CAACyF,OAAO,CAAC;MAC1C,OAAOR,MAAM,CAACA,MAAM,KAAKO,UAAU,CAACP,MAAM;IAC5C,CAAC,CAAC,CAACa,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACP,OAAO,GAAGM,CAAC,CAACN,OAAO,CAAC;IAExC,OAAO;MACLR,MAAM,EAAEO,UAAU;MAClB1F,KAAK,EAAEsC,WAAW;MAClBiE,QAAQ,EAAEjE,WAAW,CAACkE,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKL,WAAW,CAACK,GAAG,CAAC,GAAG,CAAC;MACnEC,aAAa,EAAErE,WAAW,CAACsE;IAC7B,CAAC;EACH,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAId,SAAS,IAAK;IAAA,IAAAe,qBAAA;IACxC9F,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE8E,SAAS,CAAC;;IAE7C;IACApD,iBAAiB,CAACoD,SAAS,CAAC;IAC5BtD,iBAAiB,CAAC,IAAI,CAAC;IACvBF,cAAc,CAAC,EAAAuE,qBAAA,GAAAlE,YAAY,CAACmD,SAAS,CAAC,cAAAe,qBAAA,uBAAvBA,qBAAA,CAAyB9G,KAAK,KAAI,EAAE,CAAC;;IAEpD;IACA+G,UAAU,CAAC,MAAM;MACf,MAAMC,aAAa,GAAGC,QAAQ,CAACC,aAAa,CAAE,iBAAgBnB,SAAU,IAAG,CAAC,IACvDkB,QAAQ,CAACE,cAAc,CAAE,UAASpB,SAAU,EAAC,CAAC,IAC9C7C,UAAU,CAACkE,OAAO,CAACrB,SAAS,CAAC;MAElD,IAAIiB,aAAa,EAAE;QACjBA,aAAa,CAACK,cAAc,CAAC;UAC3BC,QAAQ,EAAE,QAAQ;UAClBC,KAAK,EAAE,QAAQ;UACfC,MAAM,EAAE;QACV,CAAC,CAAC;;QAEF;QACAR,aAAa,CAACS,KAAK,CAACC,SAAS,GAAG,aAAa;QAC7CV,aAAa,CAACS,KAAK,CAACE,UAAU,GAAG,eAAe;QAChDX,aAAa,CAACS,KAAK,CAACG,SAAS,GAAG,kCAAkC;QAElEb,UAAU,CAAC,MAAM;UACfC,aAAa,CAACS,KAAK,CAACC,SAAS,GAAG,UAAU;UAC1CV,aAAa,CAACS,KAAK,CAACG,SAAS,GAAG,EAAE;QACpC,CAAC,EAAE,GAAG,CAAC;MACT;IACF,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,WAAW,GAAG,CAAC,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;IACxG,OAAOA,WAAW,CAACxB,MAAM,CAACnB,MAAM,IAAIvC,YAAY,CAACuC,MAAM,CAAC,IAAIvC,YAAY,CAACuC,MAAM,CAAC,CAACnF,KAAK,CAAC4G,MAAM,GAAG,CAAC,CAAC;EACpG,CAAC;;EAID;EACA,MAAMmB,gBAAgB,GAAG,MAAAA,CAAOC,YAAY,GAAG,KAAK,KAAK;IACvD,IAAI;MACFvG,UAAU,CAAC,IAAI,CAAC;MAChBT,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAE+G,YAAY,GAAG,iBAAiB,GAAG,EAAE,CAAC;;MAE7F;MACA,IAAI;QACFhH,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;QAC5C,MAAMgH,qBAAqB,GAAG,MAAM7I,gBAAgB,CAAC;UACnD8I,KAAK,EAAE,IAAI;UACXC,WAAW,EAAE,CAAAjI,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkI,KAAK,KAAI,KAAK;UACjCC,eAAe,EAAE,KAAK;UACtB;UACA,IAAIL,YAAY,IAAI;YAAEM,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC;UAAE,CAAC;QACxC,CAAC,CAAC;QAEFxH,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEgH,qBAAqB,CAAC;QAEhE,IAAIA,qBAAqB,IAAIA,qBAAqB,CAACQ,OAAO,IAAIR,qBAAqB,CAACS,IAAI,EAAE;UACxF1H,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;;UAEhD;UACA,MAAM0H,YAAY,GAAGV,qBAAqB,CAACS,IAAI,CAACpC,MAAM,CAAClG,QAAQ,IAC5DA,QAAQ,CAACuF,OAAO,IAAIvF,QAAQ,CAACuF,OAAO,GAAG,CAAC,IACxCvF,QAAQ,CAACwI,iBAAiB,IAAIxI,QAAQ,CAACwI,iBAAiB,GAAG,CAC9D,CAAC;;UAED;UACA5H,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE0H,YAAY,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAACrC,CAAC,KAAK;YAC/EC,GAAG,EAAED,CAAC,CAACC,GAAG;YACVqC,IAAI,EAAEtC,CAAC,CAACsC,IAAI;YACZC,YAAY,EAAEvC,CAAC,CAACuC,YAAY;YAC5BC,cAAc,EAAExC,CAAC,CAACwC,cAAc;YAChCC,cAAc,EAAE,CAAC,EAAEzC,CAAC,CAACuC,YAAY,IAAIvC,CAAC,CAACwC,cAAc;UACvD,CAAC,CAAC,CAAC,CAAC;UAEJ,MAAME,eAAe,GAAGR,YAAY,CAACG,GAAG,CAAC,CAAC1I,QAAQ,EAAEgJ,KAAK,MAAM;YAC7D1C,GAAG,EAAEtG,QAAQ,CAACsG,GAAG;YACjBqC,IAAI,EAAE3I,QAAQ,CAAC2I,IAAI,IAAI,oBAAoB;YAC3CM,KAAK,EAAEjJ,QAAQ,CAACiJ,KAAK,IAAI,EAAE;YAC3BC,KAAK,EAAElJ,QAAQ,CAACkJ,KAAK,IAAI,EAAE;YAC3BlB,KAAK,EAAEhI,QAAQ,CAACgI,KAAK,IAAI,EAAE;YAC3Ba,cAAc,EAAE7I,QAAQ,CAAC4I,YAAY,IAAI5I,QAAQ,CAAC6I,cAAc,IAAI,EAAE;YACtED,YAAY,EAAE5I,QAAQ,CAAC4I,YAAY,IAAI5I,QAAQ,CAAC6I,cAAc,IAAI,EAAE;YACpEtD,OAAO,EAAEvF,QAAQ,CAACuF,OAAO,IAAI,CAAC;YAC9BiD,iBAAiB,EAAExI,QAAQ,CAACwI,iBAAiB,IAAI,CAAC;YAClDW,YAAY,EAAEnJ,QAAQ,CAACmJ,YAAY,IAAI,CAAC;YACxCC,aAAa,EAAEpJ,QAAQ,CAACoJ,aAAa,IAAI,CAAC;YAC1CC,UAAU,EAAErJ,QAAQ,CAACqJ,UAAU,IAAI,CAAC;YACpCC,kBAAkB,EAAEtJ,QAAQ,CAACsJ,kBAAkB,IAAI,MAAM;YACzDC,IAAI,EAAEP,KAAK,GAAG,CAAC;YACfvD,IAAI,EAAEZ,aAAa,CAAC7E,QAAQ,CAACuF,OAAO,IAAI,CAAC,CAAC;YAC1CiE,UAAU,EAAE,IAAI;YAChBC,YAAY,EAAEzJ,QAAQ,CAACyJ,YAAY,IAAI,CAAC;YACxC;YACAC,YAAY,EAAE1J,QAAQ,CAAC0J,YAAY,IAAI,CAAC;YACxCC,aAAa,EAAE3J,QAAQ,CAAC2J,aAAa,IAAI,GAAG;YAC5CC,UAAU,EAAE5J,QAAQ,CAAC4J,UAAU,IAAI,CAAC;YACpCC,QAAQ,EAAE7J,QAAQ,CAAC6J,QAAQ,IAAI,CAAC;YAChCC,YAAY,EAAE9J,QAAQ,CAAC8J,YAAY,IAAI,EAAE;YACzCC,UAAU,EAAE;UACd,CAAC,CAAC,CAAC;;UAEH;UACAnJ,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEkI,eAAe,CAACN,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAACrC,CAAC,KAAK;YAC/EC,GAAG,EAAED,CAAC,CAACC,GAAG;YACVqC,IAAI,EAAEtC,CAAC,CAACsC,IAAI;YACZC,YAAY,EAAEvC,CAAC,CAACuC,YAAY;YAC5BC,cAAc,EAAExC,CAAC,CAACwC,cAAc;YAChCC,cAAc,EAAE,CAAC,EAAEzC,CAAC,CAACuC,YAAY,IAAIvC,CAAC,CAACwC,cAAc;UACvD,CAAC,CAAC,CAAC,CAAC;UAEJ1H,cAAc,CAAC4H,eAAe,CAAC;;UAE/B;UACA,MAAMiB,aAAa,GAAGjB,eAAe,CAAC3C,SAAS,CAAC6D,IAAI,IAAIA,IAAI,CAAC3D,GAAG,MAAKxG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwG,GAAG,EAAC;UAC/E/E,kBAAkB,CAACyI,aAAa,IAAI,CAAC,GAAGA,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC;;UAEjE;UACA,IAAIlK,IAAI,EAAE;YACR,MAAMoK,cAAc,GAAGnE,wBAAwB,CAACgD,eAAe,EAAEjJ,IAAI,CAAC;YACtEmC,oBAAoB,CAACiI,cAAc,CAAC;YACpC/H,cAAc,CAAC,CAAA+H,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEtK,KAAK,KAAI,EAAE,CAAC;UAC7C;;UAEA;UACA,MAAMuK,OAAO,GAAGhF,kBAAkB,CAAC4D,eAAe,CAAC;UACnDtG,eAAe,CAAC0H,OAAO,CAAC;UAExB9I,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;MACF,CAAC,CAAC,OAAO+I,OAAO,EAAE;QAChBxJ,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEuJ,OAAO,CAAC;MACpE;;MAEA;MACAxJ,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;MAE1D,IAAIwJ,eAAe,EAAEC,aAAa;MAElC,IAAI;QACF1J,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;QACpDwJ,eAAe,GAAG,MAAMtL,uBAAuB,CAAC,CAAC;QACjD6B,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;QACvCyJ,aAAa,GAAG,MAAMpL,WAAW,CAAC,CAAC;MACrC,CAAC,CAAC,OAAOqL,KAAK,EAAE;QACd3J,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE0J,KAAK,CAAC;QACnD,IAAI;UACFD,aAAa,GAAG,MAAMpL,WAAW,CAAC,CAAC;QACrC,CAAC,CAAC,OAAOsL,SAAS,EAAE;UAClB5J,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE2J,SAAS,CAAC;QACpD;MACF;MAEA,IAAIzB,eAAe,GAAG,EAAE;MAExB,IAAIuB,aAAa,IAAIA,aAAa,CAACjC,OAAO,IAAIiC,aAAa,CAAChC,IAAI,EAAE;QAChE1H,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;;QAEhD;QACA,MAAM4J,cAAc,GAAG,CAAC,CAAC;QACzB,IAAIJ,eAAe,IAAIA,eAAe,CAAChC,OAAO,IAAIgC,eAAe,CAAC/B,IAAI,EAAE;UACtE+B,eAAe,CAAC/B,IAAI,CAACjD,OAAO,CAAC4E,IAAI,IAAI;YAAA,IAAAS,UAAA;YACnC,MAAM1J,MAAM,GAAG,EAAA0J,UAAA,GAAAT,IAAI,CAACnK,IAAI,cAAA4K,UAAA,uBAATA,UAAA,CAAWpE,GAAG,KAAI2D,IAAI,CAACjJ,MAAM;YAC5C,IAAIA,MAAM,EAAE;cACVyJ,cAAc,CAACzJ,MAAM,CAAC,GAAGiJ,IAAI,CAACU,OAAO,IAAI,EAAE;YAC7C;UACF,CAAC,CAAC;QACJ;QAEA5B,eAAe,GAAGuB,aAAa,CAAChC,IAAI,CACjCpC,MAAM,CAAClG,QAAQ,IAAIA,QAAQ,IAAIA,QAAQ,CAACsG,GAAG,CAAC,CAAC;QAAA,CAC7CoC,GAAG,CAAC,CAAC1I,QAAQ,EAAEgJ,KAAK,KAAK;UACxB;UACA,MAAM4B,WAAW,GAAGH,cAAc,CAACzK,QAAQ,CAACsG,GAAG,CAAC,IAAI,EAAE;;UAEtD;UACA,IAAIuE,YAAY,GAAGD,WAAW,CAACpE,MAAM,IAAIxG,QAAQ,CAACwI,iBAAiB,IAAI,CAAC;UACxE,IAAIsC,UAAU,GAAGF,WAAW,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEC,MAAM,KAAKD,GAAG,IAAIC,MAAM,CAACC,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;UAClF,IAAI/B,YAAY,GAAG0B,YAAY,GAAG,CAAC,GAAGM,IAAI,CAACC,KAAK,CAACN,UAAU,GAAGD,YAAY,CAAC,GAAG7K,QAAQ,CAACmJ,YAAY,IAAI,CAAC;;UAExG;UACA,IAAI,CAACyB,WAAW,CAACpE,MAAM,IAAIxG,QAAQ,CAACqL,WAAW,EAAE;YAC/C;YACA,MAAMC,gBAAgB,GAAGH,IAAI,CAACI,GAAG,CAAC,CAAC,EAAEJ,IAAI,CAACK,KAAK,CAACxL,QAAQ,CAACqL,WAAW,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;YAC9E,MAAMI,gBAAgB,GAAGN,IAAI,CAAC7H,GAAG,CAAC,EAAE,EAAE6H,IAAI,CAACI,GAAG,CAAC,EAAE,EAAE,EAAE,GAAIvL,QAAQ,CAACqL,WAAW,GAAGC,gBAAgB,GAAG,EAAG,CAAC,CAAC,CAAC,CAAC;;YAE1GT,YAAY,GAAGS,gBAAgB;YAC/BnC,YAAY,GAAGgC,IAAI,CAACC,KAAK,CAACK,gBAAgB,CAAC;YAC3CX,UAAU,GAAGK,IAAI,CAACC,KAAK,CAACjC,YAAY,GAAG0B,YAAY,CAAC;YAEpDjK,OAAO,CAACC,GAAG,CAAE,0BAAyBb,QAAQ,CAAC2I,IAAK,KAAI2C,gBAAiB,aAAYG,gBAAiB,cAAazL,QAAQ,CAACqL,WAAY,SAAQ,CAAC;UACnJ;;UAEA;UACA,IAAI9F,OAAO,GAAGvF,QAAQ,CAACuF,OAAO,IAAI,CAAC;UAEnC,IAAI,CAACA,OAAO,EAAE;YACZ;YACA,IAAIvF,QAAQ,CAACqL,WAAW,EAAE;cACxB;cACA9F,OAAO,GAAG4F,IAAI,CAACK,KAAK,CAClBxL,QAAQ,CAACqL,WAAW;cAAG;cACtBR,YAAY,GAAG,EAAG;cAAG;cACrB1B,YAAY,GAAG,EAAE,GAAG0B,YAAY,GAAG,EAAE,GAAG,CAAC,CAAC;cAAG;cAC7C1B,YAAY,GAAG,EAAE,GAAG0B,YAAY,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;cAC9C,CAAC;YACH,CAAC,MAAM,IAAIA,YAAY,GAAG,CAAC,EAAE;cAC3B;cACAtF,OAAO,GAAG4F,IAAI,CAACK,KAAK,CACjBrC,YAAY,GAAG0B,YAAY,GAAG,CAAC;cAAI;cACnCA,YAAY,GAAG,EAAG;cAAG;cACrB1B,YAAY,GAAG,EAAE,GAAG0B,YAAY,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;cAC9C,CAAC;YACH;UACF;;UAEA;UACA,IAAIzB,aAAa,GAAGpJ,QAAQ,CAACoJ,aAAa,IAAI,CAAC;UAC/C,IAAIC,UAAU,GAAGrJ,QAAQ,CAACqJ,UAAU,IAAI,CAAC;UAEzC,IAAIuB,WAAW,CAACpE,MAAM,GAAG,CAAC,EAAE;YAC1B;YACA,IAAIkF,UAAU,GAAG,CAAC;YAClBd,WAAW,CAACvF,OAAO,CAAC4F,MAAM,IAAI;cAC5B,IAAIA,MAAM,CAACC,KAAK,IAAI,EAAE,EAAE;gBAAE;gBACxBQ,UAAU,EAAE;gBACZrC,UAAU,GAAG8B,IAAI,CAACI,GAAG,CAAClC,UAAU,EAAEqC,UAAU,CAAC;cAC/C,CAAC,MAAM;gBACLA,UAAU,GAAG,CAAC;cAChB;YACF,CAAC,CAAC;YACFtC,aAAa,GAAGsC,UAAU;UAC5B,CAAC,MAAM,IAAI1L,QAAQ,CAACqL,WAAW,IAAI,CAACjC,aAAa,EAAE;YACjD;YACA,MAAMuC,aAAa,GAAGd,YAAY,GAAG,CAAC,GAAG7K,QAAQ,CAACqL,WAAW,GAAGR,YAAY,GAAG,CAAC;YAChF,IAAIc,aAAa,GAAG,EAAE,EAAE;cACtBvC,aAAa,GAAG+B,IAAI,CAAC7H,GAAG,CAACuH,YAAY,EAAEM,IAAI,CAACK,KAAK,CAACG,aAAa,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;cACxEtC,UAAU,GAAG8B,IAAI,CAACI,GAAG,CAACnC,aAAa,EAAE+B,IAAI,CAACK,KAAK,CAACG,aAAa,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YACxE;UACF;;UAEA,OAAO;YACLrF,GAAG,EAAEtG,QAAQ,CAACsG,GAAG;YACjBqC,IAAI,EAAE3I,QAAQ,CAAC2I,IAAI,IAAI,oBAAoB;YAC3CM,KAAK,EAAEjJ,QAAQ,CAACiJ,KAAK,IAAI,EAAE;YAC3BC,KAAK,EAAElJ,QAAQ,CAACkJ,KAAK,IAAI,EAAE;YAC3BlB,KAAK,EAAEhI,QAAQ,CAACgI,KAAK,IAAI,EAAE;YAC3Ba,cAAc,EAAE7I,QAAQ,CAAC4I,YAAY,IAAI5I,QAAQ,CAAC6I,cAAc,IAAI,EAAE;YACtED,YAAY,EAAE5I,QAAQ,CAAC4I,YAAY,IAAI5I,QAAQ,CAAC6I,cAAc,IAAI,EAAE;YACpEtD,OAAO,EAAEA,OAAO;YAChBiD,iBAAiB,EAAEqC,YAAY;YAC/B1B,YAAY,EAAEA,YAAY;YAC1BC,aAAa,EAAEA,aAAa;YAC5BC,UAAU,EAAEA,UAAU;YACtBC,kBAAkB,EAAEtJ,QAAQ,CAACsJ,kBAAkB,IAAI,MAAM;YACzDC,IAAI,EAAEP,KAAK,GAAG,CAAC;YACfvD,IAAI,EAAEZ,aAAa,CAACU,OAAO,CAAC;YAC5BiE,UAAU,EAAE,IAAI;YAChB;YACAoC,cAAc,EAAE5L,QAAQ,CAACqL,WAAW,IAAI,CAAC;YACzCQ,UAAU,EAAEjB,WAAW,CAACpE,MAAM,GAAG,CAAC;YAClCuD,UAAU,EAAEa,WAAW,CAACpE,MAAM,GAAG,CAAC,GAAG,SAAS,GAAGxG,QAAQ,CAACqL,WAAW,GAAG,eAAe,GAAG;UAC5F,CAAC;QACH,CAAC,CAAC;;QAEJ;QACAtC,eAAe,CAACnD,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACP,OAAO,GAAGM,CAAC,CAACN,OAAO,CAAC;;QAErD;QACAwD,eAAe,CAAC1D,OAAO,CAAC,CAACvF,IAAI,EAAEkJ,KAAK,KAAK;UACvClJ,IAAI,CAACyJ,IAAI,GAAGP,KAAK,GAAG,CAAC;QACvB,CAAC,CAAC;QAEF7H,cAAc,CAAC4H,eAAe,CAAC;;QAE/B;QACA,IAAI5C,QAAQ,GAAG,CAAC,CAAC;QACjB,IAAIrG,IAAI,EAAE;UACR;UACAqG,QAAQ,GAAG4C,eAAe,CAAC3C,SAAS,CAAC6D,IAAI,IAAIA,IAAI,CAAC3D,GAAG,KAAKxG,IAAI,CAACwG,GAAG,CAAC;;UAEnE;UACA,IAAIH,QAAQ,KAAK,CAAC,CAAC,EAAE;YACnBA,QAAQ,GAAG4C,eAAe,CAAC3C,SAAS,CAAC6D,IAAI,IAAI6B,MAAM,CAAC7B,IAAI,CAAC3D,GAAG,CAAC,KAAKwF,MAAM,CAAChM,IAAI,CAACwG,GAAG,CAAC,CAAC;UACrF;;UAEA;UACA,IAAIH,QAAQ,KAAK,CAAC,CAAC,IAAIrG,IAAI,CAAC6I,IAAI,EAAE;YAChCxC,QAAQ,GAAG4C,eAAe,CAAC3C,SAAS,CAAC6D,IAAI,IAAIA,IAAI,CAACtB,IAAI,KAAK7I,IAAI,CAAC6I,IAAI,CAAC;UACvE;QACF;QAEApH,kBAAkB,CAAC4E,QAAQ,IAAI,CAAC,GAAGA,QAAQ,GAAG,CAAC,GAAG,IAAI,CAAC;;QAEvD;QACA,IAAIrG,IAAI,EAAE;UACR,MAAMoK,cAAc,GAAGnE,wBAAwB,CAACgD,eAAe,EAAEjJ,IAAI,CAAC;UACtEmC,oBAAoB,CAACiI,cAAc,CAAC;UACpC/H,cAAc,CAAC,CAAA+H,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEtK,KAAK,KAAI,EAAE,CAAC;QAC7C;;QAEA;QACA,MAAMuK,OAAO,GAAGhF,kBAAkB,CAAC4D,eAAe,CAAC;QACnDtG,eAAe,CAAC0H,OAAO,CAAC;;QAExB;QACA,IAAI4B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;UAC1CrL,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE;YAC7CoF,WAAW,EAAEnG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6I,IAAI;YACvB3H,MAAM,EAAElB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwG,GAAG;YACjB4F,UAAU,EAAE,QAAOpM,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwG,GAAG;YAC5B6F,OAAO,EAAE,CAAArM,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsM,IAAI,MAAK,OAAO,KAAItM,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqM,OAAO;YAChDE,MAAM,EAAEvM,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyF,OAAO;YACrByE,aAAa,EAAE7D,QAAQ;YACvBmG,gBAAgB,EAAEnG,QAAQ,IAAI,CAAC,GAAGA,QAAQ,GAAG,CAAC,GAAG,IAAI;YACrDoG,gBAAgB,EAAExD,eAAe,CAACvC,MAAM;YACxCgG,eAAe,EAAEzD,eAAe,CAACN,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAACrC,CAAC,KAAK;cAAEoG,EAAE,EAAEpG,CAAC,CAACC,GAAG;cAAEoG,IAAI,EAAE,OAAOrG,CAAC,CAACC,GAAG;cAAEqC,IAAI,EAAEtC,CAAC,CAACsC;YAAK,CAAC,CAAC,CAAC;YACxGgE,UAAU,EAAE5D,eAAe,CAAC6D,IAAI,CAAC3C,IAAI,IAAIA,IAAI,CAAC3D,GAAG,MAAKxG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwG,GAAG,EAAC;YAChEuG,WAAW,EAAE9D,eAAe,CAAC6D,IAAI,CAAC3C,IAAI,IAAI6B,MAAM,CAAC7B,IAAI,CAAC3D,GAAG,CAAC,KAAKwF,MAAM,CAAChM,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwG,GAAG,CAAC,CAAC;YACjFwG,SAAS,EAAE/D,eAAe,CAAC6D,IAAI,CAAC3C,IAAI,IAAIA,IAAI,CAACtB,IAAI,MAAK7I,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6I,IAAI;UAClE,CAAC,CAAC;QACJ;;QAEA;QACA,MAAMoE,WAAW,GAAG;UAClBpC,OAAO,EAAE5B,eAAe,CAAC7C,MAAM,CAACG,CAAC,IAAIA,CAAC,CAAC0D,UAAU,KAAK,SAAS,CAAC,CAACvD,MAAM;UACvEwG,aAAa,EAAEjE,eAAe,CAAC7C,MAAM,CAACG,CAAC,IAAIA,CAAC,CAAC0D,UAAU,KAAK,eAAe,CAAC,CAACvD,MAAM;UACnFyG,SAAS,EAAElE,eAAe,CAAC7C,MAAM,CAACG,CAAC,IAAIA,CAAC,CAAC0D,UAAU,KAAK,WAAW,CAAC,CAACvD;QACvE,CAAC;QAED5F,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEkI,eAAe,CAACvC,MAAM,EAAE,gBAAgB,CAAC;QACxF5F,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEkM,WAAW,CAAC;QAC5CnM,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEkI,eAAe,CAACN,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAACrC,CAAC,KAAK;UACvEsC,IAAI,EAAEtC,CAAC,CAACsC,IAAI;UACZ7D,EAAE,EAAEuB,CAAC,CAACd,OAAO;UACb2H,OAAO,EAAE7G,CAAC,CAACmC,iBAAiB;UAC5B2E,GAAG,EAAE9G,CAAC,CAAC8C,YAAY;UACnBiE,MAAM,EAAE/G,CAAC,CAAC0D;QACZ,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,MAAM;QACLnJ,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;QACxCM,cAAc,CAAC,EAAE,CAAC;QAClBI,kBAAkB,CAAC,IAAI,CAAC;QACxBtD,OAAO,CAACoP,OAAO,CAAC,0DAA0D,CAAC;MAC7E;IACF,CAAC,CAAC,OAAO9C,KAAK,EAAE;MACd3J,OAAO,CAAC2J,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDtM,OAAO,CAACsM,KAAK,CAAC,8DAA8D,CAAC;IAC/E,CAAC,SAAS;MACRlJ,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMiM,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,EAACxN,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEkB,MAAM,GAAE;MACjBJ,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEf,IAAI,CAAC;MAC3C;IACF;IAEA,IAAI;MACFc,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEf,IAAI,CAACkB,MAAM,CAAC;MAClE,MAAMuM,QAAQ,GAAG,MAAMrO,WAAW,CAAC,CAAC;MACpC0B,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE0M,QAAQ,CAAC;MAEjD,IAAIA,QAAQ,CAAClF,OAAO,EAAE;QACpBzH,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE0M,QAAQ,CAACjF,IAAI,CAAC9B,MAAM,CAAC;QAC1D5F,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEf,IAAI,CAACkB,MAAM,CAAC;QAClDJ,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE0M,QAAQ,CAACjF,IAAI,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAACrC,CAAC,KAAK;UAAEoG,EAAE,EAAEpG,CAAC,CAACC,GAAG;UAAEqC,IAAI,EAAEtC,CAAC,CAACsC;QAAK,CAAC,CAAC,CAAC,CAAC;QAEtG,MAAM3I,QAAQ,GAAGuN,QAAQ,CAACjF,IAAI,CAACsE,IAAI,CAACvG,CAAC,IAAIyF,MAAM,CAACzF,CAAC,CAACC,GAAG,CAAC,KAAKwF,MAAM,CAAChM,IAAI,CAACkB,MAAM,CAAC,CAAC;QAC/E,IAAIhB,QAAQ,EAAE;UACZY,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEb,QAAQ,CAAC;UAChD;UACA,MAAMwN,mBAAmB,GAAG;YAC1B,GAAGxN,QAAQ;YACX6I,cAAc,EAAE7I,QAAQ,CAAC4I,YAAY,IAAI5I,QAAQ,CAAC6I,cAAc,IAAI,EAAE;YACtED,YAAY,EAAE5I,QAAQ,CAAC4I,YAAY,IAAI5I,QAAQ,CAAC6I,cAAc,IAAI;UACpE,CAAC;UACDlI,eAAe,CAAC6M,mBAAmB,CAAC;QACtC,CAAC,MAAM;UACL5M,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;UAC7CD,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;;UAEtD;UACA,MAAM4M,WAAW,GAAGF,QAAQ,CAACjF,IAAI,CAACsE,IAAI,CAACvG,CAAC,IACtCA,CAAC,CAACC,GAAG,KAAKxG,IAAI,CAACkB,MAAM,IACrBqF,CAAC,CAACoG,EAAE,KAAK3M,IAAI,CAACkB,MAAM,IACpB8K,MAAM,CAACzF,CAAC,CAACC,GAAG,CAAC,CAACoH,QAAQ,CAAC5N,IAAI,CAACkB,MAAM,CAAC,IACnC8K,MAAM,CAAChM,IAAI,CAACkB,MAAM,CAAC,CAAC0M,QAAQ,CAACrH,CAAC,CAACC,GAAG,CACpC,CAAC;UAED,IAAImH,WAAW,EAAE;YACf7M,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE4M,WAAW,CAAC;YACjE;YACA,MAAMD,mBAAmB,GAAG;cAC1B,GAAGC,WAAW;cACd5E,cAAc,EAAE4E,WAAW,CAAC7E,YAAY,IAAI6E,WAAW,CAAC5E,cAAc,IAAI,EAAE;cAC5ED,YAAY,EAAE6E,WAAW,CAAC7E,YAAY,IAAI6E,WAAW,CAAC5E,cAAc,IAAI;YAC1E,CAAC;YACDlI,eAAe,CAAC6M,mBAAmB,CAAC;UACtC,CAAC,MAAM;YACL5M,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;UACjD;QACF;MACF,CAAC,MAAM;QACLD,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE0M,QAAQ,CAAC;MAChD;IACF,CAAC,CAAC,OAAOhD,KAAK,EAAE;MACd3J,OAAO,CAAC2J,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD;EACF,CAAC;;EAED;EACA5M,SAAS,CAAC,MAAM;IACd,IAAI,CAAC+C,YAAY,IAAIZ,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEkB,MAAM,IAAIE,WAAW,CAACsF,MAAM,GAAG,CAAC,EAAE;MAC3D5F,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;MACxD,MAAM8M,aAAa,GAAGzM,WAAW,CAAC0L,IAAI,CAACvG,CAAC,IAAIyF,MAAM,CAACzF,CAAC,CAACC,GAAG,CAAC,KAAKwF,MAAM,CAAChM,IAAI,CAACkB,MAAM,CAAC,CAAC;MAClF,IAAI2M,aAAa,EAAE;QACjB/M,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE8M,aAAa,CAAC;QAC3D;QACA,MAAMH,mBAAmB,GAAG;UAC1B,GAAGG,aAAa;UAChB9E,cAAc,EAAE8E,aAAa,CAAC/E,YAAY,IAAI+E,aAAa,CAAC9E,cAAc,IAAI,EAAE;UAChFD,YAAY,EAAE+E,aAAa,CAAC/E,YAAY,IAAI+E,aAAa,CAAC9E,cAAc,IAAI;QAC9E,CAAC;QACDlI,eAAe,CAAC6M,mBAAmB,CAAC;MACtC,CAAC,MAAM;QACL5M,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MACxD;IACF;EACF,CAAC,EAAE,CAACK,WAAW,EAAEpB,IAAI,EAAEY,YAAY,CAAC,CAAC;;EAErC;EACA/C,SAAS,CAAC,MAAM;IACdgK,gBAAgB,CAAC,CAAC;IAClB2F,iBAAiB,CAAC,CAAC,CAAC,CAAC;;IAErB;IACA,MAAMM,WAAW,GAAGzK,kBAAkB,CAACgI,IAAI,CAACK,KAAK,CAACL,IAAI,CAAC0C,MAAM,CAAC,CAAC,GAAG1K,kBAAkB,CAACqD,MAAM,CAAC,CAAC;IAC7FzE,oBAAoB,CAAC6L,WAAW,CAAC;;IAEjC;IACA,MAAME,cAAc,GAAGC,WAAW,CAAC,MAAM;MACvClM,iBAAiB,CAACmM,IAAI,IAAI,CAACA,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;IAC3C,CAAC,EAAE,IAAI,CAAC;;IAER;IACA;IACA;IACA;IACA;;IAEA;IACA,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;MAC9BrN,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;MAC7D8G,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC;IAC1B,CAAC;;IAED;IACA,MAAMuG,mBAAmB,GAAIC,KAAK,IAAK;MACrCvN,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEsN,KAAK,CAACC,MAAM,CAAC;;MAEnE;MACAnO,YAAY,CAACoO,UAAU,CAAC,cAAc,CAAC;MACvCpO,YAAY,CAACoO,UAAU,CAAC,qBAAqB,CAAC;MAC9CpO,YAAY,CAACoO,UAAU,CAAC,iBAAiB,CAAC;;MAE1C;MACA,MAAMC,gBAAgB,GAAG,MAAAA,CAAOC,QAAQ,GAAG,CAAC,KAAK;QAC/C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,QAAQ,EAAEC,CAAC,EAAE,EAAE;UACjC,IAAI;YAAA,IAAAC,aAAA;YACF7N,OAAO,CAACC,GAAG,CAAE,uCAAsC2N,CAAC,GAAG,CAAE,IAAGD,QAAS,GAAE,CAAC;YACxE,MAAM5G,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC;;YAE9B;YACA,IAAI,CAAA8G,aAAA,GAAAN,KAAK,CAACC,MAAM,cAAAK,aAAA,eAAZA,aAAA,CAAcC,UAAU,IAAI5O,IAAI,EAAE;cACpC,MAAM6O,WAAW,GAAGzN,WAAW,CAAC0L,IAAI,CAACvG,CAAC,IAAIyF,MAAM,CAACzF,CAAC,CAACC,GAAG,CAAC,KAAKwF,MAAM,CAAChM,IAAI,CAACwG,GAAG,CAAC,CAAC;cAC7E,IAAIqI,WAAW,IAAIA,WAAW,CAACpJ,OAAO,IAAI4I,KAAK,CAACC,MAAM,CAACM,UAAU,EAAE;gBACjE9N,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;gBACpD;cACF;YACF;;YAEA;YACA,IAAI2N,CAAC,GAAGD,QAAQ,GAAG,CAAC,EAAE;cACpB,MAAM,IAAIK,OAAO,CAACC,OAAO,IAAIlI,UAAU,CAACkI,OAAO,EAAE,IAAI,CAAC,CAAC;YACzD;UACF,CAAC,CAAC,OAAOtE,KAAK,EAAE;YACd3J,OAAO,CAAC2J,KAAK,CAAE,6BAA4BiE,CAAC,GAAG,CAAE,UAAS,EAAEjE,KAAK,CAAC;YAClE,IAAIiE,CAAC,GAAGD,QAAQ,GAAG,CAAC,EAAE;cACpB,MAAM,IAAIK,OAAO,CAACC,OAAO,IAAIlI,UAAU,CAACkI,OAAO,EAAE,IAAI,CAAC,CAAC;YACzD;UACF;QACF;MACF,CAAC;;MAED;MACAlI,UAAU,CAAC,MAAM;QACf2H,gBAAgB,CAAC,CAAC;MACpB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC;IAEDQ,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAEd,iBAAiB,CAAC;IACnDa,MAAM,CAACC,gBAAgB,CAAC,eAAe,EAAEb,mBAAmB,CAAC;IAE7D,OAAO,MAAM;MACXc,aAAa,CAAClB,cAAc,CAAC;MAC7B;MACAgB,MAAM,CAACG,mBAAmB,CAAC,OAAO,EAAEhB,iBAAiB,CAAC;MACtDa,MAAM,CAACG,mBAAmB,CAAC,eAAe,EAAEf,mBAAmB,CAAC;IAClE,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAvQ,SAAS,CAAC,MAAM;IACd,IAAImC,IAAI,IAAI0C,YAAY,IAAIyC,MAAM,CAACS,IAAI,CAAClD,YAAY,CAAC,CAACgE,MAAM,GAAG,CAAC,IAAI,CAAClE,cAAc,EAAE;MACnF;MACA,KAAK,MAAM,CAACqD,SAAS,EAAEuJ,UAAU,CAAC,IAAIjK,MAAM,CAACC,OAAO,CAAC1C,YAAY,CAAC,EAAE;QAClE,MAAM2M,YAAY,GAAGD,UAAU,CAACtP,KAAK,CAACgN,IAAI,CAACvG,CAAC,IAAIyF,MAAM,CAACzF,CAAC,CAACC,GAAG,CAAC,KAAKwF,MAAM,CAAChM,IAAI,CAACwG,GAAG,CAAC,CAAC;QACnF,IAAI6I,YAAY,EAAE;UAChBvO,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE8E,SAAS,CAAC;UACxDpD,iBAAiB,CAACoD,SAAS,CAAC;UAC5BtD,iBAAiB,CAAC,IAAI,CAAC;UACvBF,cAAc,CAAC+M,UAAU,CAACtP,KAAK,CAAC;UAChC;QACF;MACF;IACF;EACF,CAAC,EAAE,CAACE,IAAI,EAAE0C,YAAY,EAAEF,cAAc,CAAC,CAAC;;EAExC;EACA,MAAM8M,aAAa,GAAGlO,WAAW,CAACuH,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAC7C,MAAM4G,eAAe,GAAGnO,WAAW,CAACuH,KAAK,CAAC,CAAC,CAAC;;EAI5C;EACA,MAAM6G,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI,EAACxP,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEwG,GAAG,GAAE,OAAO,IAAI;;IAE3B;IACA,MAAMiJ,UAAU,GAAGH,aAAa,CAACI,IAAI,CAACC,SAAS,IAAI3D,MAAM,CAAC2D,SAAS,CAACnJ,GAAG,CAAC,KAAKwF,MAAM,CAAChM,IAAI,CAACwG,GAAG,CAAC,CAAC;IAC9F,IAAIiJ,UAAU,EAAE;MACd,MAAMG,cAAc,GAAGN,aAAa,CAAChJ,SAAS,CAACqJ,SAAS,IAAI3D,MAAM,CAAC2D,SAAS,CAACnJ,GAAG,CAAC,KAAKwF,MAAM,CAAChM,IAAI,CAACwG,GAAG,CAAC,CAAC,GAAG,CAAC;MAC3G,OAAO;QACLoG,IAAI,EAAE,QAAQ;QACdiD,QAAQ,EAAED,cAAc;QACxB3K,MAAM,EAAE,iBAAiB;QACzBY,SAAS,EAAE;MACb,CAAC;IACH;;IAEA;IACA,KAAK,MAAM,CAACA,SAAS,EAAEuJ,UAAU,CAAC,IAAIjK,MAAM,CAACC,OAAO,CAAC1C,YAAY,CAAC,EAAE;MAAA,IAAAoN,iBAAA;MAClE,MAAMT,YAAY,IAAAS,iBAAA,GAAGV,UAAU,CAACtP,KAAK,cAAAgQ,iBAAA,uBAAhBA,iBAAA,CAAkBhD,IAAI,CAACvG,CAAC,IAAIyF,MAAM,CAACzF,CAAC,CAACC,GAAG,CAAC,KAAKwF,MAAM,CAAChM,IAAI,CAACwG,GAAG,CAAC,CAAC;MACpF,IAAI6I,YAAY,EAAE;QAChB,MAAMQ,QAAQ,GAAGT,UAAU,CAACtP,KAAK,CAACwG,SAAS,CAACC,CAAC,IAAIyF,MAAM,CAACzF,CAAC,CAACC,GAAG,CAAC,KAAKwF,MAAM,CAAChM,IAAI,CAACwG,GAAG,CAAC,CAAC,GAAG,CAAC;QACxF,OAAO;UACLoG,IAAI,EAAE,QAAQ;UACdiD,QAAQ,EAAEA,QAAQ;UAClB5K,MAAM,EAAEmK,UAAU,CAACpL,KAAK;UACxB6B,SAAS,EAAEA,SAAS;UACpBkK,UAAU,EAAEX,UAAU,CAACtP,KAAK,CAAC4G;QAC/B,CAAC;MACH;IACF;IAEA,OAAO,IAAI;EACb,CAAC;EAED,MAAMsJ,cAAc,GAAGR,iBAAiB,CAAC,CAAC;;EAE1C;EACA,MAAMS,aAAa,GAAI/O,MAAM,IAAK;IAChC,OAAOlB,IAAI,IAAIgM,MAAM,CAAC9K,MAAM,CAAC,KAAK8K,MAAM,CAAChM,IAAI,CAACwG,GAAG,CAAC;EACpD,CAAC;;EAED;EACA,MAAM0J,mBAAmB,GAAIhP,MAAM,IAAK;IACtC,OAAO+O,aAAa,CAAC/O,MAAM,CAAC,IAAI,CAAC0B,gBAAgB;EACnD,CAAC;;EAED;EACA,MAAMuN,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAACvN,gBAAgB,EAAE;MACrBC,mBAAmB,CAAC,IAAI,CAAC;MACzB/B,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;IACxD;EACF,CAAC;;EAED;EACAlD,SAAS,CAAC,MAAM;IACdgF,mBAAmB,CAAC,KAAK,CAAC;IAC1BE,sBAAsB,CAAC,KAAK,CAAC,CAAC,CAAC;EACjC,CAAC,EAAE,CAAC/C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwG,GAAG,EAAEhE,cAAc,CAAC,CAAC;;EAE/B;EACA3E,SAAS,CAAC,MAAM;IACdiD,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;MACnCG,MAAM,EAAElB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwG,GAAG;MACjB1D,mBAAmB;MACnBsN,iBAAiB,EAAEhP,WAAW,CAACsF;IACjC,CAAC,CAAC;;IAEF;IACA,IAAI,EAAC1G,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEwG,GAAG,KAAI1D,mBAAmB,IAAI1B,WAAW,CAACsF,MAAM,KAAK,CAAC,EAAE;MACjE5F,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE;QACpCsP,OAAO,EAAE,CAAC,EAACrQ,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEwG,GAAG;QACpB8J,SAAS,EAAExN,mBAAmB;QAC9ByN,OAAO,EAAEnP,WAAW,CAACsF,MAAM,GAAG;MAChC,CAAC,CAAC;MACF;IACF;IAEA,MAAM8J,YAAY,GAAGA,CAAA,KAAM;MACzB1P,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEf,IAAI,CAACwG,GAAG,CAAC;;MAE1D;MACA,MAAMqH,aAAa,GAAGzM,WAAW,CAAC0L,IAAI,CAACvG,CAAC,IAAIyF,MAAM,CAACzF,CAAC,CAACC,GAAG,CAAC,KAAKwF,MAAM,CAAChM,IAAI,CAACwG,GAAG,CAAC,CAAC;MAC/E,IAAI,CAACqH,aAAa,EAAE;QAClB/M,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;QAC/CgC,sBAAsB,CAAC,IAAI,CAAC,CAAC,CAAC;QAC9B;MACF;MAEAjC,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE8M,aAAa,CAACpE,IAAI,CAAC;;MAEvE;MACA,MAAMgG,UAAU,GAAG5B,aAAa,CAACpE,IAAI,IAAI,CAAC;MAC1C3I,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE0O,UAAU,CAAC;MAEhD,IAAIA,UAAU,EAAE;QACd;QACA3O,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;QAChD,MAAM0P,aAAa,GAAG1J,QAAQ,CAACC,aAAa,CAAC,yBAAyB,CAAC;QACvElG,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,CAAC,CAAC0P,aAAa,CAAC;QACxD,IAAIA,aAAa,EAAE;UACjB5J,UAAU,CAAC,MAAM;YACf4J,aAAa,CAACtJ,cAAc,CAAC;cAC3BC,QAAQ,EAAE,QAAQ;cAClBC,KAAK,EAAE,QAAQ;cACfC,MAAM,EAAE;YACV,CAAC,CAAC;YACFxG,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;YACnC;YACA8F,UAAU,CAAC,MAAM;cACfhE,mBAAmB,CAAC,IAAI,CAAC;cACzBE,sBAAsB,CAAC,IAAI,CAAC;cAC5BjC,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;YACxC,CAAC,EAAE,IAAI,CAAC;UACV,CAAC,EAAE,GAAG,CAAC;QACT,CAAC,MAAM;UACLgC,sBAAsB,CAAC,IAAI,CAAC;QAC9B;MACF,CAAC,MAAM;QACL;QACAjC,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEf,IAAI,CAACwG,GAAG,CAAC;QAC7D,MAAMkK,WAAW,GAAG3J,QAAQ,CAACC,aAAa,CAAE,kBAAiBhH,IAAI,CAACwG,GAAI,IAAG,CAAC;QAC1E1F,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE,CAAC,CAAC2P,WAAW,CAAC;QACpD,IAAIA,WAAW,EAAE;UACf7J,UAAU,CAAC,MAAM;YACf6J,WAAW,CAACvJ,cAAc,CAAC;cACzBC,QAAQ,EAAE,QAAQ;cAClBC,KAAK,EAAE,QAAQ;cACfC,MAAM,EAAE;YACV,CAAC,CAAC;YACFxG,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;YAC1C;YACA8F,UAAU,CAAC,MAAM;cACfhE,mBAAmB,CAAC,IAAI,CAAC;cACzBE,sBAAsB,CAAC,IAAI,CAAC;cAC5BjC,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;YACxC,CAAC,EAAE,IAAI,CAAC;UACV,CAAC,EAAE,GAAG,CAAC;QACT,CAAC,MAAM;UACLD,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;UAC9CgC,sBAAsB,CAAC,IAAI,CAAC;QAC9B;MACF;IACF,CAAC;;IAED;IACA,MAAM4N,KAAK,GAAG9J,UAAU,CAAC2J,YAAY,EAAE,IAAI,CAAC;IAC5C,OAAO,MAAMI,YAAY,CAACD,KAAK,CAAC;EAClC,CAAC,EAAE,CAAC3Q,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwG,GAAG,EAAEpF,WAAW,EAAE0B,mBAAmB,CAAC,CAAC;;EAEjD;EACA,MAAM+N,oBAAoB,GAAGA,CAACrH,kBAAkB,EAAEsH,mBAAmB,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,SAAS,GAAG,CAAC,KAAK;IAC1H,MAAM3I,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;IACtB,MAAM6I,OAAO,GAAGJ,mBAAmB,GAAG,IAAIzI,IAAI,CAACyI,mBAAmB,CAAC,GAAG,IAAI;IAE1EhQ,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;MACjCyI,kBAAkB;MAClBsH,mBAAmB;MACnBC,gBAAgB;MAChBC,eAAe;MACfE,OAAO;MACP5I,GAAG;MACH6I,QAAQ,EAAED,OAAO,IAAIA,OAAO,GAAG5I,GAAG;MAClC2I;IACF,CAAC,CAAC;;IAEF;IACA,IAAIzH,kBAAkB,KAAK,QAAQ,IAAIA,kBAAkB,KAAK,SAAS,EAAE;MACvE;MACA,IAAI,CAAC0H,OAAO,IAAIA,OAAO,GAAG5I,GAAG,EAAE;QAC7B;QACA,OAAO;UACL8I,IAAI,EAAE,WAAW;UACjB3N,KAAK,EAAE,SAAS;UAAE;UAClBC,OAAO,EAAE,yBAAyB;UAClCQ,WAAW,EAAE;QACf,CAAC;MACH,CAAC,MAAM;QACL;QACA,OAAO;UACLkN,IAAI,EAAE,SAAS;UACf3N,KAAK,EAAE,SAAS;UAAE;UAClBC,OAAO,EAAE,wBAAwB;UACjCQ,WAAW,EAAE;QACf,CAAC;MACH;IACF,CAAC,MAAM;MACL;MACA,OAAO;QACLkN,IAAI,EAAE,SAAS;QACf3N,KAAK,EAAE,SAAS;QAAE;QAClBC,OAAO,EAAE,wBAAwB;QACjCQ,WAAW,EAAE;MACf,CAAC;IACH;EACF,CAAC;;EAED;EACA,MAAMmN,eAAe,GAAGA,CAAA,kBACtB9R,OAAA;IAAK+R,SAAS,EAAC,2EAA2E;IAAAC,QAAA,eACxFhS,OAAA;MAAK+R,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAE1ChS,OAAA;QAAK+R,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChChS,OAAA;UAAK+R,SAAS,EAAC;QAA6D;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnFpS,OAAA;UAAK+R,SAAS,EAAC;QAAmD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtE,CAAC,eAGNpS,OAAA;QAAK+R,SAAS,EAAC,+CAA+C;QAAAC,QAAA,EAC3D,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC3I,GAAG,CAAEiH,QAAQ,iBACtBtQ,OAAA;UAAoB+R,SAAS,EAAG,eAAczB,QAAQ,KAAK,CAAC,GAAG,SAAS,GAAGA,QAAQ,KAAK,CAAC,GAAG,SAAS,GAAG,SAAU,EAAE;UAAA0B,QAAA,gBAClHhS,OAAA;YAAK+R,SAAS,EAAG;UAAyG;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjIpS,OAAA;YAAK+R,SAAS,EAAC;UAAyD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/EpS,OAAA;YAAK+R,SAAS,EAAC;UAAmD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA,GAHjE9B,QAAQ;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAIb,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNpS,OAAA;QAAK+R,SAAS,EAAC,6BAA6B;QAAAC,QAAA,EACzC,CAAC,GAAGK,KAAK,CAAC,CAAC,CAAC,CAAC,CAAChJ,GAAG,CAAC,CAACiJ,CAAC,EAAEnD,CAAC,kBACtBnP,OAAA;UAAa+R,SAAS,EAAC,yCAAyC;UAAAC,QAAA,eAC9DhS,OAAA;YAAK+R,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1ChS,OAAA;cAAK+R,SAAS,EAAC;YAAoC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1DpS,OAAA;cAAK+R,SAAS,EAAC,QAAQ;cAAAC,QAAA,gBACrBhS,OAAA;gBAAK+R,SAAS,EAAC;cAAmC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzDpS,OAAA;gBAAK+R,SAAS,EAAC;cAA6B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACNpS,OAAA;cAAK+R,SAAS,EAAC;YAA8B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD;QAAC,GAREjD,CAAC;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OASN,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;;EAED;EACA,IAAIrQ,OAAO,IAAIF,WAAW,CAACsF,MAAM,KAAK,CAAC,EAAE;IACvC,oBAAOnH,OAAA,CAAC8R,eAAe;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC5B;EAEA,oBACEpS,OAAA,CAAAE,SAAA;IAAA8R,QAAA,gBACEhS,OAAA;MAAAgS,QAAA,EAAS;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eACVpS,OAAA;MAAK+R,SAAS,EAAC,kIAAkI;MAACQ,OAAO,EAAE3B,eAAgB;MAAAoB,QAAA,GAG1K,CAAC3O,gBAAgB,IAAI5C,IAAI,iBACxBT,OAAA,CAACxB,MAAM,CAACgU,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,IAAI,EAAE;UAAEH,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAC7BZ,SAAS,EAAC,sJAAsJ;QAAAC,QAAA,EACjK;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CACb,eAGDpS,OAAA;QAAK+R,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/ChS,OAAA;UAAK+R,SAAS,EAAC;QAA2H;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjJpS,OAAA;UAAK+R,SAAS,EAAC;QAAgJ;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtKpS,OAAA;UAAK+R,SAAS,EAAC;QAA6I;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnKpS,OAAA;UAAK+R,SAAS,EAAC;QAA8I;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjK,CAAC,eAGNpS,OAAA;QAAK+R,SAAS,EAAC,sDAAsD;QAAAC,QAAA,EAClE,CAAC,GAAGK,KAAK,CAAC,EAAE,CAAC,CAAC,CAAChJ,GAAG,CAAC,CAACiJ,CAAC,EAAEnD,CAAC,kBACvBnP,OAAA,CAACxB,MAAM,CAACgU,GAAG;UAETT,SAAS,EAAC,mDAAmD;UAC7Da,OAAO,EAAE;YACPD,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;YACfG,CAAC,EAAE,CAAC,CAAC,EAAEhH,IAAI,CAAC0C,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,CAAC,CAAC;YACnCkE,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG;UACzB,CAAE;UACFxK,UAAU,EAAE;YACV6K,QAAQ,EAAE,CAAC,GAAGjH,IAAI,CAAC0C,MAAM,CAAC,CAAC,GAAG,CAAC;YAC/BwE,MAAM,EAAEC,QAAQ;YAChBC,KAAK,EAAEpH,IAAI,CAAC0C,MAAM,CAAC,CAAC,GAAG;UACzB,CAAE;UACFxG,KAAK,EAAE;YACLmL,IAAI,EAAG,GAAErH,IAAI,CAAC0C,MAAM,CAAC,CAAC,GAAG,GAAI,GAAE;YAC/B4E,GAAG,EAAG,GAAEtH,IAAI,CAAC0C,MAAM,CAAC,CAAC,GAAG,GAAI;UAC9B;QAAE,GAfGW,CAAC;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgBP,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENpS,OAAA;QAAK+R,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAE5BhS,OAAA,CAACxB,MAAM,CAACgU,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BzK,UAAU,EAAE;YAAE6K,QAAQ,EAAE;UAAI,CAAE;UAC9BhB,SAAS,EAAC,4DAA4D;UAAAC,QAAA,eAEtEhS,OAAA;YAAK+R,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAChChS,OAAA;cAAK+R,SAAS,EAAC,sHAAsH;cAAAC,QAAA,eACnIhS,OAAA;gBAAK+R,SAAS,EAAC,wFAAwF;gBAAAC,QAAA,gBAGrGhS,OAAA,CAACxB,MAAM,CAAC6U,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BhB,OAAO,EAAEA,CAAA,KAAM3Q,QAAQ,CAAC,WAAW,CAAE;kBACrCmQ,SAAS,EAAC,gNAAgN;kBAC1N/J,KAAK,EAAE;oBACLyL,QAAQ,EAAEhE,MAAM,CAACiE,UAAU,GAAG,GAAG,GAAG,MAAM,GAAG;kBAC/C,CAAE;kBAAA1B,QAAA,gBAEFhS,OAAA,CAACd,MAAM;oBAAC6S,SAAS,EAAC;kBAAuB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC5CpS,OAAA;oBAAAgS,QAAA,EAAM;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAGf3B,cAAc,iBACbzQ,OAAA,CAACxB,MAAM,CAACgU,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEC,CAAC,EAAE;kBAAG,CAAE;kBAC/BC,OAAO,EAAE;oBAAEF,OAAO,EAAE,CAAC;oBAAEC,CAAC,EAAE;kBAAE,CAAE;kBAC9BZ,SAAS,EAAC,mJAAmJ;kBAC7J/J,KAAK,EAAE;oBACL2L,UAAU,EAAElD,cAAc,CAACpD,IAAI,KAAK,QAAQ,GACxC,2CAA2C,GAC3C,2CAA2C;oBAC/CnJ,KAAK,EAAEuM,cAAc,CAACpD,IAAI,KAAK,QAAQ,GAAG,SAAS,GAAG,SAAS;oBAC/DlF,SAAS,EAAE,oCAAoC;oBAC/CsL,QAAQ,EAAEhE,MAAM,CAACiE,UAAU,GAAG,GAAG,GAAG,QAAQ,GAAG;kBACjD,CAAE;kBAAA1B,QAAA,gBAEFhS,OAAA,CAACnB,QAAQ;oBAACkT,SAAS,EAAC;kBAAuB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC9CpS,OAAA;oBAAAgS,QAAA,EACGvB,cAAc,CAACpD,IAAI,KAAK,QAAQ,GAC5B,cAAaoD,cAAc,CAACH,QAAS,EAAC,GACtC,GAAEG,cAAc,CAAC/K,MAAO,KAAI+K,cAAc,CAACH,QAAS;kBAAC;oBAAA2B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CACb,EAGA/Q,YAAY,iBACXrB,OAAA,CAACxB,MAAM,CAACgU,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEC,CAAC,EAAE;kBAAG,CAAE;kBAC/BC,OAAO,EAAE;oBAAEF,OAAO,EAAE,CAAC;oBAAEC,CAAC,EAAE;kBAAE,CAAE;kBAC9BZ,SAAS,EAAC,sJAAsJ;kBAAAC,QAAA,eAEhKhS,OAAA;oBAAK+R,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,gBAEtChS,OAAA;sBAAK+R,SAAS,EAAC,eAAe;sBAAAC,QAAA,eAC5BhS,OAAA,CAACF,cAAc;wBACbW,IAAI,EAAEY,YAAa;wBACnBuS,IAAI,EAAC,IAAI;wBACTC,gBAAgB,EAAE,IAAK;wBACvB7L,KAAK,EAAE;0BACL8L,MAAM,EAAE,mBAAmB;0BAC3B3L,SAAS,EAAE;wBACb;sBAAE;wBAAA8J,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,eAGNpS,OAAA;sBAAK+R,SAAS,EAAC,WAAW;sBAAAC,QAAA,gBACxBhS,OAAA;wBAAI+R,SAAS,EAAC,4CAA4C;wBAAAC,QAAA,EACvD3Q,YAAY,CAACiI,IAAI,IAAIjI,YAAY,CAAC0S,QAAQ,IAAI;sBAAM;wBAAA9B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnD,CAAC,eAGLpS,OAAA;wBAAK+R,SAAS,EAAC,gCAAgC;wBAAAC,QAAA,gBAC7ChS,OAAA;0BAAK+R,SAAS,EAAC,4CAA4C;0BAAAC,QAAA,gBACzDhS,OAAA;4BAAK+R,SAAS,EAAC,wBAAwB;4BAAAC,QAAA,EAAC;0BAAQ;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACtDpS,OAAA;4BAAK+R,SAAS,EAAC,sBAAsB;4BAAAC,QAAA,EAClC,CAAC,MAAM;8BACN;8BACA,MAAMvM,EAAE,GAAGpE,YAAY,CAAC6E,OAAO,IAAI7E,YAAY,CAACoE,EAAE,IAAIpE,YAAY,CAAC2S,MAAM,IAAI3S,YAAY,CAAC2K,WAAW,IAAI,CAAC;8BAC1G,OAAOvG,EAAE,CAACwO,cAAc,CAAC,CAAC;4BAC5B,CAAC,EAAE;0BAAC;4BAAAhC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eAENpS,OAAA;0BAAK+R,SAAS,EAAC,6CAA6C;0BAAAC,QAAA,gBAC1DhS,OAAA;4BAAK+R,SAAS,EAAC,yBAAyB;4BAAAC,QAAA,EAAC;0BAAI;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACnDpS,OAAA;4BAAK+R,SAAS,EAAC,sBAAsB;4BAAAC,QAAA,EAClC,CAAC,MAAM;8BACN;8BACA,MAAM1D,aAAa,GAAGzM,WAAW,CAAC0L,IAAI,CAACvG,CAAC,IAAIyF,MAAM,CAACzF,CAAC,CAACC,GAAG,CAAC,KAAKwF,MAAM,CAACpL,YAAY,CAAC4F,GAAG,CAAC,CAAC;8BACvF,OAAOqH,aAAa,GAAI,IAAGA,aAAa,CAACpE,IAAK,EAAC,GAAIjI,eAAe,GAAI,IAAGA,eAAgB,EAAC,GAAG,KAAM;4BACrG,CAAC,EAAE;0BAAC;4BAAAgQ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eAENpS,OAAA;0BAAK+R,SAAS,EAAC,2CAA2C;0BAAAC,QAAA,gBACxDhS,OAAA;4BAAK+R,SAAS,EAAC,uBAAuB;4BAAAC,QAAA,EAAC;0BAAM;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACnDpS,OAAA;4BAAK+R,SAAS,EAAC,8BAA8B;4BAAAC,QAAA,EAC1C,CAAC,MAAM;8BACN;8BACA,MAAMhF,MAAM,GAAG3L,YAAY,CAAC6E,OAAO,IAAI7E,YAAY,CAACoE,EAAE,IAAIpE,YAAY,CAAC2S,MAAM,IAAI3S,YAAY,CAAC2K,WAAW,IAAI,CAAC;8BAC9G,KAAK,MAAM,CAAC1F,SAAS,EAAEuJ,UAAU,CAAC,IAAIjK,MAAM,CAACC,OAAO,CAAC1C,YAAY,CAAC,EAAE;gCAAA,IAAA+Q,kBAAA;gCAClE,MAAMpE,YAAY,IAAAoE,kBAAA,GAAGrE,UAAU,CAACtP,KAAK,cAAA2T,kBAAA,uBAAhBA,kBAAA,CAAkB3G,IAAI,CAACvG,CAAC,IAAIyF,MAAM,CAACzF,CAAC,CAACC,GAAG,CAAC,KAAKwF,MAAM,CAACpL,YAAY,CAAC4F,GAAG,CAAC,CAAC;gCAC5F,IAAI6I,YAAY,EAAE;kCAChB,MAAMqE,UAAU,GAAG3O,aAAa,CAACwH,MAAM,CAAC;kCACxC,OAAQ,GAAEmH,UAAU,CAACtP,UAAW,IAAGyB,SAAS,CAAC8N,WAAW,CAAC,CAAE,EAAC;gCAC9D;8BACF;8BACA;8BACA,IAAIpH,MAAM,GAAG,CAAC,EAAE;gCACd,MAAMmH,UAAU,GAAG3O,aAAa,CAACwH,MAAM,CAAC;gCACxC,OAAQ,GAAEmH,UAAU,CAACtP,UAAW,IAAGsP,UAAU,CAACzO,MAAM,CAAC0O,WAAW,CAAC,CAAE,EAAC;8BACtE;8BACA,OAAO,aAAa;4BACtB,CAAC,EAAE;0BAAC;4BAAAnC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eAENpS,OAAA;0BAAK+R,SAAS,EAAC,6CAA6C;0BAAAC,QAAA,gBAC1DhS,OAAA;4BAAK+R,SAAS,EAAC,yBAAyB;4BAAAC,QAAA,EAAC;0BAAO;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACtDpS,OAAA;4BAAK+R,SAAS,EAAC,sBAAsB;4BAAAC,QAAA,EAClC,CAAC,MAAM;8BACN;8BACA,OAAO3Q,YAAY,CAACgT,gBAAgB,IAAIhT,YAAY,CAAC8H,iBAAiB,IAAI9H,YAAY,CAACiT,YAAY,IAAIjT,YAAY,CAACmK,YAAY,IAAI,CAAC;4BACvI,CAAC,EAAE;0BAAC;4BAAAyG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eAGNpS,OAAA;wBAAK+R,SAAS,EAAC,qCAAqC;wBAAAC,QAAA,gBAClDhS,OAAA;0BAAK+R,SAAS,EAAC,+CAA+C;0BAAAC,QAAA,gBAC5DhS,OAAA;4BAAK+R,SAAS,EAAC,yBAAyB;4BAAAC,QAAA,EAAC;0BAAK;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACpDpS,OAAA;4BAAK+R,SAAS,EAAC,sBAAsB;4BAAAC,QAAA,EAClC3Q,YAAY,CAACgJ,YAAY,IAAIhJ,YAAY,CAACsH,KAAK,IAAI;0BAAC;4BAAAsJ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAClD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eAENpS,OAAA;0BAAK+R,SAAS,EAAC,4CAA4C;0BAAAC,QAAA,gBACzDhS,OAAA;4BAAK+R,SAAS,EAAC,sBAAsB;4BAAAC,QAAA,EAAC;0BAAM;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eAClDpS,OAAA;4BAAK+R,SAAS,EAAC,sBAAsB;4BAAAC,QAAA,EAClC3Q,YAAY,CAAC0I,aAAa,IAAI1I,YAAY,CAACkT,MAAM,IAAI;0BAAC;4BAAAtC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACpD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eAENpS,OAAA;0BAAK+R,SAAS,EAAC,6CAA6C;0BAAAC,QAAA,gBAC1DhS,OAAA;4BAAK+R,SAAS,EAAC,uBAAuB;4BAAAC,QAAA,EAAC;0BAAS;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACtDpS,OAAA;4BAAK+R,SAAS,EAAC,sBAAsB;4BAAAC,QAAA,GAClC,CAAC,MAAM;8BACN,MAAMwC,QAAQ,GAAGnT,YAAY,CAACyI,YAAY,IAAIzI,YAAY,CAACmT,QAAQ,IAAI,CAAC;8BACxE,OAAO1I,IAAI,CAACC,KAAK,CAACyI,QAAQ,CAAC;4BAC7B,CAAC,EAAE,CAAC,EAAC,GACP;0BAAA;4BAAAvC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,EAGL,CAAC,MAAM;wBACN;wBACA,KAAK,MAAM,CAAC9L,SAAS,EAAEuJ,UAAU,CAAC,IAAIjK,MAAM,CAACC,OAAO,CAAC1C,YAAY,CAAC,EAAE;0BAAA,IAAAsR,kBAAA;0BAClE,MAAM/C,SAAS,IAAA+C,kBAAA,GAAG5E,UAAU,CAACtP,KAAK,cAAAkU,kBAAA,uBAAhBA,kBAAA,CAAkB1N,SAAS,CAACC,CAAC,IAAIyF,MAAM,CAACzF,CAAC,CAACC,GAAG,CAAC,KAAKwF,MAAM,CAACpL,YAAY,CAAC4F,GAAG,CAAC,CAAC;0BAC9F,IAAIyK,SAAS,KAAK,CAAC,CAAC,IAAIA,SAAS,KAAKgD,SAAS,EAAE;4BAC/C,oBACE1U,OAAA;8BAAK+R,SAAS,EAAC,kBAAkB;8BAAAC,QAAA,eAC/BhS,OAAA;gCAAK+R,SAAS,EAAC,uEAAuE;gCAAAC,QAAA,gBACpFhS,OAAA;kCAAK+R,SAAS,EAAC,yBAAyB;kCAAAC,QAAA,EAAC;gCAAe;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAK,CAAC,eAC9DpS,OAAA;kCAAK+R,SAAS,EAAC,8BAA8B;kCAAAC,QAAA,GAAC,GAC3C,EAACN,SAAS,GAAG,CAAC,EAAC,MAAI,EAAC7B,UAAU,CAACtP,KAAK,CAAC4G,MAAM;gCAAA;kCAAA8K,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACzC,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACH;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH,CAAC;0BAEV;wBACF;wBACA,OAAO,IAAI;sBACb,CAAC,EAAE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CACb,eAaDpS,OAAA;kBAAK+R,SAAS,EAAC,uHAAuH;kBAAAC,QAAA,gBAEpIhS,OAAA,CAACxB,MAAM,CAACmW,EAAE;oBACR5C,SAAS,EAAC,sCAAsC;oBAChD/J,KAAK,EAAE;sBACL2L,UAAU,EAAE,mDAAmD;sBAC/DiB,oBAAoB,EAAE,MAAM;sBAC5BC,mBAAmB,EAAE,aAAa;sBAClCC,UAAU,EAAE,6BAA6B;sBACzCjO,MAAM,EAAE;oBACV,CAAE;oBACF+L,OAAO,EAAE;sBAAEW,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;oBAAE,CAAE;oBACjCrL,UAAU,EAAE;sBAAE6K,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC;oBAAS,CAAE;oBAAAjB,QAAA,EAC/C;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAGZpS,OAAA;oBAAK+R,SAAS,EAAC,2DAA2D;oBAAAC,QAAA,EACvE5J,iBAAiB,CAAC,CAAC,CAACiB,GAAG,CAAE/C,SAAS,IAAK;sBAAA,IAAAyO,sBAAA;sBACtC,MAAMrP,MAAM,GAAG3B,YAAY,CAACuC,SAAS,CAAC;sBACtC,MAAM0O,UAAU,GAAG/R,cAAc,KAAKqD,SAAS;sBAC/C,MAAM2O,SAAS,GAAG,EAAAF,sBAAA,GAAA5R,YAAY,CAACmD,SAAS,CAAC,cAAAyO,sBAAA,uBAAvBA,sBAAA,CAAyBxU,KAAK,CAAC4G,MAAM,KAAI,CAAC;sBAE5D,oBACEnH,OAAA,CAACxB,MAAM,CAACgU,GAAG;wBAETT,SAAS,EAAC,kCAAkC;wBAC5CuB,UAAU,EAAE;0BAAEC,KAAK,EAAE;wBAAK,CAAE;wBAAAvB,QAAA,gBAE5BhS,OAAA,CAACxB,MAAM,CAAC6U,MAAM;0BACZC,UAAU,EAAE;4BAAEC,KAAK,EAAE,GAAG;4BAAEZ,CAAC,EAAE,CAAC;0BAAE,CAAE;0BAClCa,QAAQ,EAAE;4BAAED,KAAK,EAAE;0BAAK,CAAE;0BAC1BhB,OAAO,EAAEA,CAAA,KAAMnL,kBAAkB,CAACd,SAAS,CAAE;0BAC7CyL,SAAS,EAAG,+GACViD,UAAU,GACN,oDAAoD,GACpD,kCACL,EAAE;0BACHhN,KAAK,EAAE;4BACL2L,UAAU,EAAEqB,UAAU,GACjB,2BAA0BtP,MAAM,CAACf,WAAY,OAAMe,MAAM,CAACtB,SAAU,OAAMsB,MAAM,CAACf,WAAY,KAAI,GACjG,2BAA0Be,MAAM,CAACf,WAAY,OAAMe,MAAM,CAACtB,SAAU,KAAI;4BAC7E0P,MAAM,EAAG,aAAYkB,UAAU,GAAG,SAAS,GAAGtP,MAAM,CAACf,WAAW,GAAG,IAAK,EAAC;4BACzEwD,SAAS,EAAE6M,UAAU,GAChB,YAAWtP,MAAM,CAACpB,WAAY,sCAAqCoB,MAAM,CAACpB,WAAY,IAAG,GACzF,cAAaoB,MAAM,CAACpB,WAAY,IAAG;4BACxC2D,SAAS,EAAE+M,UAAU,GAAG,YAAY,GAAG,UAAU;4BACjDnO,MAAM,EAAEmO,UAAU,GAAG,+BAA+B,GAAG;0BACzD,CAAE;0BACFpC,OAAO,EAAEoC,UAAU,GAAG;4BACpB7M,SAAS,EAAE,CACR,YAAWzC,MAAM,CAACpB,WAAY,wBAAuB,EACrD,YAAWoB,MAAM,CAACpB,WAAY,yBAAwB,EACtD,YAAWoB,MAAM,CAACpB,WAAY,wBAAuB,CACvD;4BACDiP,KAAK,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG;0BACxB,CAAC,GAAG,CAAC,CAAE;0BACPrL,UAAU,EAAE;4BACV6K,QAAQ,EAAE,CAAC;4BACXC,MAAM,EAAEgC,UAAU,GAAG/B,QAAQ,GAAG,CAAC;4BACjCiC,IAAI,EAAE;0BACR,CAAE;0BACFzQ,KAAK,EAAG,iBAAgBiB,MAAM,CAACjB,KAAM,YAAWwQ,SAAU,SAAS;0BAAAjD,QAAA,gBAEnEhS,OAAA;4BAAM+R,SAAS,EAAC,sBAAsB;4BAAAC,QAAA,EAAEtM,MAAM,CAACb;0BAAU;4BAAAoN,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC,EAChE4C,UAAU,iBACThV,OAAA,CAACxB,MAAM,CAACgU,GAAG;4BACTC,OAAO,EAAE;8BAAEc,KAAK,EAAE,CAAC;8BAAE4B,MAAM,EAAE,CAAC,GAAG;8BAAEzC,OAAO,EAAE;4BAAE,CAAE;4BAChDE,OAAO,EAAE;8BACPW,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;8BAClB4B,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;8BACrBzC,OAAO,EAAE,CAAC;8BACVvK,SAAS,EAAE,CACT,gEAAgE,EAChE,8DAA8D,EAC9D,gEAAgE;4BAEpE,CAAE;4BACFD,UAAU,EAAE;8BACVqL,KAAK,EAAE;gCAAER,QAAQ,EAAE,CAAC;gCAAEC,MAAM,EAAEC,QAAQ;gCAAEiC,IAAI,EAAE;8BAAY,CAAC;8BAC3DC,MAAM,EAAE;gCAAEpC,QAAQ,EAAE,CAAC;gCAAEC,MAAM,EAAEC,QAAQ;gCAAEiC,IAAI,EAAE;8BAAS,CAAC;8BACzD/M,SAAS,EAAE;gCAAE4K,QAAQ,EAAE,GAAG;gCAAEC,MAAM,EAAEC,QAAQ;gCAAEiC,IAAI,EAAE;8BAAY,CAAC;8BACjExC,OAAO,EAAE;gCAAEK,QAAQ,EAAE;8BAAI;4BAC3B,CAAE;4BACFhB,SAAS,EAAC,8KAA8K;4BACxL/J,KAAK,EAAE;8BACL2L,UAAU,EAAE,mDAAmD;8BAC/DG,MAAM,EAAE,iBAAiB;8BACzBsB,MAAM,EAAE;4BACV,CAAE;4BAAApD,QAAA,eAEFhS,OAAA,CAACxB,MAAM,CAAC6W,IAAI;8BACVtD,SAAS,EAAC,kCAAkC;8BAC5Ca,OAAO,EAAE;gCACPW,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;gCAClB4B,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;8BACxB,CAAE;8BACFjN,UAAU,EAAE;gCACV6K,QAAQ,EAAE,CAAC;gCACXC,MAAM,EAAEC,QAAQ;gCAChBiC,IAAI,EAAE;8BACR,CAAE;8BAAAlD,QAAA,EACH;4BAED;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAa;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACJ,CACb,eACDpS,OAAA;4BACE+R,SAAS,EAAC,2HAA2H;4BACrI/J,KAAK,EAAE;8BACL2L,UAAU,EAAEjO,MAAM,CAACf,WAAW;8BAC9BT,KAAK,EAAE,SAAS;8BAChBuP,QAAQ,EAAE;4BACZ,CAAE;4BAAAzB,QAAA,EAEDiD;0BAAS;4BAAAhD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACP,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACO,CAAC,eAGhBpS,OAAA,CAACxB,MAAM,CAACgU,GAAG;0BACTT,SAAS,EAAC,aAAa;0BACvBuB,UAAU,EAAE;4BAAEC,KAAK,EAAE;0BAAK,CAAE;0BAAAvB,QAAA,eAE5BhS,OAAA;4BACE+R,SAAS,EAAC,mDAAmD;4BAC7D/J,KAAK,EAAE;8BACL9D,KAAK,EAAEwB,MAAM,CAACrB,SAAS;8BACvByQ,UAAU,EAAG,eAAcpP,MAAM,CAACpB,WAAY,EAAC;8BAC/CqP,UAAU,EAAG,GAAEjO,MAAM,CAACf,WAAY,IAAG;8BACrCmP,MAAM,EAAG,aAAYpO,MAAM,CAACf,WAAY;4BAC1C,CAAE;4BAAAqN,QAAA,EAEDtM,MAAM,CAACjB;0BAAK;4BAAAwN,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACV;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACI,CAAC;sBAAA,GA9GR9L,SAAS;wBAAA2L,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OA+GJ,CAAC;oBAEjB,CAAC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eAENpS,OAAA;oBAAG+R,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EAAC;kBAEtD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eAKNpS,OAAA,CAACxB,MAAM,CAAC6U,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE,IAAI;oBAAE4B,MAAM,EAAE;kBAAI,CAAE;kBACzC3B,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BhB,OAAO,EAAEjK,gBAAiB;kBAC1BgN,QAAQ,EAAEvT,OAAQ;kBAClBgQ,SAAS,EAAC,qNAAqN;kBAC/N/J,KAAK,EAAE;oBACLyL,QAAQ,EAAEhE,MAAM,CAACiE,UAAU,GAAG,GAAG,GAAG,MAAM,GAAG;kBAC/C,CAAE;kBAAA1B,QAAA,gBAEFhS,OAAA,CAACb,SAAS;oBAAC4S,SAAS,EAAG,yBAAwBhQ,OAAO,GAAG,cAAc,GAAG,EAAG;kBAAE;oBAAAkQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAClFpS,OAAA;oBAAAgS,QAAA,EAAM;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,EAGZ,KAAK,KAAK,CAAA3R,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsM,IAAI,MAAK,OAAO,KAAItM,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqM,OAAO,EAAC,iBACjD9M,OAAA,CAACxB,MAAM,CAACgU,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BzK,UAAU,EAAE;YAAE6K,QAAQ,EAAE;UAAI,CAAE;UAC9BhB,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAE7ChS,OAAA;YAAK+R,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAChChS,OAAA;cAAK+R,SAAS,EAAC,gHAAgH;cAAAC,QAAA,eAC7HhS,OAAA;gBAAK+R,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACtChS,OAAA;kBAAK+R,SAAS,EAAC,qEAAqE;kBAAAC,QAAA,eAClFhS,OAAA;oBAAM+R,SAAS,EAAC,8BAA8B;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC,eACNpS,OAAA;kBAAAgS,QAAA,gBACEhS,OAAA;oBAAI+R,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpDpS,OAAA;oBAAG+R,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAErC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACb,eAGDpS,OAAA,CAACxB,MAAM,CAACgU,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BzK,UAAU,EAAE;YAAE6K,QAAQ,EAAE,CAAC;YAAEmC,IAAI,EAAE;UAAU,CAAE;UAC7CnD,SAAS,EAAC,+BAA+B;UAAAC,QAAA,eAGzChS,OAAA;YAAK+R,SAAS,EAAC,iGAAiG;YAAAC,QAAA,gBAC9GhS,OAAA;cAAK+R,SAAS,EAAC;YAA6E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnGpS,OAAA;cAAK+R,SAAS,EAAC;YAA+E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAGrGpS,OAAA;cAAK+R,SAAS,EAAC,2EAA2E;cAAAC,QAAA,eACxFhS,OAAA;gBAAK+R,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,gBAG5ChS,OAAA,CAACxB,MAAM,CAACgU,GAAG;kBACTI,OAAO,EAAE;oBACPW,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;oBACnBgC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;kBACnB,CAAE;kBACFrN,UAAU,EAAE;oBACV6K,QAAQ,EAAE,CAAC;oBACXC,MAAM,EAAEC,QAAQ;oBAChBiC,IAAI,EAAE;kBACR,CAAE;kBACFnD,SAAS,EAAC,cAAc;kBAAAC,QAAA,eAExBhS,OAAA;oBAAI+R,SAAS,EAAC,iGAAiG;oBAAAC,QAAA,gBAC7GhS,OAAA,CAACxB,MAAM,CAAC6W,IAAI;sBACVzC,OAAO,EAAE;wBACP4C,kBAAkB,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,QAAQ;sBACrD,CAAE;sBACFtN,UAAU,EAAE;wBACV6K,QAAQ,EAAE,CAAC;wBACXC,MAAM,EAAEC,QAAQ;wBAChBiC,IAAI,EAAE;sBACR,CAAE;sBACFnD,SAAS,EAAC,+HAA+H;sBACzI/J,KAAK,EAAE;wBACLyN,cAAc,EAAE,WAAW;wBAC3Bb,oBAAoB,EAAE,MAAM;wBAC5BC,mBAAmB,EAAE,aAAa;wBAClChO,MAAM,EAAE;sBACV,CAAE;sBAAAmL,QAAA,EACH;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC,eACdpS,OAAA;sBAAAiS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNpS,OAAA,CAACxB,MAAM,CAAC6W,IAAI;sBACVzC,OAAO,EAAE;wBACPkC,UAAU,EAAE,CACV,4DAA4D,EAC5D,0DAA0D,EAC1D,4DAA4D;sBAEhE,CAAE;sBACF5M,UAAU,EAAE;wBACV6K,QAAQ,EAAE,GAAG;wBACbC,MAAM,EAAEC,QAAQ;wBAChBiC,IAAI,EAAE;sBACR,CAAE;sBACFlN,KAAK,EAAE;wBACL9D,KAAK,EAAE,SAAS;wBAChBwR,UAAU,EAAE,KAAK;wBACjBZ,UAAU,EAAE;sBACd,CAAE;sBAAA9C,QAAA,EACH;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC,eAGbpS,OAAA,CAACxB,MAAM,CAACmX,CAAC;kBACPlD,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEC,CAAC,EAAE;kBAAG,CAAE;kBAC/BC,OAAO,EAAE;oBAAEF,OAAO,EAAE,CAAC;oBAAEC,CAAC,EAAE;kBAAE,CAAE;kBAC9BzK,UAAU,EAAE;oBAAEgL,KAAK,EAAE,GAAG;oBAAEH,QAAQ,EAAE;kBAAI,CAAE;kBAC1ChB,SAAS,EAAC,8GAA8G;kBACxH/J,KAAK,EAAE;oBACL9D,KAAK,EAAE,SAAS;oBAChB4Q,UAAU,EAAE,6BAA6B;oBACzCnB,UAAU,EAAE,0CAA0C;oBACtDiB,oBAAoB,EAAE,MAAM;oBAC5BC,mBAAmB,EAAE;kBACvB,CAAE;kBAAA7C,QAAA,EACH;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAGXpS,OAAA,CAACxB,MAAM,CAACgU,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEa,KAAK,EAAE;kBAAI,CAAE;kBACpCX,OAAO,EAAE;oBAAEF,OAAO,EAAE,CAAC;oBAAEa,KAAK,EAAE;kBAAE,CAAE;kBAClCrL,UAAU,EAAE;oBAAEgL,KAAK,EAAE,GAAG;oBAAEH,QAAQ,EAAE;kBAAI,CAAE;kBAC1ChB,SAAS,EAAC,cAAc;kBAAAC,QAAA,eAExBhS,OAAA;oBAAG+R,SAAS,EAAC,6JAA6J;oBACvK/J,KAAK,EAAE;sBACL8M,UAAU,EAAE,6BAA6B;sBACzCc,SAAS,EAAE;oBACb,CAAE;oBAAA5D,QAAA,EACFvP;kBAAiB;oBAAAwP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CAAC,eAGbpS,OAAA,CAACxB,MAAM,CAACgU,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEC,CAAC,EAAE;kBAAG,CAAE;kBAC/BC,OAAO,EAAE;oBAAEF,OAAO,EAAE,CAAC;oBAAEC,CAAC,EAAE;kBAAE,CAAE;kBAC9BzK,UAAU,EAAE;oBAAEgL,KAAK,EAAE,CAAC;oBAAEH,QAAQ,EAAE;kBAAI,CAAE;kBACxChB,SAAS,EAAC,2EAA2E;kBAAAC,QAAA,EAEpF,CACC;oBACExN,IAAI,EAAE/E,OAAO;oBACboW,KAAK,EAAEhU,WAAW,CAACsF,MAAM;oBACzB2O,KAAK,EAAE,WAAW;oBAClBC,UAAU,EAAE,qDAAqD;oBACjEC,SAAS,EAAE,SAAS;oBACpBrR,WAAW,EAAE;kBACf,CAAC,EACD;oBACEH,IAAI,EAAE3F,QAAQ;oBACdgX,KAAK,EAAE9F,aAAa,CAAC5I,MAAM;oBAC3B2O,KAAK,EAAE,gBAAgB;oBACvBC,UAAU,EAAE,oDAAoD;oBAChEC,SAAS,EAAE,SAAS;oBACpBrR,WAAW,EAAE;kBACf,CAAC,EACD;oBACEH,IAAI,EAAExF,OAAO;oBACb6W,KAAK,EAAEhU,WAAW,CAACgF,MAAM,CAACG,CAAC,IAAIA,CAAC,CAAC+C,aAAa,GAAG,CAAC,CAAC,CAAC5C,MAAM;oBAC1D2O,KAAK,EAAE,gBAAgB;oBACvBC,UAAU,EAAE,gDAAgD;oBAC5DC,SAAS,EAAE,SAAS;oBACpBrR,WAAW,EAAE;kBACf,CAAC,EACD;oBACEH,IAAI,EAAEzF,MAAM;oBACZ8W,KAAK,EAAEhU,WAAW,CAAC6J,MAAM,CAAC,CAACC,GAAG,EAAE3E,CAAC,KAAK2E,GAAG,IAAI3E,CAAC,CAACd,OAAO,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC+N,cAAc,CAAC,CAAC;oBACjF6B,KAAK,EAAE,UAAU;oBACjBC,UAAU,EAAE,qDAAqD;oBACjEC,SAAS,EAAE,SAAS;oBACpBrR,WAAW,EAAE;kBACf,CAAC,CACF,CAAC0E,GAAG,CAAC,CAAC4M,IAAI,EAAEtM,KAAK,kBAChB3J,OAAA,CAACxB,MAAM,CAACgU,GAAG;oBAETC,OAAO,EAAE;sBAAEC,OAAO,EAAE,CAAC;sBAAEa,KAAK,EAAE;oBAAI,CAAE;oBACpCX,OAAO,EAAE;sBAAEF,OAAO,EAAE,CAAC;sBAAEa,KAAK,EAAE;oBAAE,CAAE;oBAClCrL,UAAU,EAAE;sBAAEgL,KAAK,EAAE,GAAG,GAAGvJ,KAAK,GAAG,GAAG;sBAAEoJ,QAAQ,EAAE;oBAAI,CAAE;oBACxDO,UAAU,EAAE;sBAAEC,KAAK,EAAE,IAAI;sBAAEZ,CAAC,EAAE,CAAC;oBAAE,CAAE;oBACnCZ,SAAS,EAAG,qBAAoBkE,IAAI,CAACF,UAAW,8EAA8E;oBAC9H/N,KAAK,EAAE;sBACL8L,MAAM,EAAG,aAAYmC,IAAI,CAACtR,WAAY,IAAG;sBACzCwD,SAAS,EAAG,cAAa8N,IAAI,CAACtR,WAAY;oBAC5C,CAAE;oBAAAqN,QAAA,gBAEFhS,OAAA;sBAAK+R,SAAS,EAAC;oBAAgE;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACtFpS,OAAA,CAACiW,IAAI,CAACzR,IAAI;sBACRuN,SAAS,EAAC,kDAAkD;sBAC5D/J,KAAK,EAAE;wBAAE9D,KAAK,EAAE+R,IAAI,CAACD,SAAS;wBAAEnP,MAAM,EAAE;sBAAyC;oBAAE;sBAAAoL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpF,CAAC,eACFpS,OAAA;sBACE+R,SAAS,EAAC,0EAA0E;sBACpF/J,KAAK,EAAE;wBACL9D,KAAK,EAAE+R,IAAI,CAACD,SAAS;wBACrBlB,UAAU,EAAG,6BAA4B;wBACzCjO,MAAM,EAAE,oCAAoC;wBAC5C4M,QAAQ,EAAE;sBACZ,CAAE;sBAAAzB,QAAA,EAEDiE,IAAI,CAACJ;oBAAK;sBAAA5D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CAAC,eACNpS,OAAA;sBACE+R,SAAS,EAAC,4CAA4C;sBACtD/J,KAAK,EAAE;wBACL9D,KAAK,EAAE,SAAS;wBAChB4Q,UAAU,EAAE,6BAA6B;wBACzCrB,QAAQ,EAAE;sBACZ,CAAE;sBAAAzB,QAAA,EAEDiE,IAAI,CAACH;oBAAK;sBAAA7D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CAAC;kBAAA,GApCDzI,KAAK;oBAAAsI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAqCA,CACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,EAGZrQ,OAAO,iBACN/B,OAAA,CAACxB,MAAM,CAACgU,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE;UAAE,CAAE;UACxBE,OAAO,EAAE;YAAEF,OAAO,EAAE;UAAE,CAAE;UACxBX,SAAS,EAAC,iDAAiD;UAAAC,QAAA,gBAE3DhS,OAAA,CAACxB,MAAM,CAACgU,GAAG;YACTI,OAAO,EAAE;cAAEuC,MAAM,EAAE;YAAI,CAAE;YACzBjN,UAAU,EAAE;cAAE6K,QAAQ,EAAE,CAAC;cAAEC,MAAM,EAAEC,QAAQ;cAAEiC,IAAI,EAAE;YAAS,CAAE;YAC9DnD,SAAS,EAAC;UAA6E;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxF,CAAC,eACFpS,OAAA;YAAG+R,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CACb,EAGA,CAACrQ,OAAO,iBACP/B,OAAA,CAACxB,MAAM,CAACgU,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BzK,UAAU,EAAE;YAAEgL,KAAK,EAAE,GAAG;YAAEH,QAAQ,EAAE;UAAI,CAAE;UAC1ChB,SAAS,EAAC,uDAAuD;UAAAC,QAAA,eAEjEhS,OAAA;YAAK+R,SAAS,EAAC,mBAAmB;YAAAC,QAAA,GAG/BjC,aAAa,CAAC5I,MAAM,GAAG,CAAC,iBACvBnH,OAAA,CAACxB,MAAM,CAACgU,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEa,KAAK,EAAE;cAAI,CAAE;cACpCX,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEa,KAAK,EAAE;cAAE,CAAE;cAClCrL,UAAU,EAAE;gBAAEgL,KAAK,EAAE,GAAG;gBAAEH,QAAQ,EAAE;cAAI,CAAE;cAC1ChB,SAAS,EAAC,OAAO;cAAAC,QAAA,gBAEjBhS,OAAA;gBAAI+R,SAAS,EAAC,gGAAgG;gBAAC/J,KAAK,EAAE;kBACpH2L,UAAU,EAAE,mDAAmD;kBAC/DiB,oBAAoB,EAAE,MAAM;kBAC5BC,mBAAmB,EAAE,aAAa;kBAClCC,UAAU,EAAE,6BAA6B;kBACzCjO,MAAM,EAAE;gBACV,CAAE;gBAAAmL,QAAA,EAAC;cAEH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAGLpS,OAAA;gBAAK+R,SAAS,EAAC,mGAAmG;gBAAAC,QAAA,GAE/GjC,aAAa,CAAC,CAAC,CAAC,iBACf/P,OAAA,CAACxB,MAAM,CAACgU,GAAG;kBAET0D,GAAG,EAAEzV,IAAI,IAAIgM,MAAM,CAACsD,aAAa,CAAC,CAAC,CAAC,CAAC9I,GAAG,CAAC,KAAKwF,MAAM,CAAChM,IAAI,CAACwG,GAAG,CAAC,GAAGrD,aAAa,GAAG,IAAK;kBACtF,gBAAcmM,aAAa,CAAC,CAAC,CAAC,CAAC9I,GAAI;kBACnC,kBAAgB,CAAE;kBAClBwL,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEI,CAAC,EAAE,CAAC,GAAG;oBAAEH,CAAC,EAAE;kBAAG,CAAE;kBACxCC,OAAO,EAAE;oBACPF,OAAO,EAAE,CAAC;oBACVI,CAAC,EAAE,CAAC;oBACJH,CAAC,EAAE,CAAC;oBACJY,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;oBACnBgC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;kBACnB,CAAE;kBACFrN,UAAU,EAAE;oBACVgL,KAAK,EAAE,GAAG;oBACVH,QAAQ,EAAE,GAAG;oBACbQ,KAAK,EAAE;sBAAER,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEiC,IAAI,EAAE;oBAAY,CAAC;oBAC3DK,OAAO,EAAE;sBAAExC,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEiC,IAAI,EAAE;oBAAY;kBAC9D,CAAE;kBACF5B,UAAU,EAAE;oBAAEC,KAAK,EAAE,IAAI;oBAAEZ,CAAC,EAAE,CAAC;kBAAG,CAAE;kBACpCZ,SAAS,EAAG,oBACVpB,mBAAmB,CAACZ,aAAa,CAAC,CAAC,CAAC,CAAC9I,GAAG,CAAC,GACrC,yCAAyC,GACzC,EACL,EAAE;kBACHe,KAAK,EAAE;oBACLmO,MAAM,EAAE,OAAO;oBACflO,SAAS,EAAE0I,mBAAmB,CAACZ,aAAa,CAAC,CAAC,CAAC,CAAC9I,GAAG,CAAC,GAAG,aAAa,GAAG,UAAU;oBACjFJ,MAAM,EAAE8J,mBAAmB,CAACZ,aAAa,CAAC,CAAC,CAAC,CAAC9I,GAAG,CAAC,GAC7C,0EAA0E,GAC1E,MAAM;oBACViB,UAAU,EAAE,eAAe;oBAC3B4L,MAAM,EAAEnD,mBAAmB,CAACZ,aAAa,CAAC,CAAC,CAAC,CAAC9I,GAAG,CAAC,GAAG,mBAAmB,GAAG,MAAM;oBAChFmP,YAAY,EAAE1F,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC9I,GAAG,CAAC,GAAG,MAAM,GAAG,KAAK;oBAClE0M,UAAU,EAAEjD,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC9I,GAAG,CAAC,GAAG,wEAAwE,GAAG;kBAC/H,CAAE;kBAAA+K,QAAA,gBAGFhS,OAAA;oBAAK+R,SAAS,EAAC,sJAAsJ;oBAAAC,QAAA,eACnKhS,OAAA;sBAAM+R,SAAS,EAAC,iDAAiD;sBAAAC,QAAA,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzE,CAAC,eAGNpS,OAAA;oBACE+R,SAAS,EAAG,8BAA6BhC,aAAa,CAAC,CAAC,CAAC,CAAC3J,IAAI,CAAClC,KAAM,mBAAkB6L,aAAa,CAAC,CAAC,CAAC,CAAC3J,IAAI,CAAC7B,IAAK,kBAAkB;oBACpIyD,KAAK,EAAE;sBACLG,SAAS,EAAG,cAAa4H,aAAa,CAAC,CAAC,CAAC,CAAC3J,IAAI,CAAC9B,WAAY,IAAG;sBAC9D+R,KAAK,EAAE;oBACT,CAAE;oBAAArE,QAAA,eAEFhS,OAAA;sBACE+R,SAAS,EAAG,GAAEhC,aAAa,CAAC,CAAC,CAAC,CAAC3J,IAAI,CAACjC,OAAQ,uEAAuE;sBAAA6N,QAAA,gBAEnHhS,OAAA;wBAAK+R,SAAS,EAAC;sBAAgE;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAGtFpS,OAAA;wBACE+R,SAAS,EAAC,oMAAoM;wBAC9M/J,KAAK,EAAE;0BACL9D,KAAK,EAAE,SAAS;0BAChB4P,MAAM,EAAE;wBACV,CAAE;wBAAA9B,QAAA,EACH;sBAED;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAGNpS,OAAA;wBAAK+R,SAAS,EAAG,yBAAwBtR,IAAI,IAAIsP,aAAa,CAAC,CAAC,CAAC,CAAC9I,GAAG,KAAKxG,IAAI,CAACwG,GAAG,GAAG,wCAAwC,GAAG,EAAG,EAAE;wBAAA+K,QAAA,gBAEnIhS,OAAA,CAACF,cAAc;0BACbW,IAAI,EAAEsP,aAAa,CAAC,CAAC,CAAE;0BACvB6D,IAAI,EAAC,IAAI;0BACTC,gBAAgB,EAAE,IAAK;0BACvB7L,KAAK,EAAE;4BACLqO,KAAK,EAAE,MAAM;4BACbF,MAAM,EAAE;0BACV;wBAAE;0BAAAlE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,EAED7Q,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEuO,aAAa,CAAC,CAAC,CAAC,CAAC;sBAAA;wBAAAkC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpD,CAAC,eAGNpS,OAAA;wBACE+R,SAAS,EAAC,iCAAiC;wBAC3C/J,KAAK,EAAE;0BAAE9D,KAAK,EAAE6L,aAAa,CAAC,CAAC,CAAC,CAAC3J,IAAI,CAAC/B;wBAAU,CAAE;wBAAA2N,QAAA,EAEjDjC,aAAa,CAAC,CAAC,CAAC,CAACzG;sBAAI;wBAAA2I,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB,CAAC,eAELpS,OAAA;wBAAK+R,SAAS,EAAC,yBAAyB;wBAAC/J,KAAK,EAAE;0BAAE9D,KAAK,EAAE6L,aAAa,CAAC,CAAC,CAAC,CAAC3J,IAAI,CAAChC;wBAAU,CAAE;wBAAA4N,QAAA,GACxFjC,aAAa,CAAC,CAAC,CAAC,CAAC7J,OAAO,CAAC+N,cAAc,CAAC,CAAC,EAAC,KAC7C;sBAAA;wBAAAhC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAENpS,OAAA;wBAAK+R,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,gBAChDhS,OAAA;0BAAMgI,KAAK,EAAE;4BAAE9D,KAAK,EAAE6L,aAAa,CAAC,CAAC,CAAC,CAAC3J,IAAI,CAAChC;0BAAU,CAAE;0BAAA4N,QAAA,GAAC,eACpD,EAACjC,aAAa,CAAC,CAAC,CAAC,CAAC5G,iBAAiB;wBAAA;0BAAA8I,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAClC,CAAC,eACPpS,OAAA;0BAAMgI,KAAK,EAAE;4BAAE9D,KAAK,EAAE6L,aAAa,CAAC,CAAC,CAAC,CAAC3J,IAAI,CAAChC;0BAAU,CAAE;0BAAA4N,QAAA,GAAC,eACpD,EAACjC,aAAa,CAAC,CAAC,CAAC,CAAChG,aAAa;wBAAA;0BAAAkI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9B,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,GAtGA,UAASrC,aAAa,CAAC,CAAC,CAAC,CAAC9I,GAAI,EAAC;kBAAAgL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAuG3B,CACb,EAGArC,aAAa,CAAC,CAAC,CAAC,iBACf/P,OAAA,CAACxB,MAAM,CAACgU,GAAG;kBAET0D,GAAG,EAAEzV,IAAI,IAAIgM,MAAM,CAACsD,aAAa,CAAC,CAAC,CAAC,CAAC9I,GAAG,CAAC,KAAKwF,MAAM,CAAChM,IAAI,CAACwG,GAAG,CAAC,GAAGrD,aAAa,GAAG,IAAK;kBACtF,gBAAcmM,aAAa,CAAC,CAAC,CAAC,CAAC9I,GAAI;kBACnC,kBAAgB,CAAE;kBAClBwL,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEC,CAAC,EAAE,CAAC,GAAG;oBAAEY,KAAK,EAAE;kBAAI,CAAE;kBAC7CX,OAAO,EAAE;oBACPF,OAAO,EAAE,CAAC;oBACVC,CAAC,EAAE,CAAC;oBACJY,KAAK,EAAE,CAAC;oBACRgC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;oBACxB5C,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;kBACf,CAAE;kBACFzK,UAAU,EAAE;oBACVgL,KAAK,EAAE,GAAG;oBACVH,QAAQ,EAAE,GAAG;oBACbwC,OAAO,EAAE;sBAAExC,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEiC,IAAI,EAAE;oBAAY,CAAC;oBAC7DvC,CAAC,EAAE;sBAAEI,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEiC,IAAI,EAAE;oBAAY;kBACxD,CAAE;kBACF5B,UAAU,EAAE;oBAAEC,KAAK,EAAE,IAAI;oBAAEZ,CAAC,EAAE,CAAC;kBAAG,CAAE;kBACpCZ,SAAS,EAAG,yBACVpB,mBAAmB,CAACZ,aAAa,CAAC,CAAC,CAAC,CAAC9I,GAAG,CAAC,GACrC,yCAAyC,GACzC,EACL,EAAE;kBACHe,KAAK,EAAE;oBACLmO,MAAM,EAAE,OAAO;oBACflO,SAAS,EAAE0I,mBAAmB,CAACZ,aAAa,CAAC,CAAC,CAAC,CAAC9I,GAAG,CAAC,GAAG,aAAa,GAAG,UAAU;oBACjFJ,MAAM,EAAE8J,mBAAmB,CAACZ,aAAa,CAAC,CAAC,CAAC,CAAC9I,GAAG,CAAC,GAC7C,0EAA0E,GAC1E,MAAM;oBACViB,UAAU,EAAE,eAAe;oBAC3B4L,MAAM,EAAEnD,mBAAmB,CAACZ,aAAa,CAAC,CAAC,CAAC,CAAC9I,GAAG,CAAC,GAAG,mBAAmB,GAAG,MAAM;oBAChFmP,YAAY,EAAE1F,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC9I,GAAG,CAAC,GAAG,MAAM,GAAG,KAAK;oBAClE0M,UAAU,EAAEjD,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC9I,GAAG,CAAC,GAAG,wEAAwE,GAAG;kBAC/H,CAAE;kBACF,gBAAa,QAAQ;kBAAA+K,QAAA,gBAIrBhS,OAAA;oBAAK+R,SAAS,EAAC,4JAA4J;oBAAAC,QAAA,eACzKhS,OAAA;sBAAM+R,SAAS,EAAC,mDAAmD;sBAAAC,QAAA,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3E,CAAC,eAGNpS,OAAA,CAACxB,MAAM,CAACgU,GAAG;oBACTI,OAAO,EAAE;sBAAEuC,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;sBAAExC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;oBAAE,CAAE;oBACpDzK,UAAU,EAAE;sBAAE6K,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC;oBAAS,CAAE;oBAC9ClB,SAAS,EAAC,2DAA2D;oBAAAC,QAAA,eAErEhS,OAAA,CAAClB,OAAO;sBAACiT,SAAS,EAAC;oBAA0C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD,CAAC,eAGbpS,OAAA;oBACE+R,SAAS,EAAG,8BAA6BhC,aAAa,CAAC,CAAC,CAAC,CAAC3J,IAAI,CAAClC,KAAM,sBAAqB6L,aAAa,CAAC,CAAC,CAAC,CAAC3J,IAAI,CAAC7B,IAAK,uCAAuC;oBAC5JyD,KAAK,EAAE;sBACLG,SAAS,EAAG,cAAa4H,aAAa,CAAC,CAAC,CAAC,CAAC3J,IAAI,CAAC9B,WAAY,qCAAoC;sBAC/F+R,KAAK,EAAE;oBACT,CAAE;oBAAArE,QAAA,eAEFhS,OAAA;sBACE+R,SAAS,EAAG,GAAEhC,aAAa,CAAC,CAAC,CAAC,CAAC3J,IAAI,CAACjC,OAAQ,uEAAuE;sBACnH6D,KAAK,EAAE;wBACL2L,UAAU,EAAG,GAAE5D,aAAa,CAAC,CAAC,CAAC,CAAC3J,IAAI,CAACjC,OAAQ;sBAC/C,CAAE;sBAAA6N,QAAA,gBAEFhS,OAAA;wBAAK+R,SAAS,EAAC;sBAA4E;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAGlGpS,OAAA;wBACE+R,SAAS,EAAC,wMAAwM;wBAClN/J,KAAK,EAAE;0BACL9D,KAAK,EAAE,SAAS;0BAChB4Q,UAAU,EAAE,6BAA6B;0BACzChB,MAAM,EAAE;wBACV,CAAE;wBAAA9B,QAAA,EACH;sBAED;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAGNpS,OAAA;wBAAK+R,SAAS,EAAG,yBAAwBtR,IAAI,IAAIsP,aAAa,CAAC,CAAC,CAAC,CAAC9I,GAAG,KAAKxG,IAAI,CAACwG,GAAG,GAAG,wCAAwC,GAAG,EAAG,EAAE;wBAAA+K,QAAA,gBAEnIhS,OAAA,CAACF,cAAc;0BACbW,IAAI,EAAEsP,aAAa,CAAC,CAAC,CAAE;0BACvB6D,IAAI,EAAC,IAAI;0BACTC,gBAAgB,EAAE,IAAK;0BACvB7L,KAAK,EAAE;4BACLqO,KAAK,EAAE,MAAM;4BACbF,MAAM,EAAE;0BACV;wBAAE;0BAAAlE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,EAED7Q,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEuO,aAAa,CAAC,CAAC,CAAC,CAAC,EACrDtP,IAAI,IAAIsP,aAAa,CAAC,CAAC,CAAC,CAAC9I,GAAG,KAAKxG,IAAI,CAACwG,GAAG,iBACxCjH,OAAA;0BACE+R,SAAS,EAAC,4DAA4D;0BACtE/J,KAAK,EAAE;4BACL2L,UAAU,EAAE,0CAA0C;4BACtDxL,SAAS,EAAE;0BACb,CAAE;0BAAA6J,QAAA,eAEFhS,OAAA,CAACjB,MAAM;4BAACgT,SAAS,EAAC;0BAAuB;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACzC,CACN;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,eAGNpS,OAAA;wBAAK+R,SAAS,EAAC,6CAA6C;wBAAAC,QAAA,gBAC1DhS,OAAA;0BACE+R,SAAS,EAAC,6BAA6B;0BACvC/J,KAAK,EAAE;4BACL9D,KAAK,EAAE6L,aAAa,CAAC,CAAC,CAAC,CAAC3J,IAAI,CAAC/B,SAAS;4BACtCyQ,UAAU,EAAG,eAAc/E,aAAa,CAAC,CAAC,CAAC,CAAC3J,IAAI,CAAC9B,WAAY,EAAC;4BAC9DuC,MAAM,EAAE;0BACV,CAAE;0BAAAmL,QAAA,EAEDjC,aAAa,CAAC,CAAC,CAAC,CAACzG;wBAAI;0BAAA2I,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACpB,CAAC,EACJ1B,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC9I,GAAG,CAAC,iBAClCjH,OAAA;0BACE+R,SAAS,EAAC,yDAAyD;0BACnE/J,KAAK,EAAE;4BACL2L,UAAU,EAAE,0CAA0C;4BACtDzP,KAAK,EAAE,SAAS;4BAChBiE,SAAS,EAAE,+BAA+B;4BAC1C2L,MAAM,EAAE,mBAAmB;4BAC3BL,QAAQ,EAAE;0BACZ,CAAE;0BAAAzB,QAAA,EACH;wBAED;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CACP;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,eAENpS,OAAA;wBACE+R,SAAS,EAAG,6DAA4DhC,aAAa,CAAC,CAAC,CAAC,CAAC3J,IAAI,CAAClC,KAAM,qDAAqD;wBACzJ8D,KAAK,EAAE;0BACL2L,UAAU,EAAG,2BAA0B5D,aAAa,CAAC,CAAC,CAAC,CAAC3J,IAAI,CAACzB,WAAY,KAAIoL,aAAa,CAAC,CAAC,CAAC,CAAC3J,IAAI,CAAChC,SAAU,GAAE;0BAC/GF,KAAK,EAAE,SAAS;0BAChB4Q,UAAU,EAAE,6BAA6B;0BACzC3M,SAAS,EAAG,cAAa4H,aAAa,CAAC,CAAC,CAAC,CAAC3J,IAAI,CAAC9B,WAAY,IAAG;0BAC9DwP,MAAM,EAAE;wBACV,CAAE;wBAAA9B,QAAA,GAEDjC,aAAa,CAAC,CAAC,CAAC,CAAC3J,IAAI,CAAC5B,IAAI,iBAAIpG,KAAK,CAACkY,aAAa,CAACvG,aAAa,CAAC,CAAC,CAAC,CAAC3J,IAAI,CAAC5B,IAAI,EAAE;0BAC7EuN,SAAS,EAAE,SAAS;0BACpB/J,KAAK,EAAE;4BAAE9D,KAAK,EAAE;0BAAU;wBAC5B,CAAC,CAAC,eACFlE,OAAA;0BAAMgI,KAAK,EAAE;4BAAE9D,KAAK,EAAE;0BAAU,CAAE;0BAAA8N,QAAA,EAAEjC,aAAa,CAAC,CAAC,CAAC,CAAC3J,IAAI,CAAC3B;wBAAK;0BAAAwN,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpE,CAAC,eAGNpS,OAAA;wBAAK+R,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,gBACtChS,OAAA;0BAAK+R,SAAS,EAAC,oBAAoB;0BAAC/J,KAAK,EAAE;4BACzC9D,KAAK,EAAE6L,aAAa,CAAC,CAAC,CAAC,CAAC3J,IAAI,CAAC/B,SAAS;4BACtCyQ,UAAU,EAAG,eAAc/E,aAAa,CAAC,CAAC,CAAC,CAAC3J,IAAI,CAAC9B,WAAY,EAAC;4BAC9DuC,MAAM,EAAE;0BACV,CAAE;0BAAAmL,QAAA,GACCjC,aAAa,CAAC,CAAC,CAAC,CAAC7J,OAAO,CAAC+N,cAAc,CAAC,CAAC,EAAC,KAC7C;wBAAA;0BAAAhC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eAENpS,OAAA;0BAAK+R,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,gBAChDhS,OAAA;4BAAK+R,SAAS,EAAC,aAAa;4BAAAC,QAAA,gBAC1BhS,OAAA;8BAAK+R,SAAS,EAAC,wCAAwC;8BAAAC,QAAA,gBACrDhS,OAAA,CAACf,OAAO;gCAAC8S,SAAS,EAAC,SAAS;gCAAC/J,KAAK,EAAE;kCAAE9D,KAAK,EAAE6L,aAAa,CAAC,CAAC,CAAC,CAAC3J,IAAI,CAAChC;gCAAU;8BAAE;gCAAA6N,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,eAClFpS,OAAA;gCAAM+R,SAAS,EAAC,WAAW;gCAAC/J,KAAK,EAAE;kCAAE9D,KAAK,EAAE6L,aAAa,CAAC,CAAC,CAAC,CAAC3J,IAAI,CAAChC;gCAAU,CAAE;gCAAA4N,QAAA,EAC3EjC,aAAa,CAAC,CAAC,CAAC,CAAC5G;8BAAiB;gCAAA8I,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC/B,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACJ,CAAC,eACNpS,OAAA;8BAAK+R,SAAS,EAAC,oBAAoB;8BAAC/J,KAAK,EAAE;gCAAE9D,KAAK,EAAE6L,aAAa,CAAC,CAAC,CAAC,CAAC3J,IAAI,CAAChC;8BAAU,CAAE;8BAAA4N,QAAA,EAAC;4BAAO;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjG,CAAC,eACNpS,OAAA;4BAAK+R,SAAS,EAAC,aAAa;4BAAAC,QAAA,gBAC1BhS,OAAA;8BAAK+R,SAAS,EAAC,wCAAwC;8BAAAC,QAAA,gBACrDhS,OAAA,CAAChB,OAAO;gCAAC+S,SAAS,EAAC,SAAS;gCAAC/J,KAAK,EAAE;kCAAE9D,KAAK,EAAE;gCAAU;8BAAE;gCAAA+N,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,eAC5DpS,OAAA;gCAAM+R,SAAS,EAAC,WAAW;gCAAC/J,KAAK,EAAE;kCAAE9D,KAAK,EAAE6L,aAAa,CAAC,CAAC,CAAC,CAAC3J,IAAI,CAAChC;gCAAU,CAAE;gCAAA4N,QAAA,EAC3EjC,aAAa,CAAC,CAAC,CAAC,CAAChG;8BAAa;gCAAAkI,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC3B,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACJ,CAAC,eACNpS,OAAA;8BAAK+R,SAAS,EAAC,oBAAoB;8BAAC/J,KAAK,EAAE;gCAAE9D,KAAK,EAAE6L,aAAa,CAAC,CAAC,CAAC,CAAC3J,IAAI,CAAChC;8BAAU,CAAE;8BAAA4N,QAAA,EAAC;4BAAM;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAChG,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,GAxLA,SAAQrC,aAAa,CAAC,CAAC,CAAC,CAAC9I,GAAI,EAAC;kBAAAgL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAyL1B,CACb,EAGArC,aAAa,CAAC,CAAC,CAAC,iBACf/P,OAAA,CAACxB,MAAM,CAACgU,GAAG;kBAET0D,GAAG,EAAEzV,IAAI,IAAIgM,MAAM,CAACsD,aAAa,CAAC,CAAC,CAAC,CAAC9I,GAAG,CAAC,KAAKwF,MAAM,CAAChM,IAAI,CAACwG,GAAG,CAAC,GAAGrD,aAAa,GAAG,IAAK;kBACtF,gBAAcmM,aAAa,CAAC,CAAC,CAAC,CAAC9I,GAAI;kBACnC,kBAAgB,CAAE;kBAClBwL,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEI,CAAC,EAAE,GAAG;oBAAEH,CAAC,EAAE;kBAAG,CAAE;kBACvCC,OAAO,EAAE;oBACPF,OAAO,EAAE,CAAC;oBACVI,CAAC,EAAE,CAAC;oBACJH,CAAC,EAAE,CAAC;oBACJY,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;oBACnBgC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;kBACpB,CAAE;kBACFrN,UAAU,EAAE;oBACVgL,KAAK,EAAE,GAAG;oBACVH,QAAQ,EAAE,GAAG;oBACbQ,KAAK,EAAE;sBAAER,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEiC,IAAI,EAAE;oBAAY,CAAC;oBAC3DK,OAAO,EAAE;sBAAExC,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEiC,IAAI,EAAE;oBAAY;kBAC9D,CAAE;kBACF5B,UAAU,EAAE;oBAAEC,KAAK,EAAE,IAAI;oBAAEZ,CAAC,EAAE,CAAC;kBAAG,CAAE;kBACpCZ,SAAS,EAAG,oBACVpB,mBAAmB,CAACZ,aAAa,CAAC,CAAC,CAAC,CAAC9I,GAAG,CAAC,GACrC,yCAAyC,GACzC,EACL,EAAE;kBACHe,KAAK,EAAE;oBACLmO,MAAM,EAAE,OAAO;oBACflO,SAAS,EAAE0I,mBAAmB,CAACZ,aAAa,CAAC,CAAC,CAAC,CAAC9I,GAAG,CAAC,GAAG,aAAa,GAAG,UAAU;oBACjFJ,MAAM,EAAE8J,mBAAmB,CAACZ,aAAa,CAAC,CAAC,CAAC,CAAC9I,GAAG,CAAC,GAC7C,0EAA0E,GAC1E,MAAM;oBACViB,UAAU,EAAE,eAAe;oBAC3B4L,MAAM,EAAEnD,mBAAmB,CAACZ,aAAa,CAAC,CAAC,CAAC,CAAC9I,GAAG,CAAC,GAAG,mBAAmB,GAAG,MAAM;oBAChFmP,YAAY,EAAE1F,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC9I,GAAG,CAAC,GAAG,MAAM,GAAG,KAAK;oBAClE0M,UAAU,EAAEjD,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC9I,GAAG,CAAC,GAAG,wEAAwE,GAAG;kBAC/H,CAAE;kBAAA+K,QAAA,gBAGFhS,OAAA;oBAAK+R,SAAS,EAAC,yJAAyJ;oBAAAC,QAAA,eACtKhS,OAAA;sBAAM+R,SAAS,EAAC,iDAAiD;sBAAAC,QAAA,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzE,CAAC,eAGNpS,OAAA;oBACE+R,SAAS,EAAG,8BAA6BhC,aAAa,CAAC,CAAC,CAAC,CAAC3J,IAAI,CAAClC,KAAM,mBAAkB6L,aAAa,CAAC,CAAC,CAAC,CAAC3J,IAAI,CAAC7B,IAAK,kBAAkB;oBACpIyD,KAAK,EAAE;sBACLG,SAAS,EAAG,cAAa4H,aAAa,CAAC,CAAC,CAAC,CAAC3J,IAAI,CAAC9B,WAAY,IAAG;sBAC9D+R,KAAK,EAAE;oBACT,CAAE;oBAAArE,QAAA,eAEFhS,OAAA;sBACE+R,SAAS,EAAG,GAAEhC,aAAa,CAAC,CAAC,CAAC,CAAC3J,IAAI,CAACjC,OAAQ,uEAAuE;sBAAA6N,QAAA,gBAEnHhS,OAAA;wBAAK+R,SAAS,EAAC;sBAAgE;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAGtFpS,OAAA;wBACE+R,SAAS,EAAC,sMAAsM;wBAChN/J,KAAK,EAAE;0BACL9D,KAAK,EAAE,SAAS;0BAChB4P,MAAM,EAAE;wBACV,CAAE;wBAAA9B,QAAA,EACH;sBAED;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAGNpS,OAAA;wBAAK+R,SAAS,EAAG,yBAAwBtR,IAAI,IAAIsP,aAAa,CAAC,CAAC,CAAC,CAAC9I,GAAG,KAAKxG,IAAI,CAACwG,GAAG,GAAG,wCAAwC,GAAG,EAAG,EAAE;wBAAA+K,QAAA,eACnIhS,OAAA,CAACF,cAAc;0BACbW,IAAI,EAAEsP,aAAa,CAAC,CAAC,CAAE;0BACvB6D,IAAI,EAAC,IAAI;0BACTC,gBAAgB,EAAE,IAAK;0BACvB7L,KAAK,EAAE;4BACLqO,KAAK,EAAE,MAAM;4BACbF,MAAM,EAAE;0BACV;wBAAE;0BAAAlE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC,eAGNpS,OAAA;wBACE+R,SAAS,EAAC,iCAAiC;wBAC3C/J,KAAK,EAAE;0BAAE9D,KAAK,EAAE6L,aAAa,CAAC,CAAC,CAAC,CAAC3J,IAAI,CAAC/B;wBAAU,CAAE;wBAAA2N,QAAA,EAEjDjC,aAAa,CAAC,CAAC,CAAC,CAACzG;sBAAI;wBAAA2I,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB,CAAC,eAELpS,OAAA;wBAAK+R,SAAS,EAAC,yBAAyB;wBAAC/J,KAAK,EAAE;0BAAE9D,KAAK,EAAE6L,aAAa,CAAC,CAAC,CAAC,CAAC3J,IAAI,CAAChC;wBAAU,CAAE;wBAAA4N,QAAA,GACxFjC,aAAa,CAAC,CAAC,CAAC,CAAC7J,OAAO,CAAC+N,cAAc,CAAC,CAAC,EAAC,KAC7C;sBAAA;wBAAAhC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAENpS,OAAA;wBAAK+R,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,gBAChDhS,OAAA;0BAAMgI,KAAK,EAAE;4BAAE9D,KAAK,EAAE6L,aAAa,CAAC,CAAC,CAAC,CAAC3J,IAAI,CAAChC;0BAAU,CAAE;0BAAA4N,QAAA,GAAC,eACpD,EAACjC,aAAa,CAAC,CAAC,CAAC,CAAC5G,iBAAiB;wBAAA;0BAAA8I,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAClC,CAAC,eACPpS,OAAA;0BAAMgI,KAAK,EAAE;4BAAE9D,KAAK,EAAE6L,aAAa,CAAC,CAAC,CAAC,CAAC3J,IAAI,CAAChC;0BAAU,CAAE;0BAAA4N,QAAA,GAAC,eACpD,EAACjC,aAAa,CAAC,CAAC,CAAC,CAAChG,aAAa;wBAAA;0BAAAkI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9B,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,GAnGA,SAAQrC,aAAa,CAAC,CAAC,CAAC,CAAC9I,GAAI,EAAC;kBAAAgL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAoG1B,CACb;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASI,CACb,EAGAnP,cAAc,GACb;YACAJ,WAAW,CAACsE,MAAM,GAAG,CAAC,iBACpBnH,OAAA,CAACxB,MAAM,CAACgU,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BzK,UAAU,EAAE;gBAAEgL,KAAK,EAAE,CAAC;gBAAEH,QAAQ,EAAE;cAAI,CAAE;cACxChB,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBAGtChS,OAAA;gBAAK+R,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxChS,OAAA,CAACxB,MAAM,CAAC+X,EAAE;kBACRxE,SAAS,EAAC,kDAAkD;kBAC5D/J,KAAK,EAAE;oBACL2L,UAAU,EAAG,0BAAyB5P,YAAY,CAACd,cAAc,CAAC,CAAC0B,WAAY,KAAIZ,YAAY,CAACd,cAAc,CAAC,CAACmB,SAAU,GAAE;oBAC5HwQ,oBAAoB,EAAE,MAAM;oBAC5BC,mBAAmB,EAAE,aAAa;oBAClCC,UAAU,EAAE,6BAA6B;oBACzCjO,MAAM,EAAG,wBAAuB9C,YAAY,CAACd,cAAc,CAAC,CAAC0B,WAAY;kBAC3E,CAAE;kBACFiO,OAAO,EAAE;oBAAEW,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;kBAAE,CAAE;kBACjCrL,UAAU,EAAE;oBAAE6K,QAAQ,EAAE,CAAC;oBAAEC,MAAM,EAAEC;kBAAS,CAAE;kBAAAjB,QAAA,GAE7CjO,YAAY,CAACd,cAAc,CAAC,CAAC4B,UAAU,EAAC,GAAC,EAACd,YAAY,CAACd,cAAc,CAAC,CAACwB,KAAK,EAAC,UAAQ,EAACV,YAAY,CAACd,cAAc,CAAC,CAAC4B,UAAU;gBAAA;kBAAAoN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrH,CAAC,eACZpS,OAAA;kBAAG+R,SAAS,EAAC,gDAAgD;kBAAAC,QAAA,GAC1DnP,WAAW,CAACsE,MAAM,EAAC,2BACtB;gBAAA;kBAAA8K,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJpS,OAAA,CAACxB,MAAM,CAAC6U,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BhB,OAAO,EAAEA,CAAA,KAAMrP,iBAAiB,CAAC,IAAI,CAAE;kBACvC6O,SAAS,EAAC,mJAAmJ;kBAAAC,QAAA,EAC9J;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC,eAGNpS,OAAA;gBAAK+R,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,eACrChS,OAAA;kBAAK+R,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EACjCnP,WAAW,CAACwG,GAAG,CAAC,CAACmN,QAAQ,EAAE7M,KAAK,KAAK;oBACpC,MAAM8M,UAAU,GAAG9M,KAAK,GAAG,CAAC;oBAC5B,MAAM+G,aAAa,GAAGjQ,IAAI,IAAIgM,MAAM,CAAC+J,QAAQ,CAACvP,GAAG,CAAC,KAAKwF,MAAM,CAAChM,IAAI,CAACwG,GAAG,CAAC;oBAEvE,oBACEjH,OAAA,CAACxB,MAAM,CAACgU,GAAG;sBAET0D,GAAG,EAAExF,aAAa,GAAG7M,WAAW,GAAG,IAAK;sBACxC,gBAAc2S,QAAQ,CAACvP,GAAI;sBAC3B,kBAAgBwP,UAAW;sBAC3BhE,OAAO,EAAE;wBAAEC,OAAO,EAAE,CAAC;wBAAEC,CAAC,EAAE;sBAAG,CAAE;sBAC/BC,OAAO,EAAE;wBAAEF,OAAO,EAAE,CAAC;wBAAEC,CAAC,EAAE;sBAAE,CAAE;sBAC9BzK,UAAU,EAAE;wBAAEgL,KAAK,EAAE,GAAG,GAAGvJ,KAAK,GAAG,IAAI;wBAAEoJ,QAAQ,EAAE;sBAAI,CAAE;sBACzDO,UAAU,EAAE;wBAAEC,KAAK,EAAE,IAAI;wBAAEZ,CAAC,EAAE,CAAC;sBAAE,CAAE;sBACnCZ,SAAS,EAAG,+BACVpB,mBAAmB,CAAC6F,QAAQ,CAACvP,GAAG,CAAC,GAC7B,yCAAyC,GACzC,EACL,EAAE;sBACHe,KAAK,EAAE;wBACLC,SAAS,EAAE0I,mBAAmB,CAAC6F,QAAQ,CAACvP,GAAG,CAAC,GAAG,aAAa,GAAG,UAAU;wBACzEJ,MAAM,EAAE8J,mBAAmB,CAAC6F,QAAQ,CAACvP,GAAG,CAAC,GACrC,2EAA2E,GAC3E,MAAM;wBACViB,UAAU,EAAE,eAAe;wBAC3B4L,MAAM,EAAEnD,mBAAmB,CAAC6F,QAAQ,CAACvP,GAAG,CAAC,GAAG,mBAAmB,GAAG,MAAM;wBACxEmP,YAAY,EAAEzF,mBAAmB,CAAC6F,QAAQ,CAACvP,GAAG,CAAC,GAAG,MAAM,GAAG,KAAK;wBAChE0M,UAAU,EAAEjD,aAAa,GAAG,2EAA2E,GAAG,aAAa;wBACvHJ,QAAQ,EAAE,UAAU;wBACpB8E,MAAM,EAAE1E,aAAa,GAAG,EAAE,GAAG;sBAC/B,CAAE;sBAAAsB,QAAA,eAGFhS,OAAA;wBACE+R,SAAS,EAAG,oBAAmByE,QAAQ,CAACpQ,IAAI,CAAClC,KAAM,sBAAqBsS,QAAQ,CAACpQ,IAAI,CAAC7B,IAAK,uDAAuD;wBAClJyD,KAAK,EAAE;0BACLG,SAAS,EAAG,cAAaqO,QAAQ,CAACpQ,IAAI,CAAC9B,WAAY;wBACrD,CAAE;wBAAA0N,QAAA,eAEFhS,OAAA;0BACE+R,SAAS,EAAG,GAAEyE,QAAQ,CAACpQ,IAAI,CAACjC,OAAQ,oFAAoF;0BACxH6D,KAAK,EAAE;4BACL8L,MAAM,EAAG,aAAY0C,QAAQ,CAACpQ,IAAI,CAACzB,WAAY;0BACjD,CAAE;0BAAAqN,QAAA,gBAGFhS,OAAA;4BAAK+R,SAAS,EAAC;0BAA2E;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eAGjGpS,OAAA;4BAAK+R,SAAS,EAAC,uCAAuC;4BAAAC,QAAA,gBAEpDhS,OAAA;8BAAK+R,SAAS,EAAC,UAAU;8BAAAC,QAAA,eACvBhS,OAAA;gCACE+R,SAAS,EAAC,kJAAkJ;gCAC5J/J,KAAK,EAAE;kCACL9D,KAAK,EAAE,SAAS;kCAChB4Q,UAAU,EAAE,6BAA6B;kCACzChB,MAAM,EAAE,iCAAiC;kCACzC3L,SAAS,EAAE;gCACb,CAAE;gCAAA6J,QAAA,GACH,GACE,EAACyE,UAAU;8BAAA;gCAAAxE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACT;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH,CAAC,eAGNpS,OAAA;8BAAK+R,SAAS,EAAC,UAAU;8BAAAC,QAAA,gBACvBhS,OAAA,CAACF,cAAc;gCACbW,IAAI,EAAE+V,QAAS;gCACf5C,IAAI,EAAC,IAAI;gCACTC,gBAAgB,EAAE,IAAK;gCACvB7L,KAAK,EAAE;kCACLqO,KAAK,EAAE,MAAM;kCACbF,MAAM,EAAE;gCACV;8BAAE;gCAAAlE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACH,CAAC,EAED1B,aAAa,iBACZ1Q,OAAA;gCACE+R,SAAS,EAAC,8FAA8F;gCACxG/J,KAAK,EAAE;kCACL2L,UAAU,EAAE,0CAA0C;kCACtDxL,SAAS,EAAE;gCACb,CAAE;gCAAA6J,QAAA,eAEFhS,OAAA,CAACjB,MAAM;kCAACgT,SAAS,EAAC;gCAA2B;kCAAAE,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC7C,CACN;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACE,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,eAGNpS,OAAA;4BAAK+R,SAAS,EAAC,qBAAqB;4BAAAC,QAAA,eAClChS,OAAA;8BAAK+R,SAAS,EAAC,WAAW;8BAAAC,QAAA,gBAExBhS,OAAA;gCAAK+R,SAAS,EAAC,8BAA8B;gCAAAC,QAAA,gBAC3ChS,OAAA;kCACE+R,SAAS,EAAC,yCAAyC;kCACnD/J,KAAK,EAAE;oCACL9D,KAAK,EAAEsS,QAAQ,CAACpQ,IAAI,CAAC/B,SAAS;oCAC9ByQ,UAAU,EAAG,eAAc0B,QAAQ,CAACpQ,IAAI,CAAC9B,WAAY,EAAC;oCACtDuC,MAAM,EAAE;kCACV,CAAE;kCAAAmL,QAAA,EAEDwE,QAAQ,CAAClN;gCAAI;kCAAA2I,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACZ,CAAC,EACJ1B,aAAa,iBACZ1Q,OAAA;kCACE+R,SAAS,EAAC,yDAAyD;kCACnE/J,KAAK,EAAE;oCACL2L,UAAU,EAAE,mDAAmD;oCAC/DzP,KAAK,EAAE,SAAS;oCAChBiE,SAAS,EAAE,8DAA8D;oCACzE2L,MAAM,EAAE,mBAAmB;oCAC3BgB,UAAU,EAAE,6BAA6B;oCACzCrB,QAAQ,EAAE,MAAM;oCAChBiC,UAAU,EAAE;kCACd,CAAE;kCAAA1D,QAAA,EACH;gCAED;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAM,CACP;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACE,CAAC,eAGNpS,OAAA;gCAAK+R,SAAS,EAAC,8BAA8B;gCAAAC,QAAA,GAC1CwE,QAAQ,CAAC7N,KAAK,EAAC,gBAAS,EAAC6N,QAAQ,CAAC3M,KAAK;8BAAA;gCAAAoI,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACrC,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,eAGNpS,OAAA;4BAAK+R,SAAS,EAAC,6CAA6C;4BAAAC,QAAA,gBAE1DhS,OAAA;8BACE+R,SAAS,EAAC,oCAAoC;8BAC9C/J,KAAK,EAAE;gCACL9D,KAAK,EAAEsS,QAAQ,CAACpQ,IAAI,CAAC/B,SAAS;gCAC9ByQ,UAAU,EAAG,eAAc0B,QAAQ,CAACpQ,IAAI,CAAC9B,WAAY,EAAC;gCACtDuC,MAAM,EAAE;8BACV,CAAE;8BAAAmL,QAAA,GAEDwE,QAAQ,CAACtQ,OAAO,CAAC+N,cAAc,CAAC,CAAC,EAAC,KACrC;4BAAA;8BAAAhC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eAGNpS,OAAA;8BAAK+R,SAAS,EAAC,iCAAiC;8BAAAC,QAAA,gBAC9ChS,OAAA;gCACE+R,SAAS,EAAC,8CAA8C;gCACxD/J,KAAK,EAAE;kCACL0O,eAAe,EAAG,GAAEF,QAAQ,CAACpQ,IAAI,CAACzB,WAAY,IAAG;kCACjDT,KAAK,EAAEsS,QAAQ,CAACpQ,IAAI,CAAChC;gCACvB,CAAE;gCAAA4N,QAAA,gBAEFhS,OAAA,CAACf,OAAO;kCAAC8S,SAAS,EAAC;gCAAS;kCAAAE,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE,CAAC,eAC/BpS,OAAA;kCAAM+R,SAAS,EAAC,aAAa;kCAAAC,QAAA,EAAEwE,QAAQ,CAACrN;gCAAiB;kCAAA8I,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAO,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC9D,CAAC,eACNpS,OAAA;gCACE+R,SAAS,EAAC,8CAA8C;gCACxD/J,KAAK,EAAE;kCACL0O,eAAe,EAAE,WAAW;kCAC5BxS,KAAK,EAAE;gCACT,CAAE;gCAAA8N,QAAA,gBAEFhS,OAAA,CAAChB,OAAO;kCAAC+S,SAAS,EAAC;gCAAS;kCAAAE,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE,CAAC,eAC/BpS,OAAA;kCAAM+R,SAAS,EAAC,aAAa;kCAAAC,QAAA,EAAEwE,QAAQ,CAACzM;gCAAa;kCAAAkI,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAO,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC1D,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC,GApKDoE,QAAQ,CAACvP,GAAG;sBAAAgL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAqKP,CAAC;kBAEjB,CAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,GAED;YACAxM,MAAM,CAACS,IAAI,CAAClD,YAAY,CAAC,CAACgE,MAAM,GAAG,CAAC,iBAClCnH,OAAA,CAACxB,MAAM,CAACgU,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BzK,UAAU,EAAE;gBAAEgL,KAAK,EAAE,CAAC;gBAAEH,QAAQ,EAAE;cAAI,CAAE;cACxChB,SAAS,EAAC,4BAA4B;cACtC3E,EAAE,EAAC,yBAAyB;cAAA4E,QAAA,gBAG5BhS,OAAA;gBAAK+R,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxChS,OAAA,CAACxB,MAAM,CAAC+X,EAAE;kBACRxE,SAAS,EAAC,kDAAkD;kBAC5D/J,KAAK,EAAE;oBACL2L,UAAU,EAAE,mDAAmD;oBAC/DiB,oBAAoB,EAAE,MAAM;oBAC5BC,mBAAmB,EAAE,aAAa;oBAClCC,UAAU,EAAE,6BAA6B;oBACzCjO,MAAM,EAAE;kBACV,CAAE;kBACF+L,OAAO,EAAE;oBAAEW,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;kBAAE,CAAE;kBACjCrL,UAAU,EAAE;oBAAE6K,QAAQ,EAAE,CAAC;oBAAEC,MAAM,EAAEC;kBAAS,CAAE;kBAAAjB,QAAA,EAC/C;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACZpS,OAAA;kBAAG+R,SAAS,EAAC,gDAAgD;kBAAAC,QAAA,EAAC;gBAE9D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAGNpS,OAAA;gBAAK+R,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAC9C5J,iBAAiB,CAAC,CAAC,CAACiB,GAAG,CAAE/C,SAAS,IAAK;kBACtC,MAAMZ,MAAM,GAAG3B,YAAY,CAACuC,SAAS,CAAC;kBACtC,MAAMuJ,UAAU,GAAG1M,YAAY,CAACmD,SAAS,CAAC;kBAC1C,MAAMqQ,QAAQ,GAAG9G,UAAU,CAACtP,KAAK,CAAC6I,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;kBAE/C,oBACEpJ,OAAA,CAACxB,MAAM,CAACgU,GAAG;oBAET0D,GAAG,EAAGU,EAAE,IAAMnT,UAAU,CAACkE,OAAO,CAACrB,SAAS,CAAC,GAAGsQ,EAAI;oBAClDnE,OAAO,EAAE;sBAAEC,OAAO,EAAE,CAAC;sBAAEC,CAAC,EAAE;oBAAG,CAAE;oBAC/BC,OAAO,EAAE;sBAAEF,OAAO,EAAE,CAAC;sBAAEC,CAAC,EAAE;oBAAE,CAAE;oBAC9BzK,UAAU,EAAE;sBAAEgL,KAAK,EAAE,GAAG;sBAAEH,QAAQ,EAAE;oBAAI,CAAE;oBAC1ChB,SAAS,EAAC,mGAAmG;oBAC7G3E,EAAE,EAAG,UAAS9G,SAAU,EAAE;oBAC1B,eAAaA,SAAU;oBAAA0L,QAAA,gBAGvBhS,OAAA;sBAAK+R,SAAS,EAAC,wCAAwC;sBAAAC,QAAA,gBACrDhS,OAAA;wBAAK+R,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,gBACtChS,OAAA;0BACE+R,SAAS,EAAC,gEAAgE;0BAC1E/J,KAAK,EAAE;4BACL2L,UAAU,EAAG,2BAA0BjO,MAAM,CAACf,WAAY,OAAMe,MAAM,CAACtB,SAAU,KAAI;4BACrF0P,MAAM,EAAG,aAAYpO,MAAM,CAACf,WAAY,IAAG;4BAC3CwD,SAAS,EAAG,cAAazC,MAAM,CAACpB,WAAY;0BAC9C,CAAE;0BAAA0N,QAAA,EAEDtM,MAAM,CAACb;wBAAU;0BAAAoN,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACf,CAAC,eACNpS,OAAA;0BAAAgS,QAAA,gBACEhS,OAAA;4BACE+R,SAAS,EAAC,0BAA0B;4BACpC/J,KAAK,EAAE;8BACL9D,KAAK,EAAEwB,MAAM,CAACrB,SAAS;8BACvByQ,UAAU,EAAG,eAAcpP,MAAM,CAACpB,WAAY,EAAC;8BAC/CuC,MAAM,EAAE;4BACV,CAAE;4BAAAmL,QAAA,GAEDtM,MAAM,CAACjB,KAAK,EAAC,SAChB;0BAAA;4BAAAwN,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACLpS,OAAA;4BAAG+R,SAAS,EAAC,uBAAuB;4BAAAC,QAAA,GACjCnC,UAAU,CAACtP,KAAK,CAAC4G,MAAM,EAAC,oBAAa,EAACzB,MAAM,CAAChB,WAAW;0BAAA;4BAAAuN,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACxD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNpS,OAAA,CAACxB,MAAM,CAAC6U,MAAM;wBACZC,UAAU,EAAE;0BAAEC,KAAK,EAAE;wBAAK,CAAE;wBAC5BC,QAAQ,EAAE;0BAAED,KAAK,EAAE;wBAAK,CAAE;wBAC1BhB,OAAO,EAAEA,CAAA,KAAMnL,kBAAkB,CAACd,SAAS,CAAE;wBAC7CyL,SAAS,EAAC,gJAAgJ;wBAAAC,QAAA,GAC3J,YACW,EAACnC,UAAU,CAACtP,KAAK,CAAC4G,MAAM,EAAC,GACrC;sBAAA;wBAAA8K,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAe,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACb,CAAC,eAGNpS,OAAA;sBAAK+R,SAAS,EAAC,sDAAsD;sBAAAC,QAAA,EAClE2E,QAAQ,CAACtN,GAAG,CAAC,CAACmN,QAAQ,EAAE7M,KAAK,KAAK;wBACjC,MAAM+G,aAAa,GAAGjQ,IAAI,IAAI+V,QAAQ,CAACvP,GAAG,KAAKxG,IAAI,CAACwG,GAAG;wBACvD,MAAM4P,UAAU,GAAGlN,KAAK,GAAG,CAAC;wBAE5B,oBACE3J,OAAA,CAACxB,MAAM,CAACgU,GAAG;0BAET,gBAAcgE,QAAQ,CAACvP,GAAI;0BAC3B,kBAAgB4P,UAAW;0BAC3BpE,OAAO,EAAE;4BAAEC,OAAO,EAAE,CAAC;4BAAEa,KAAK,EAAE;0BAAI,CAAE;0BACpCX,OAAO,EAAE;4BAAEF,OAAO,EAAE,CAAC;4BAAEa,KAAK,EAAE;0BAAE,CAAE;0BAClCrL,UAAU,EAAE;4BAAEgL,KAAK,EAAE,GAAG,GAAGvJ,KAAK,GAAG,GAAG;4BAAEoJ,QAAQ,EAAE;0BAAI,CAAE;0BACxDO,UAAU,EAAE;4BAAEC,KAAK,EAAE,IAAI;4BAAEZ,CAAC,EAAE,CAAC;0BAAE,CAAE;0BACnCZ,SAAS,EAAG,YACVpB,mBAAmB,CAAC6F,QAAQ,CAACvP,GAAG,CAAC,GAC7B,2BAA2B,GAC3B,EACL,EAAE;0BAAA+K,QAAA,eAEHhS,OAAA;4BACE+R,SAAS,EAAG,qBAAoByE,QAAQ,CAACpQ,IAAI,CAAClC,KAAM,qBAAoBsS,QAAQ,CAACpQ,IAAI,CAAC7B,IAAK,YAAY;4BACvGyD,KAAK,EAAE;8BACLG,SAAS,EAAG,cAAaqO,QAAQ,CAACpQ,IAAI,CAAC9B,WAAY;4BACrD,CAAE;4BAAA0N,QAAA,eAEFhS,OAAA;8BACE+R,SAAS,EAAG,GAAEyE,QAAQ,CAACpQ,IAAI,CAACjC,OAAQ,uEAAuE;8BAAA6N,QAAA,gBAE3GhS,OAAA;gCAAK+R,SAAS,EAAC;8BAAgE;gCAAAE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAM,CAAC,eAGtFpS,OAAA;gCACE+R,SAAS,EAAC,mGAAmG;gCAC7G/J,KAAK,EAAE;kCACL2L,UAAU,EAAEjO,MAAM,CAACf,WAAW;kCAC9BT,KAAK,EAAE,SAAS;kCAChB4P,MAAM,EAAE;gCACV,CAAE;gCAAA9B,QAAA,GACH,GACE,EAAC6E,UAAU;8BAAA;gCAAA5E,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACT,CAAC,eAGNpS,OAAA;gCAAK+R,SAAS,EAAG,yBACfrB,aAAa,GACT,wCAAwC,GACxC,EACL,EAAE;gCAAAsB,QAAA,gBACDhS,OAAA,CAACF,cAAc;kCACbW,IAAI,EAAE+V,QAAS;kCACf5C,IAAI,EAAC,IAAI;kCACTC,gBAAgB,EAAE,IAAK;kCACvB7L,KAAK,EAAE;oCACLqO,KAAK,EAAE,MAAM;oCACbF,MAAM,EAAE;kCACV;gCAAE;kCAAAlE,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACH,CAAC,EACD1B,aAAa,iBACZ1Q,OAAA;kCACE+R,SAAS,EAAC,iGAAiG;kCAC3G/J,KAAK,EAAE;oCACL2L,UAAU,EAAE,0CAA0C;oCACtDxL,SAAS,EAAE;kCACb,CAAE;kCAAA6J,QAAA,eAEFhS,OAAA,CAACjB,MAAM;oCAACgT,SAAS,EAAC;kCAA2B;oCAAAE,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAE;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAC7C,CACN;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACE,CAAC,eAGNpS,OAAA;gCACE+R,SAAS,EAAC,iCAAiC;gCAC3C/J,KAAK,EAAE;kCAAE9D,KAAK,EAAEsS,QAAQ,CAACpQ,IAAI,CAAC/B;gCAAU,CAAE;gCAAA2N,QAAA,GAEzCwE,QAAQ,CAAClN,IAAI,EACboH,aAAa,iBACZ1Q,OAAA;kCAAM+R,SAAS,EAAC,8BAA8B;kCAAAC,QAAA,EAAC;gCAAE;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAM,CACxD;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACC,CAAC,eAELpS,OAAA;gCAAK+R,SAAS,EAAC,yBAAyB;gCAAC/J,KAAK,EAAE;kCAAE9D,KAAK,EAAEsS,QAAQ,CAACpQ,IAAI,CAAChC;gCAAU,CAAE;gCAAA4N,QAAA,GAChFwE,QAAQ,CAACtQ,OAAO,CAAC+N,cAAc,CAAC,CAAC,EAAC,KACrC;8BAAA;gCAAAhC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAK,CAAC,eAENpS,OAAA;gCAAK+R,SAAS,EAAC,mCAAmC;gCAAAC,QAAA,gBAChDhS,OAAA;kCAAMgI,KAAK,EAAE;oCAAE9D,KAAK,EAAEsS,QAAQ,CAACpQ,IAAI,CAAChC;kCAAU,CAAE;kCAAA4N,QAAA,GAAC,eAC5C,EAACwE,QAAQ,CAACrN,iBAAiB;gCAAA;kCAAA8I,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAC1B,CAAC,eACPpS,OAAA;kCAAMgI,KAAK,EAAE;oCAAE9D,KAAK,EAAEsS,QAAQ,CAACpQ,IAAI,CAAChC;kCAAU,CAAE;kCAAA4N,QAAA,GAAC,eAC5C,EAACwE,QAAQ,CAACzM,aAAa;gCAAA;kCAAAkI,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACtB,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACJ,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH;wBAAC,GAxFDoE,QAAQ,CAACvP,GAAG;0BAAAgL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAyFP,CAAC;sBAEjB,CAAC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,EAGLvC,UAAU,CAACtP,KAAK,CAAC4G,MAAM,GAAG,CAAC,iBAC1BnH,OAAA;sBAAK+R,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,eAC/BhS,OAAA;wBAAG+R,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,GAAC,GAClC,EAACnC,UAAU,CAACtP,KAAK,CAAC4G,MAAM,GAAG,CAAC,EAAC,gCAChC;sBAAA;wBAAA8K,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CACN;kBAAA,GA7JI9L,SAAS;oBAAA2L,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OA8JJ,CAAC;gBAEjB,CAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAEf,EAMAvQ,WAAW,CAACsF,MAAM,GAAG,CAAC,iBACrBnH,OAAA,CAACxB,MAAM,CAACgU,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BzK,UAAU,EAAE;gBAAEgL,KAAK,EAAE,GAAG;gBAAEH,QAAQ,EAAE;cAAI,CAAE;cAC1ChB,SAAS,EAAC,sIAAsI;cAAAC,QAAA,eAEhJhS,OAAA;gBAAK+R,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BhS,OAAA;kBAAI+R,SAAS,EAAC,wBAAwB;kBAAC/J,KAAK,EAAE;oBAC5C9D,KAAK,EAAE,SAAS;oBAChB4Q,UAAU,EAAE,6BAA6B;oBACzCY,UAAU,EAAE;kBACd,CAAE;kBAAA1D,QAAA,EAAC;gBAA6B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrCpS,OAAA;kBAAK+R,SAAS,EAAC,+CAA+C;kBAAAC,QAAA,gBAC5DhS,OAAA;oBAAK+R,SAAS,EAAC,gCAAgC;oBAAAC,QAAA,gBAC7ChS,OAAA;sBAAK+R,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAC9CnQ,WAAW,CAACgF,MAAM,CAACG,CAAC,IAAIA,CAAC,CAAC0D,UAAU,KAAK,SAAS,CAAC,CAACvD;oBAAM;sBAAA8K,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxD,CAAC,eACNpS,OAAA;sBAAK+R,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAiB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC,eACNpS,OAAA;oBAAK+R,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5ChS,OAAA;sBAAK+R,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAC7CnQ,WAAW,CAACgF,MAAM,CAACG,CAAC,IAAIA,CAAC,CAAC0D,UAAU,KAAK,eAAe,CAAC,CAACvD;oBAAM;sBAAA8K,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9D,CAAC,eACNpS,OAAA;sBAAK+R,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAgB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC,eACNpS,OAAA;oBAAK+R,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,gBAC9ChS,OAAA;sBAAK+R,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAC/CnQ,WAAW,CAACgF,MAAM,CAACG,CAAC,IAAIA,CAAC,CAAC0D,UAAU,KAAK,WAAW,CAAC,CAACvD;oBAAM;sBAAA8K,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1D,CAAC,eACNpS,OAAA;sBAAK+R,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAkB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNpS,OAAA;kBAAG+R,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAE1C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,EAGAnQ,eAAe,IAAIA,eAAe,GAAG,CAAC,iBACrCjC,OAAA,CAACxB,MAAM,CAACgU,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEa,KAAK,EAAE;cAAI,CAAE;cACpCX,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEa,KAAK,EAAE;cAAE,CAAE;cAClCrL,UAAU,EAAE;gBAAEgL,KAAK,EAAE,GAAG;gBAAEH,QAAQ,EAAE;cAAI,CAAE;cAC1ChB,SAAS,EAAC,wIAAwI;cAAAC,QAAA,eAElJhS,OAAA;gBAAK+R,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BhS,OAAA;kBAAI+R,SAAS,EAAC,yBAAyB;kBAAC/J,KAAK,EAAE;oBAC7C9D,KAAK,EAAE,SAAS;oBAChB4Q,UAAU,EAAE,6BAA6B;oBACzCY,UAAU,EAAE;kBACd,CAAE;kBAAA1D,QAAA,EAAC;gBAAqB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7BpS,OAAA;kBAAK+R,SAAS,EAAC,0BAA0B;kBAAC/J,KAAK,EAAE;oBAC/C9D,KAAK,EAAE,SAAS;oBAChB4Q,UAAU,EAAE,6BAA6B;oBACzCY,UAAU,EAAE;kBACd,CAAE;kBAAA1D,QAAA,GAAC,GAAC,EAAC/P,eAAe;gBAAA;kBAAAgQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC3BpS,OAAA;kBAAG+R,SAAS,EAAC,SAAS;kBAAC/J,KAAK,EAAE;oBAC5B9D,KAAK,EAAE,SAAS;oBAChB4Q,UAAU,EAAE,6BAA6B;oBACzCY,UAAU,EAAE;kBACd,CAAE;kBAAA1D,QAAA,EAAC;gBAEH;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,eAGDpS,OAAA,CAACxB,MAAM,CAACgU,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BzK,UAAU,EAAE;gBAAEgL,KAAK,EAAE,CAAC;gBAAEH,QAAQ,EAAE;cAAI,CAAE;cACxChB,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAE7BhS,OAAA;gBAAK+R,SAAS,EAAC,8HAA8H;gBAAAC,QAAA,gBAC3IhS,OAAA,CAACxB,MAAM,CAACgU,GAAG;kBACTI,OAAO,EAAE;oBAAEW,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;kBAAE,CAAE;kBACjCrL,UAAU,EAAE;oBAAE6K,QAAQ,EAAE,CAAC;oBAAEC,MAAM,EAAEC;kBAAS,CAAE;kBAAAjB,QAAA,eAE9ChS,OAAA,CAACX,QAAQ;oBAAC0S,SAAS,EAAC;kBAAwC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC,eACbpS,OAAA;kBAAI+R,SAAS,EAAC,yBAAyB;kBAAC/J,KAAK,EAAE;oBAC7C9D,KAAK,EAAE,SAAS;oBAChB4Q,UAAU,EAAE,6BAA6B;oBACzCY,UAAU,EAAE;kBACd,CAAE;kBAAA1D,QAAA,EAAC;gBAAqB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7BpS,OAAA;kBAAG+R,SAAS,EAAC,gCAAgC;kBAAC/J,KAAK,EAAE;oBACnD9D,KAAK,EAAE,SAAS;oBAChB4Q,UAAU,EAAE,6BAA6B;oBACzCY,UAAU,EAAE;kBACd,CAAE;kBAAA1D,QAAA,EAAC;gBAGH;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJpS,OAAA,CAACxB,MAAM,CAAC6U,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BxB,SAAS,EAAC,sJAAsJ;kBAChKQ,OAAO,EAAEA,CAAA,KAAM9C,MAAM,CAACqH,QAAQ,CAACC,IAAI,GAAG,YAAa;kBAAA/E,QAAA,EACpD;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,EAGZvQ,WAAW,CAACsF,MAAM,KAAK,CAAC,IAAI,CAACpF,OAAO,iBACnC/B,OAAA,CAACxB,MAAM,CAACgU,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEa,KAAK,EAAE;cAAI,CAAE;cACpCX,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEa,KAAK,EAAE;cAAE,CAAE;cAClCxB,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAE7BhS,OAAA,CAACnB,QAAQ;gBAACkT,SAAS,EAAC;cAAsC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7DpS,OAAA;gBAAI+R,SAAS,EAAC,yBAAyB;gBAAC/J,KAAK,EAAE;kBAC7C9D,KAAK,EAAE,SAAS;kBAChB4Q,UAAU,EAAE,6BAA6B;kBACzCY,UAAU,EAAE;gBACd,CAAE;gBAAA1D,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxBpS,OAAA;gBAAG+R,SAAS,EAAC,SAAS;gBAAC/J,KAAK,EAAE;kBAC5B9D,KAAK,EAAE,SAAS;kBAChB4Q,UAAU,EAAE,6BAA6B;kBACzCY,UAAU,EAAE;gBACd,CAAE;gBAAA1D,QAAA,EAAC;cAEH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACJ,CAAC;AAEP,CAAC;AAAChS,EAAA,CA/0FID,kBAAkB;EAAA,QACJzB,WAAW,EA4CZC,WAAW;AAAA;AAAAqY,EAAA,GA7CxB7W,kBAAkB;AAi1FxB,eAAeA,kBAAkB;AAAC,IAAA6W,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}