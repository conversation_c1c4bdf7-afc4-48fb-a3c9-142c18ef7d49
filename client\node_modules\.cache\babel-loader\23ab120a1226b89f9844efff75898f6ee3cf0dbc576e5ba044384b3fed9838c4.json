{"ast": null, "code": "import axios from 'axios';\nimport { message } from 'antd';\nconst axiosInstance = axios.create({\n  // baseURL: 'https://backend-r0tz.onrender.com' ,\n  baseURL: 'http://localhost:8002',\n  timeout: 30000 // 30 seconds timeout for better user experience\n});\n\n// Add a request interceptor to dynamically set the authorization header\naxiosInstance.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    // Check if token is still valid before making request\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1]));\n      const currentTime = Date.now() / 1000;\n      if (payload.exp && payload.exp < currentTime) {\n        console.log('🔒 Token expired, clearing storage');\n        localStorage.removeItem('token');\n        localStorage.removeItem('user');\n        window.location.href = '/login';\n        return Promise.reject(new Error('Token expired'));\n      }\n    } catch (e) {\n      console.log('⚠️ Invalid token format, clearing storage');\n      localStorage.removeItem('token');\n      localStorage.removeItem('user');\n    }\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Add a response interceptor to handle authentication errors\naxiosInstance.interceptors.response.use(response => {\n  return response;\n}, error => {\n  // Handle different types of errors\n  if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {\n    // Don't redirect on timeout errors\n    console.log('⏰ Request timeout detected');\n    return Promise.reject(error);\n  }\n\n  // Handle 401 Unauthorized errors\n  if (error.response && error.response.status === 401) {\n    var _error$response$data, _error$config, _error$config$url, _error$config2, _error$config2$url, _error$response$data2;\n    console.log('🔒 Authentication error detected:', (_error$response$data = error.response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message);\n\n    // Check if this is an AI-related request\n    const isAIRequest = ((_error$config = error.config) === null || _error$config === void 0 ? void 0 : (_error$config$url = _error$config.url) === null || _error$config$url === void 0 ? void 0 : _error$config$url.includes('/ai-questions')) || ((_error$config2 = error.config) === null || _error$config2 === void 0 ? void 0 : (_error$config2$url = _error$config2.url) === null || _error$config2$url === void 0 ? void 0 : _error$config2$url.includes('/auth/'));\n    if (isAIRequest) {\n      // For AI requests, don't auto-redirect - let the component handle it\n      console.log('🤖 AI authentication error - letting component handle');\n      return Promise.reject(error);\n    }\n\n    // Only redirect for non-AI requests if it's a real authentication error\n    const errorMessage = ((_error$response$data2 = error.response.data) === null || _error$response$data2 === void 0 ? void 0 : _error$response$data2.message) || '';\n    const isRealAuthError = errorMessage.includes('expired') || errorMessage.includes('Invalid token') || errorMessage.includes('not authenticated');\n    if (isRealAuthError) {\n      // Clear the token\n      localStorage.removeItem('token');\n      localStorage.removeItem('user');\n\n      // Show error message\n      message.error('Your session has expired. Please login again.');\n\n      // Redirect to login page only for non-AI requests\n      setTimeout(() => {\n        window.location.href = '/login';\n      }, 1000);\n    }\n    return Promise.reject(error);\n  }\n  return Promise.reject(error);\n});\nexport default axiosInstance;", "map": {"version": 3, "names": ["axios", "message", "axiosInstance", "create", "baseURL", "timeout", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "payload", "JSON", "parse", "atob", "split", "currentTime", "Date", "now", "exp", "console", "log", "removeItem", "window", "location", "href", "Promise", "reject", "Error", "e", "headers", "Authorization", "error", "response", "code", "includes", "status", "_error$response$data", "_error$config", "_error$config$url", "_error$config2", "_error$config2$url", "_error$response$data2", "data", "isAIRequest", "url", "errorMessage", "isRealAuthError", "setTimeout"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/apicalls/index.js"], "sourcesContent": ["import axios from 'axios';\r\nimport { message } from 'antd';\r\n\r\nconst axiosInstance = axios.create({\r\n    // baseURL: 'https://backend-r0tz.onrender.com' ,\r\n    baseURL: 'http://localhost:8002' ,\r\n    timeout: 30000, // 30 seconds timeout for better user experience\r\n});\r\n\r\n// Add a request interceptor to dynamically set the authorization header\r\naxiosInstance.interceptors.request.use(\r\n    (config) => {\r\n        const token = localStorage.getItem('token');\r\n        if (token) {\r\n            // Check if token is still valid before making request\r\n            try {\r\n                const payload = JSON.parse(atob(token.split('.')[1]));\r\n                const currentTime = Date.now() / 1000;\r\n\r\n                if (payload.exp && payload.exp < currentTime) {\r\n                    console.log('🔒 Token expired, clearing storage');\r\n                    localStorage.removeItem('token');\r\n                    localStorage.removeItem('user');\r\n                    window.location.href = '/login';\r\n                    return Promise.reject(new Error('Token expired'));\r\n                }\r\n            } catch (e) {\r\n                console.log('⚠️ Invalid token format, clearing storage');\r\n                localStorage.removeItem('token');\r\n                localStorage.removeItem('user');\r\n            }\r\n\r\n            config.headers.Authorization = `Bearer ${token}`;\r\n        }\r\n        return config;\r\n    },\r\n    (error) => {\r\n        return Promise.reject(error);\r\n    }\r\n);\r\n\r\n// Add a response interceptor to handle authentication errors\r\naxiosInstance.interceptors.response.use(\r\n    (response) => {\r\n        return response;\r\n    },\r\n    (error) => {\r\n        // Handle different types of errors\r\n        if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {\r\n            // Don't redirect on timeout errors\r\n            console.log('⏰ Request timeout detected');\r\n            return Promise.reject(error);\r\n        }\r\n\r\n        // Handle 401 Unauthorized errors\r\n        if (error.response && error.response.status === 401) {\r\n            console.log('🔒 Authentication error detected:', error.response.data?.message);\r\n\r\n            // Check if this is an AI-related request\r\n            const isAIRequest = error.config?.url?.includes('/ai-questions') ||\r\n                               error.config?.url?.includes('/auth/');\r\n\r\n            if (isAIRequest) {\r\n                // For AI requests, don't auto-redirect - let the component handle it\r\n                console.log('🤖 AI authentication error - letting component handle');\r\n                return Promise.reject(error);\r\n            }\r\n\r\n            // Only redirect for non-AI requests if it's a real authentication error\r\n            const errorMessage = error.response.data?.message || '';\r\n            const isRealAuthError = errorMessage.includes('expired') ||\r\n                                   errorMessage.includes('Invalid token') ||\r\n                                   errorMessage.includes('not authenticated');\r\n\r\n            if (isRealAuthError) {\r\n                // Clear the token\r\n                localStorage.removeItem('token');\r\n                localStorage.removeItem('user');\r\n\r\n                // Show error message\r\n                message.error('Your session has expired. Please login again.');\r\n\r\n                // Redirect to login page only for non-AI requests\r\n                setTimeout(() => {\r\n                    window.location.href = '/login';\r\n                }, 1000);\r\n            }\r\n\r\n            return Promise.reject(error);\r\n        }\r\n\r\n        return Promise.reject(error);\r\n    }\r\n);\r\n\r\nexport default axiosInstance;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,MAAM;AAE9B,MAAMC,aAAa,GAAGF,KAAK,CAACG,MAAM,CAAC;EAC/B;EACAC,OAAO,EAAE,uBAAuB;EAChCC,OAAO,EAAE,KAAK,CAAE;AACpB,CAAC,CAAC;;AAEF;AACAH,aAAa,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CACjCC,MAAM,IAAK;EACR,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACP;IACA,IAAI;MACA,MAAMG,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACN,KAAK,CAACO,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrD,MAAMC,WAAW,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI;MAErC,IAAIP,OAAO,CAACQ,GAAG,IAAIR,OAAO,CAACQ,GAAG,GAAGH,WAAW,EAAE;QAC1CI,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;QACjDZ,YAAY,CAACa,UAAU,CAAC,OAAO,CAAC;QAChCb,YAAY,CAACa,UAAU,CAAC,MAAM,CAAC;QAC/BC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;QAC/B,OAAOC,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAAC,eAAe,CAAC,CAAC;MACrD;IACJ,CAAC,CAAC,OAAOC,CAAC,EAAE;MACRT,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;MACxDZ,YAAY,CAACa,UAAU,CAAC,OAAO,CAAC;MAChCb,YAAY,CAACa,UAAU,CAAC,MAAM,CAAC;IACnC;IAEAf,MAAM,CAACuB,OAAO,CAACC,aAAa,GAAI,UAASvB,KAAM,EAAC;EACpD;EACA,OAAOD,MAAM;AACjB,CAAC,EACAyB,KAAK,IAAK;EACP,OAAON,OAAO,CAACC,MAAM,CAACK,KAAK,CAAC;AAChC,CACJ,CAAC;;AAED;AACAhC,aAAa,CAACI,YAAY,CAAC6B,QAAQ,CAAC3B,GAAG,CAClC2B,QAAQ,IAAK;EACV,OAAOA,QAAQ;AACnB,CAAC,EACAD,KAAK,IAAK;EACP;EACA,IAAIA,KAAK,CAACE,IAAI,KAAK,cAAc,IAAIF,KAAK,CAACjC,OAAO,CAACoC,QAAQ,CAAC,SAAS,CAAC,EAAE;IACpE;IACAf,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;IACzC,OAAOK,OAAO,CAACC,MAAM,CAACK,KAAK,CAAC;EAChC;;EAEA;EACA,IAAIA,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAACG,MAAM,KAAK,GAAG,EAAE;IAAA,IAAAC,oBAAA,EAAAC,aAAA,EAAAC,iBAAA,EAAAC,cAAA,EAAAC,kBAAA,EAAAC,qBAAA;IACjDtB,OAAO,CAACC,GAAG,CAAC,mCAAmC,GAAAgB,oBAAA,GAAEL,KAAK,CAACC,QAAQ,CAACU,IAAI,cAAAN,oBAAA,uBAAnBA,oBAAA,CAAqBtC,OAAO,CAAC;;IAE9E;IACA,MAAM6C,WAAW,GAAG,EAAAN,aAAA,GAAAN,KAAK,CAACzB,MAAM,cAAA+B,aAAA,wBAAAC,iBAAA,GAAZD,aAAA,CAAcO,GAAG,cAAAN,iBAAA,uBAAjBA,iBAAA,CAAmBJ,QAAQ,CAAC,eAAe,CAAC,OAAAK,cAAA,GAC7CR,KAAK,CAACzB,MAAM,cAAAiC,cAAA,wBAAAC,kBAAA,GAAZD,cAAA,CAAcK,GAAG,cAAAJ,kBAAA,uBAAjBA,kBAAA,CAAmBN,QAAQ,CAAC,QAAQ,CAAC;IAExD,IAAIS,WAAW,EAAE;MACb;MACAxB,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;MACpE,OAAOK,OAAO,CAACC,MAAM,CAACK,KAAK,CAAC;IAChC;;IAEA;IACA,MAAMc,YAAY,GAAG,EAAAJ,qBAAA,GAAAV,KAAK,CAACC,QAAQ,CAACU,IAAI,cAAAD,qBAAA,uBAAnBA,qBAAA,CAAqB3C,OAAO,KAAI,EAAE;IACvD,MAAMgD,eAAe,GAAGD,YAAY,CAACX,QAAQ,CAAC,SAAS,CAAC,IACjCW,YAAY,CAACX,QAAQ,CAAC,eAAe,CAAC,IACtCW,YAAY,CAACX,QAAQ,CAAC,mBAAmB,CAAC;IAEjE,IAAIY,eAAe,EAAE;MACjB;MACAtC,YAAY,CAACa,UAAU,CAAC,OAAO,CAAC;MAChCb,YAAY,CAACa,UAAU,CAAC,MAAM,CAAC;;MAE/B;MACAvB,OAAO,CAACiC,KAAK,CAAC,+CAA+C,CAAC;;MAE9D;MACAgB,UAAU,CAAC,MAAM;QACbzB,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;MACnC,CAAC,EAAE,IAAI,CAAC;IACZ;IAEA,OAAOC,OAAO,CAACC,MAAM,CAACK,KAAK,CAAC;EAChC;EAEA,OAAON,OAAO,CAACC,MAAM,CAACK,KAAK,CAAC;AAChC,CACJ,CAAC;AAED,eAAehC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}