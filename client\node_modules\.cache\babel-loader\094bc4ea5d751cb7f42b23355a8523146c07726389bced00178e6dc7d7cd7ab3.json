{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Quiz\\\\QuizPlay.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport { getExamById } from '../../../apicalls/exams';\nimport { addReport } from '../../../apicalls/reports';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport { chatWithChatGPTToGetAns } from '../../../apicalls/chat';\nimport QuizRenderer from '../../../components/QuizRenderer';\nimport QuizErrorBoundary from '../../../components/QuizErrorBoundary';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuizPlay = () => {\n  _s();\n  const [examData, setExamData] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [selectedQuestionIndex, setSelectedQuestionIndex] = useState(0);\n  const [selectedOptions, setSelectedOptions] = useState({});\n  const [secondsLeft, setSecondsLeft] = useState(0);\n  const [timeUp, setTimeUp] = useState(false);\n  const [intervalId, setIntervalId] = useState(null);\n  const [startTime, setStartTime] = useState(null);\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const getExamData = async (retryCount = 0) => {\n    try {\n      dispatch(ShowLoading());\n      const response = await getExamById({\n        examId: id\n      });\n      dispatch(HideLoading());\n      if (response.success) {\n        var _response$data, _response$data2;\n        setQuestions(((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.questions) || []);\n        setExamData(response.data);\n        setSecondsLeft((((_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.duration) || 0) * 60);\n      } else {\n        message.error(response.message);\n        navigate('/user/quiz');\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n\n      // Retry logic for network errors\n      if (retryCount < 2 && (error.code === 'ECONNABORTED' || !error.response)) {\n        console.log(`Retrying quiz data fetch... Attempt ${retryCount + 1}`);\n        setTimeout(() => getExamData(retryCount + 1), 1000);\n        return;\n      }\n      message.error(error.message || 'Failed to load quiz. Please try again.');\n      navigate('/user/quiz');\n    }\n  };\n  const checkFreeTextAnswers = async payload => {\n    if (!payload.length) return [];\n    const {\n      data\n    } = await chatWithChatGPTToGetAns(payload);\n    return data;\n  };\n  const calculateResult = useCallback(async () => {\n    try {\n      if (!user || !user._id) {\n        message.error(\"User not found. Please log in again.\");\n        navigate(\"/login\");\n        return;\n      }\n      dispatch(ShowLoading());\n      const freeTextPayload = [];\n      questions.forEach((q, idx) => {\n        if (q.type === \"fill\" || q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\n          freeTextPayload.push({\n            question: q.name,\n            expectedAnswer: q.correctAnswer || q.correctOption,\n            userAnswer: selectedOptions[idx] || \"\"\n          });\n        }\n      });\n      const gptResults = await checkFreeTextAnswers(freeTextPayload);\n      const gptMap = {};\n      gptResults.forEach(r => {\n        if (r.result && typeof r.result.isCorrect === \"boolean\") {\n          gptMap[r.question] = r.result;\n        } else if (typeof r.isCorrect === \"boolean\") {\n          gptMap[r.question] = {\n            isCorrect: r.isCorrect,\n            reason: r.reason || \"\"\n          };\n        }\n      });\n      const correctAnswers = [];\n      const wrongAnswers = [];\n      questions.forEach((q, idx) => {\n        const userAnswerKey = selectedOptions[idx] || \"\";\n        if (q.type === \"fill\" || q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\n          const {\n            isCorrect = false,\n            reason = \"\"\n          } = gptMap[q.name] || {};\n          const enriched = {\n            ...q,\n            userAnswer: userAnswerKey,\n            reason\n          };\n          if (isCorrect) {\n            correctAnswers.push(enriched);\n          } else {\n            wrongAnswers.push(enriched);\n          }\n        } else if (q.type === \"mcq\" || q.answerType === \"Options\") {\n          const correctKey = q.correctOption || q.correctAnswer;\n          const isCorrect = correctKey === userAnswerKey;\n          const enriched = {\n            ...q,\n            userAnswer: userAnswerKey\n          };\n          if (isCorrect) {\n            correctAnswers.push(enriched);\n          } else {\n            wrongAnswers.push(enriched);\n          }\n        }\n      });\n      const timeSpent = startTime ? Math.floor((Date.now() - startTime) / 1000) : 0;\n      const totalTimeAllowed = ((examData === null || examData === void 0 ? void 0 : examData.duration) || 0) * 60;\n      const totalQuestions = questions.length;\n      const correctCount = correctAnswers.length;\n      const scorePercentage = Math.round(correctCount / totalQuestions * 100);\n      const points = correctCount * 10;\n\n      // Handle both passingMarks and passingPercentage for backward compatibility\n      const passingPercentage = examData.passingPercentage || examData.passingMarks || 70;\n      const verdict = scorePercentage >= passingPercentage ? \"Pass\" : \"Fail\";\n      const tempResult = {\n        correctAnswers,\n        wrongAnswers,\n        verdict,\n        score: scorePercentage,\n        points: points,\n        totalQuestions: totalQuestions,\n        timeSpent: timeSpent,\n        totalTimeAllowed: totalTimeAllowed\n      };\n      const response = await addReport({\n        exam: id,\n        result: tempResult,\n        user: user._id\n      });\n      if (response.success) {\n        var _response$xpData, _response$xpData2, _response$xpData3, _response$xpData4;\n        // Clear ranking cache for real-time updates\n        localStorage.removeItem('rankingCache');\n        localStorage.removeItem('userRankingPosition');\n        localStorage.removeItem('leaderboardData');\n\n        // Trigger ranking update event for real-time updates\n        window.dispatchEvent(new CustomEvent('rankingUpdate', {\n          detail: {\n            userId: user._id,\n            xpGained: ((_response$xpData = response.xpData) === null || _response$xpData === void 0 ? void 0 : _response$xpData.xpAwarded) || 0,\n            newTotalXP: ((_response$xpData2 = response.xpData) === null || _response$xpData2 === void 0 ? void 0 : _response$xpData2.newTotalXP) || 0,\n            levelUp: ((_response$xpData3 = response.xpData) === null || _response$xpData3 === void 0 ? void 0 : _response$xpData3.levelUp) || false,\n            newLevel: ((_response$xpData4 = response.xpData) === null || _response$xpData4 === void 0 ? void 0 : _response$xpData4.newLevel) || user.currentLevel\n          }\n        }));\n\n        // Debug XP data\n        console.log('🔍 Quiz completion response:', response);\n        console.log('💰 XP Data received:', response.xpData);\n        const resultWithXP = {\n          ...tempResult,\n          xpData: response.xpData\n        };\n        console.log('📊 Final result with XP:', resultWithXP);\n        navigate(`/quiz/${id}/result`, {\n          state: {\n            result: resultWithXP\n          }\n        });\n      } else {\n        message.error(response.message);\n        console.error('❌ Quiz submission failed:', response.message);\n      }\n      dispatch(HideLoading());\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  }, [questions, selectedOptions, examData, id, user, navigate, dispatch]);\n  const startTimer = useCallback(() => {\n    const totalSeconds = ((examData === null || examData === void 0 ? void 0 : examData.duration) || 0) * 60;\n    setSecondsLeft(totalSeconds);\n    setStartTime(Date.now());\n    const newIntervalId = setInterval(() => {\n      setSecondsLeft(prevSeconds => {\n        if (prevSeconds > 0) {\n          return prevSeconds - 1;\n        } else {\n          setTimeUp(true);\n          return 0;\n        }\n      });\n    }, 1000);\n    setIntervalId(newIntervalId);\n  }, [examData]);\n  useEffect(() => {\n    if (timeUp && intervalId) {\n      clearInterval(intervalId);\n      setIntervalId(null);\n      calculateResult();\n    }\n  }, [timeUp, intervalId, calculateResult]);\n  useEffect(() => {\n    if (id) {\n      getExamData();\n    }\n  }, []);\n  useEffect(() => {\n    document.body.classList.add('quiz-fullscreen');\n    return () => {\n      document.body.classList.remove('quiz-fullscreen');\n    };\n  }, []);\n  useEffect(() => {\n    if (examData && questions.length > 0) {\n      startTimer();\n    }\n  }, [examData, questions]);\n  useEffect(() => {\n    return () => {\n      if (intervalId) {\n        clearInterval(intervalId);\n        setIntervalId(null);\n      }\n    };\n  }, [intervalId]);\n  if (!examData || questions.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-4 text-gray-600\",\n          children: \"Loading quiz questions...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 246,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(QuizErrorBoundary, {\n    children: /*#__PURE__*/_jsxDEV(QuizRenderer, {\n      question: questions[selectedQuestionIndex],\n      questionIndex: selectedQuestionIndex,\n      totalQuestions: questions.length,\n      selectedAnswer: selectedOptions[selectedQuestionIndex],\n      onAnswerChange: answer => setSelectedOptions({\n        ...selectedOptions,\n        [selectedQuestionIndex]: answer\n      }),\n      timeLeft: secondsLeft,\n      examTitle: (examData === null || examData === void 0 ? void 0 : examData.name) || \"Quiz\",\n      isTimeWarning: secondsLeft <= 60,\n      onNext: () => {\n        if (selectedQuestionIndex === questions.length - 1) {\n          calculateResult();\n        } else {\n          setSelectedQuestionIndex(selectedQuestionIndex + 1);\n        }\n      },\n      onPrevious: () => setSelectedQuestionIndex(selectedQuestionIndex - 1)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 257,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 256,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizPlay, \"GRZFMt3c7DCTioJr9el14mYOwwo=\", false, function () {\n  return [useParams, useNavigate, useDispatch, useSelector];\n});\n_c = QuizPlay;\nexport default QuizPlay;\nvar _c;\n$RefreshReg$(_c, \"QuizPlay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useParams", "useNavigate", "useDispatch", "useSelector", "message", "getExamById", "addReport", "HideLoading", "ShowLoading", "chatWithChatGPTToGetAns", "Quiz<PERSON><PERSON><PERSON>", "QuizError<PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "QuizPlay", "_s", "examData", "setExamData", "questions", "setQuestions", "selectedQuestionIndex", "setSelectedQuestionIndex", "selectedOptions", "setSelectedOptions", "secondsLeft", "setSecondsLeft", "timeUp", "setTimeUp", "intervalId", "setIntervalId", "startTime", "setStartTime", "id", "navigate", "dispatch", "user", "state", "getExamData", "retryCount", "response", "examId", "success", "_response$data", "_response$data2", "data", "duration", "error", "code", "console", "log", "setTimeout", "checkFreeTextAnswers", "payload", "length", "calculateResult", "_id", "freeTextPayload", "for<PERSON>ach", "q", "idx", "type", "answerType", "push", "question", "name", "expectedAnswer", "<PERSON><PERSON><PERSON><PERSON>", "correctOption", "userAnswer", "gptResults", "gptMap", "r", "result", "isCorrect", "reason", "correctAnswers", "wrongAnswers", "userAnswerKey", "enriched", "<PERSON><PERSON><PERSON>", "timeSpent", "Math", "floor", "Date", "now", "totalTimeAllowed", "totalQuestions", "correctCount", "scorePercentage", "round", "points", "passingPercentage", "passingMarks", "verdict", "tempResult", "score", "exam", "_response$xpData", "_response$xpData2", "_response$xpData3", "_response$xpData4", "localStorage", "removeItem", "window", "dispatchEvent", "CustomEvent", "detail", "userId", "xpGained", "xpData", "xpAwarded", "newTotalXP", "levelUp", "newLevel", "currentLevel", "resultWithXP", "startTimer", "totalSeconds", "newIntervalId", "setInterval", "prevSeconds", "clearInterval", "document", "body", "classList", "add", "remove", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "questionIndex", "<PERSON><PERSON><PERSON><PERSON>", "onAnswerChange", "answer", "timeLeft", "examTitle", "isTimeWarning", "onNext", "onPrevious", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/QuizPlay.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport { getExamById } from '../../../apicalls/exams';\nimport { addReport } from '../../../apicalls/reports';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport { chatWithChatGPTToGetAns } from '../../../apicalls/chat';\nimport QuizRenderer from '../../../components/QuizRenderer';\nimport QuizErrorBoundary from '../../../components/QuizErrorBoundary';\n\nconst QuizPlay = () => {\n  const [examData, setExamData] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [selectedQuestionIndex, setSelectedQuestionIndex] = useState(0);\n  const [selectedOptions, setSelectedOptions] = useState({});\n  const [secondsLeft, setSecondsLeft] = useState(0);\n  const [timeUp, setTimeUp] = useState(false);\n  const [intervalId, setIntervalId] = useState(null);\n  const [startTime, setStartTime] = useState(null);\n\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const { user } = useSelector((state) => state.user);\n\n  const getExamData = async (retryCount = 0) => {\n    try {\n      dispatch(ShowLoading());\n      const response = await getExamById({ examId: id });\n      dispatch(HideLoading());\n\n      if (response.success) {\n        setQuestions(response.data?.questions || []);\n        setExamData(response.data);\n        setSecondsLeft((response.data?.duration || 0) * 60);\n      } else {\n        message.error(response.message);\n        navigate('/user/quiz');\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n\n      // Retry logic for network errors\n      if (retryCount < 2 && (error.code === 'ECONNABORTED' || !error.response)) {\n        console.log(`Retrying quiz data fetch... Attempt ${retryCount + 1}`);\n        setTimeout(() => getExamData(retryCount + 1), 1000);\n        return;\n      }\n\n      message.error(error.message || 'Failed to load quiz. Please try again.');\n      navigate('/user/quiz');\n    }\n  };\n\n  const checkFreeTextAnswers = async (payload) => {\n    if (!payload.length) return [];\n    const { data } = await chatWithChatGPTToGetAns(payload);\n    return data;\n  };\n\n  const calculateResult = useCallback(async () => {\n    try {\n      if (!user || !user._id) {\n        message.error(\"User not found. Please log in again.\");\n        navigate(\"/login\");\n        return;\n      }\n\n      dispatch(ShowLoading());\n\n      const freeTextPayload = [];\n      questions.forEach((q, idx) => {\n        if (q.type === \"fill\" || q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\n          freeTextPayload.push({\n            question: q.name,\n            expectedAnswer: q.correctAnswer || q.correctOption,\n            userAnswer: selectedOptions[idx] || \"\",\n          });\n        }\n      });\n\n      const gptResults = await checkFreeTextAnswers(freeTextPayload);\n      const gptMap = {};\n\n      gptResults.forEach((r) => {\n        if (r.result && typeof r.result.isCorrect === \"boolean\") {\n          gptMap[r.question] = r.result;\n        } else if (typeof r.isCorrect === \"boolean\") {\n          gptMap[r.question] = { isCorrect: r.isCorrect, reason: r.reason || \"\" };\n        }\n      });\n\n      const correctAnswers = [];\n      const wrongAnswers = [];\n\n      questions.forEach((q, idx) => {\n        const userAnswerKey = selectedOptions[idx] || \"\";\n\n        if (q.type === \"fill\" || q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\n          const { isCorrect = false, reason = \"\" } = gptMap[q.name] || {};\n          const enriched = { ...q, userAnswer: userAnswerKey, reason };\n\n          if (isCorrect) {\n            correctAnswers.push(enriched);\n          } else {\n            wrongAnswers.push(enriched);\n          }\n        } else if (q.type === \"mcq\" || q.answerType === \"Options\") {\n          const correctKey = q.correctOption || q.correctAnswer;\n          const isCorrect = correctKey === userAnswerKey;\n          const enriched = { ...q, userAnswer: userAnswerKey };\n\n          if (isCorrect) {\n            correctAnswers.push(enriched);\n          } else {\n            wrongAnswers.push(enriched);\n          }\n        }\n      });\n\n      const timeSpent = startTime ? Math.floor((Date.now() - startTime) / 1000) : 0;\n      const totalTimeAllowed = (examData?.duration || 0) * 60;\n      const totalQuestions = questions.length;\n      const correctCount = correctAnswers.length;\n      const scorePercentage = Math.round((correctCount / totalQuestions) * 100);\n      const points = correctCount * 10;\n\n      // Handle both passingMarks and passingPercentage for backward compatibility\n      const passingPercentage = examData.passingPercentage || examData.passingMarks || 70;\n      const verdict = scorePercentage >= passingPercentage ? \"Pass\" : \"Fail\";\n\n      const tempResult = {\n        correctAnswers,\n        wrongAnswers,\n        verdict,\n        score: scorePercentage,\n        points: points,\n        totalQuestions: totalQuestions,\n        timeSpent: timeSpent,\n        totalTimeAllowed: totalTimeAllowed\n      };\n\n      const response = await addReport({\n        exam: id,\n        result: tempResult,\n        user: user._id,\n      });\n\n      if (response.success) {\n        // Clear ranking cache for real-time updates\n        localStorage.removeItem('rankingCache');\n        localStorage.removeItem('userRankingPosition');\n        localStorage.removeItem('leaderboardData');\n\n        // Trigger ranking update event for real-time updates\n        window.dispatchEvent(new CustomEvent('rankingUpdate', {\n          detail: {\n            userId: user._id,\n            xpGained: response.xpData?.xpAwarded || 0,\n            newTotalXP: response.xpData?.newTotalXP || 0,\n            levelUp: response.xpData?.levelUp || false,\n            newLevel: response.xpData?.newLevel || user.currentLevel\n          }\n        }));\n\n        // Debug XP data\n        console.log('🔍 Quiz completion response:', response);\n        console.log('💰 XP Data received:', response.xpData);\n\n        const resultWithXP = {\n          ...tempResult,\n          xpData: response.xpData\n        };\n\n        console.log('📊 Final result with XP:', resultWithXP);\n        navigate(`/quiz/${id}/result`, { state: { result: resultWithXP } });\n      } else {\n        message.error(response.message);\n        console.error('❌ Quiz submission failed:', response.message);\n      }\n      dispatch(HideLoading());\n\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  }, [questions, selectedOptions, examData, id, user, navigate, dispatch]);\n\n  const startTimer = useCallback(() => {\n    const totalSeconds = (examData?.duration || 0) * 60;\n    setSecondsLeft(totalSeconds);\n    setStartTime(Date.now());\n\n    const newIntervalId = setInterval(() => {\n      setSecondsLeft((prevSeconds) => {\n        if (prevSeconds > 0) {\n          return prevSeconds - 1;\n        } else {\n          setTimeUp(true);\n          return 0;\n        }\n      });\n    }, 1000);\n    setIntervalId(newIntervalId);\n  }, [examData]);\n\n  useEffect(() => {\n    if (timeUp && intervalId) {\n      clearInterval(intervalId);\n      setIntervalId(null);\n      calculateResult();\n    }\n  }, [timeUp, intervalId, calculateResult]);\n\n  useEffect(() => {\n    if (id) {\n      getExamData();\n    }\n  }, []);\n\n  useEffect(() => {\n    document.body.classList.add('quiz-fullscreen');\n    return () => {\n      document.body.classList.remove('quiz-fullscreen');\n    };\n  }, []);\n\n  useEffect(() => {\n    if (examData && questions.length > 0) {\n      startTimer();\n    }\n  }, [examData, questions]);\n\n  useEffect(() => {\n    return () => {\n      if (intervalId) {\n        clearInterval(intervalId);\n        setIntervalId(null);\n      }\n    };\n  }, [intervalId]);\n\n  if (!examData || questions.length === 0) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">Loading quiz questions...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <QuizErrorBoundary>\n      <QuizRenderer\n        question={questions[selectedQuestionIndex]}\n        questionIndex={selectedQuestionIndex}\n        totalQuestions={questions.length}\n        selectedAnswer={selectedOptions[selectedQuestionIndex]}\n        onAnswerChange={(answer) =>\n          setSelectedOptions({\n            ...selectedOptions,\n            [selectedQuestionIndex]: answer,\n          })\n        }\n        timeLeft={secondsLeft}\n        examTitle={examData?.name || \"Quiz\"}\n        isTimeWarning={secondsLeft <= 60}\n        onNext={() => {\n          if (selectedQuestionIndex === questions.length - 1) {\n            calculateResult();\n          } else {\n            setSelectedQuestionIndex(selectedQuestionIndex + 1);\n          }\n        }}\n        onPrevious={() => setSelectedQuestionIndex(selectedQuestionIndex - 1)}\n      />\n    </QuizErrorBoundary>\n  );\n};\n\nexport default QuizPlay;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,SAAS,QAAQ,2BAA2B;AACrD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,uBAAuB,QAAQ,wBAAwB;AAChE,OAAOC,YAAY,MAAM,kCAAkC;AAC3D,OAAOC,iBAAiB,MAAM,uCAAuC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtE,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACqB,SAAS,EAAEC,YAAY,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACuB,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGxB,QAAQ,CAAC,CAAC,CAAC;EACrE,MAAM,CAACyB,eAAe,EAAEC,kBAAkB,CAAC,GAAG1B,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1D,MAAM,CAAC2B,WAAW,EAAEC,cAAc,CAAC,GAAG5B,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC6B,MAAM,EAAEC,SAAS,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAAC+B,UAAU,EAAEC,aAAa,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACiC,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EAEhD,MAAM;IAAEmC;EAAG,CAAC,GAAGhC,SAAS,CAAC,CAAC;EAC1B,MAAMiC,QAAQ,GAAGhC,WAAW,CAAC,CAAC;EAC9B,MAAMiC,QAAQ,GAAGhC,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEiC;EAAK,CAAC,GAAGhC,WAAW,CAAEiC,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EAEnD,MAAME,WAAW,GAAG,MAAAA,CAAOC,UAAU,GAAG,CAAC,KAAK;IAC5C,IAAI;MACFJ,QAAQ,CAAC1B,WAAW,CAAC,CAAC,CAAC;MACvB,MAAM+B,QAAQ,GAAG,MAAMlC,WAAW,CAAC;QAAEmC,MAAM,EAAER;MAAG,CAAC,CAAC;MAClDE,QAAQ,CAAC3B,WAAW,CAAC,CAAC,CAAC;MAEvB,IAAIgC,QAAQ,CAACE,OAAO,EAAE;QAAA,IAAAC,cAAA,EAAAC,eAAA;QACpBxB,YAAY,CAAC,EAAAuB,cAAA,GAAAH,QAAQ,CAACK,IAAI,cAAAF,cAAA,uBAAbA,cAAA,CAAexB,SAAS,KAAI,EAAE,CAAC;QAC5CD,WAAW,CAACsB,QAAQ,CAACK,IAAI,CAAC;QAC1BnB,cAAc,CAAC,CAAC,EAAAkB,eAAA,GAAAJ,QAAQ,CAACK,IAAI,cAAAD,eAAA,uBAAbA,eAAA,CAAeE,QAAQ,KAAI,CAAC,IAAI,EAAE,CAAC;MACrD,CAAC,MAAM;QACLzC,OAAO,CAAC0C,KAAK,CAACP,QAAQ,CAACnC,OAAO,CAAC;QAC/B6B,QAAQ,CAAC,YAAY,CAAC;MACxB;IACF,CAAC,CAAC,OAAOa,KAAK,EAAE;MACdZ,QAAQ,CAAC3B,WAAW,CAAC,CAAC,CAAC;;MAEvB;MACA,IAAI+B,UAAU,GAAG,CAAC,KAAKQ,KAAK,CAACC,IAAI,KAAK,cAAc,IAAI,CAACD,KAAK,CAACP,QAAQ,CAAC,EAAE;QACxES,OAAO,CAACC,GAAG,CAAE,uCAAsCX,UAAU,GAAG,CAAE,EAAC,CAAC;QACpEY,UAAU,CAAC,MAAMb,WAAW,CAACC,UAAU,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;QACnD;MACF;MAEAlC,OAAO,CAAC0C,KAAK,CAACA,KAAK,CAAC1C,OAAO,IAAI,wCAAwC,CAAC;MACxE6B,QAAQ,CAAC,YAAY,CAAC;IACxB;EACF,CAAC;EAED,MAAMkB,oBAAoB,GAAG,MAAOC,OAAO,IAAK;IAC9C,IAAI,CAACA,OAAO,CAACC,MAAM,EAAE,OAAO,EAAE;IAC9B,MAAM;MAAET;IAAK,CAAC,GAAG,MAAMnC,uBAAuB,CAAC2C,OAAO,CAAC;IACvD,OAAOR,IAAI;EACb,CAAC;EAED,MAAMU,eAAe,GAAGvD,WAAW,CAAC,YAAY;IAC9C,IAAI;MACF,IAAI,CAACoC,IAAI,IAAI,CAACA,IAAI,CAACoB,GAAG,EAAE;QACtBnD,OAAO,CAAC0C,KAAK,CAAC,sCAAsC,CAAC;QACrDb,QAAQ,CAAC,QAAQ,CAAC;QAClB;MACF;MAEAC,QAAQ,CAAC1B,WAAW,CAAC,CAAC,CAAC;MAEvB,MAAMgD,eAAe,GAAG,EAAE;MAC1BtC,SAAS,CAACuC,OAAO,CAAC,CAACC,CAAC,EAAEC,GAAG,KAAK;QAC5B,IAAID,CAAC,CAACE,IAAI,KAAK,MAAM,IAAIF,CAAC,CAACG,UAAU,KAAK,WAAW,IAAIH,CAAC,CAACG,UAAU,KAAK,mBAAmB,EAAE;UAC7FL,eAAe,CAACM,IAAI,CAAC;YACnBC,QAAQ,EAAEL,CAAC,CAACM,IAAI;YAChBC,cAAc,EAAEP,CAAC,CAACQ,aAAa,IAAIR,CAAC,CAACS,aAAa;YAClDC,UAAU,EAAE9C,eAAe,CAACqC,GAAG,CAAC,IAAI;UACtC,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;MAEF,MAAMU,UAAU,GAAG,MAAMlB,oBAAoB,CAACK,eAAe,CAAC;MAC9D,MAAMc,MAAM,GAAG,CAAC,CAAC;MAEjBD,UAAU,CAACZ,OAAO,CAAEc,CAAC,IAAK;QACxB,IAAIA,CAAC,CAACC,MAAM,IAAI,OAAOD,CAAC,CAACC,MAAM,CAACC,SAAS,KAAK,SAAS,EAAE;UACvDH,MAAM,CAACC,CAAC,CAACR,QAAQ,CAAC,GAAGQ,CAAC,CAACC,MAAM;QAC/B,CAAC,MAAM,IAAI,OAAOD,CAAC,CAACE,SAAS,KAAK,SAAS,EAAE;UAC3CH,MAAM,CAACC,CAAC,CAACR,QAAQ,CAAC,GAAG;YAAEU,SAAS,EAAEF,CAAC,CAACE,SAAS;YAAEC,MAAM,EAAEH,CAAC,CAACG,MAAM,IAAI;UAAG,CAAC;QACzE;MACF,CAAC,CAAC;MAEF,MAAMC,cAAc,GAAG,EAAE;MACzB,MAAMC,YAAY,GAAG,EAAE;MAEvB1D,SAAS,CAACuC,OAAO,CAAC,CAACC,CAAC,EAAEC,GAAG,KAAK;QAC5B,MAAMkB,aAAa,GAAGvD,eAAe,CAACqC,GAAG,CAAC,IAAI,EAAE;QAEhD,IAAID,CAAC,CAACE,IAAI,KAAK,MAAM,IAAIF,CAAC,CAACG,UAAU,KAAK,WAAW,IAAIH,CAAC,CAACG,UAAU,KAAK,mBAAmB,EAAE;UAC7F,MAAM;YAAEY,SAAS,GAAG,KAAK;YAAEC,MAAM,GAAG;UAAG,CAAC,GAAGJ,MAAM,CAACZ,CAAC,CAACM,IAAI,CAAC,IAAI,CAAC,CAAC;UAC/D,MAAMc,QAAQ,GAAG;YAAE,GAAGpB,CAAC;YAAEU,UAAU,EAAES,aAAa;YAAEH;UAAO,CAAC;UAE5D,IAAID,SAAS,EAAE;YACbE,cAAc,CAACb,IAAI,CAACgB,QAAQ,CAAC;UAC/B,CAAC,MAAM;YACLF,YAAY,CAACd,IAAI,CAACgB,QAAQ,CAAC;UAC7B;QACF,CAAC,MAAM,IAAIpB,CAAC,CAACE,IAAI,KAAK,KAAK,IAAIF,CAAC,CAACG,UAAU,KAAK,SAAS,EAAE;UACzD,MAAMkB,UAAU,GAAGrB,CAAC,CAACS,aAAa,IAAIT,CAAC,CAACQ,aAAa;UACrD,MAAMO,SAAS,GAAGM,UAAU,KAAKF,aAAa;UAC9C,MAAMC,QAAQ,GAAG;YAAE,GAAGpB,CAAC;YAAEU,UAAU,EAAES;UAAc,CAAC;UAEpD,IAAIJ,SAAS,EAAE;YACbE,cAAc,CAACb,IAAI,CAACgB,QAAQ,CAAC;UAC/B,CAAC,MAAM;YACLF,YAAY,CAACd,IAAI,CAACgB,QAAQ,CAAC;UAC7B;QACF;MACF,CAAC,CAAC;MAEF,MAAME,SAAS,GAAGlD,SAAS,GAAGmD,IAAI,CAACC,KAAK,CAAC,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGtD,SAAS,IAAI,IAAI,CAAC,GAAG,CAAC;MAC7E,MAAMuD,gBAAgB,GAAG,CAAC,CAAArE,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE6B,QAAQ,KAAI,CAAC,IAAI,EAAE;MACvD,MAAMyC,cAAc,GAAGpE,SAAS,CAACmC,MAAM;MACvC,MAAMkC,YAAY,GAAGZ,cAAc,CAACtB,MAAM;MAC1C,MAAMmC,eAAe,GAAGP,IAAI,CAACQ,KAAK,CAAEF,YAAY,GAAGD,cAAc,GAAI,GAAG,CAAC;MACzE,MAAMI,MAAM,GAAGH,YAAY,GAAG,EAAE;;MAEhC;MACA,MAAMI,iBAAiB,GAAG3E,QAAQ,CAAC2E,iBAAiB,IAAI3E,QAAQ,CAAC4E,YAAY,IAAI,EAAE;MACnF,MAAMC,OAAO,GAAGL,eAAe,IAAIG,iBAAiB,GAAG,MAAM,GAAG,MAAM;MAEtE,MAAMG,UAAU,GAAG;QACjBnB,cAAc;QACdC,YAAY;QACZiB,OAAO;QACPE,KAAK,EAAEP,eAAe;QACtBE,MAAM,EAAEA,MAAM;QACdJ,cAAc,EAAEA,cAAc;QAC9BN,SAAS,EAAEA,SAAS;QACpBK,gBAAgB,EAAEA;MACpB,CAAC;MAED,MAAM9C,QAAQ,GAAG,MAAMjC,SAAS,CAAC;QAC/B0F,IAAI,EAAEhE,EAAE;QACRwC,MAAM,EAAEsB,UAAU;QAClB3D,IAAI,EAAEA,IAAI,CAACoB;MACb,CAAC,CAAC;MAEF,IAAIhB,QAAQ,CAACE,OAAO,EAAE;QAAA,IAAAwD,gBAAA,EAAAC,iBAAA,EAAAC,iBAAA,EAAAC,iBAAA;QACpB;QACAC,YAAY,CAACC,UAAU,CAAC,cAAc,CAAC;QACvCD,YAAY,CAACC,UAAU,CAAC,qBAAqB,CAAC;QAC9CD,YAAY,CAACC,UAAU,CAAC,iBAAiB,CAAC;;QAE1C;QACAC,MAAM,CAACC,aAAa,CAAC,IAAIC,WAAW,CAAC,eAAe,EAAE;UACpDC,MAAM,EAAE;YACNC,MAAM,EAAExE,IAAI,CAACoB,GAAG;YAChBqD,QAAQ,EAAE,EAAAX,gBAAA,GAAA1D,QAAQ,CAACsE,MAAM,cAAAZ,gBAAA,uBAAfA,gBAAA,CAAiBa,SAAS,KAAI,CAAC;YACzCC,UAAU,EAAE,EAAAb,iBAAA,GAAA3D,QAAQ,CAACsE,MAAM,cAAAX,iBAAA,uBAAfA,iBAAA,CAAiBa,UAAU,KAAI,CAAC;YAC5CC,OAAO,EAAE,EAAAb,iBAAA,GAAA5D,QAAQ,CAACsE,MAAM,cAAAV,iBAAA,uBAAfA,iBAAA,CAAiBa,OAAO,KAAI,KAAK;YAC1CC,QAAQ,EAAE,EAAAb,iBAAA,GAAA7D,QAAQ,CAACsE,MAAM,cAAAT,iBAAA,uBAAfA,iBAAA,CAAiBa,QAAQ,KAAI9E,IAAI,CAAC+E;UAC9C;QACF,CAAC,CAAC,CAAC;;QAEH;QACAlE,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEV,QAAQ,CAAC;QACrDS,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEV,QAAQ,CAACsE,MAAM,CAAC;QAEpD,MAAMM,YAAY,GAAG;UACnB,GAAGrB,UAAU;UACbe,MAAM,EAAEtE,QAAQ,CAACsE;QACnB,CAAC;QAED7D,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEkE,YAAY,CAAC;QACrDlF,QAAQ,CAAE,SAAQD,EAAG,SAAQ,EAAE;UAAEI,KAAK,EAAE;YAAEoC,MAAM,EAAE2C;UAAa;QAAE,CAAC,CAAC;MACrE,CAAC,MAAM;QACL/G,OAAO,CAAC0C,KAAK,CAACP,QAAQ,CAACnC,OAAO,CAAC;QAC/B4C,OAAO,CAACF,KAAK,CAAC,2BAA2B,EAAEP,QAAQ,CAACnC,OAAO,CAAC;MAC9D;MACA8B,QAAQ,CAAC3B,WAAW,CAAC,CAAC,CAAC;IAEzB,CAAC,CAAC,OAAOuC,KAAK,EAAE;MACdZ,QAAQ,CAAC3B,WAAW,CAAC,CAAC,CAAC;MACvBH,OAAO,CAAC0C,KAAK,CAACA,KAAK,CAAC1C,OAAO,CAAC;IAC9B;EACF,CAAC,EAAE,CAACc,SAAS,EAAEI,eAAe,EAAEN,QAAQ,EAAEgB,EAAE,EAAEG,IAAI,EAAEF,QAAQ,EAAEC,QAAQ,CAAC,CAAC;EAExE,MAAMkF,UAAU,GAAGrH,WAAW,CAAC,MAAM;IACnC,MAAMsH,YAAY,GAAG,CAAC,CAAArG,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE6B,QAAQ,KAAI,CAAC,IAAI,EAAE;IACnDpB,cAAc,CAAC4F,YAAY,CAAC;IAC5BtF,YAAY,CAACoD,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;IAExB,MAAMkC,aAAa,GAAGC,WAAW,CAAC,MAAM;MACtC9F,cAAc,CAAE+F,WAAW,IAAK;QAC9B,IAAIA,WAAW,GAAG,CAAC,EAAE;UACnB,OAAOA,WAAW,GAAG,CAAC;QACxB,CAAC,MAAM;UACL7F,SAAS,CAAC,IAAI,CAAC;UACf,OAAO,CAAC;QACV;MACF,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;IACRE,aAAa,CAACyF,aAAa,CAAC;EAC9B,CAAC,EAAE,CAACtG,QAAQ,CAAC,CAAC;EAEdlB,SAAS,CAAC,MAAM;IACd,IAAI4B,MAAM,IAAIE,UAAU,EAAE;MACxB6F,aAAa,CAAC7F,UAAU,CAAC;MACzBC,aAAa,CAAC,IAAI,CAAC;MACnByB,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAAC5B,MAAM,EAAEE,UAAU,EAAE0B,eAAe,CAAC,CAAC;EAEzCxD,SAAS,CAAC,MAAM;IACd,IAAIkC,EAAE,EAAE;MACNK,WAAW,CAAC,CAAC;IACf;EACF,CAAC,EAAE,EAAE,CAAC;EAENvC,SAAS,CAAC,MAAM;IACd4H,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAC9C,OAAO,MAAM;MACXH,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,iBAAiB,CAAC;IACnD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAENhI,SAAS,CAAC,MAAM;IACd,IAAIkB,QAAQ,IAAIE,SAAS,CAACmC,MAAM,GAAG,CAAC,EAAE;MACpC+D,UAAU,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAACpG,QAAQ,EAAEE,SAAS,CAAC,CAAC;EAEzBpB,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX,IAAI8B,UAAU,EAAE;QACd6F,aAAa,CAAC7F,UAAU,CAAC;QACzBC,aAAa,CAAC,IAAI,CAAC;MACrB;IACF,CAAC;EACH,CAAC,EAAE,CAACD,UAAU,CAAC,CAAC;EAEhB,IAAI,CAACZ,QAAQ,IAAIE,SAAS,CAACmC,MAAM,KAAK,CAAC,EAAE;IACvC,oBACExC,OAAA;MAAKkH,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGnH,OAAA;QAAKkH,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BnH,OAAA;UAAKkH,SAAS,EAAC;QAAwE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC9FvH,OAAA;UAAGkH,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEvH,OAAA,CAACF,iBAAiB;IAAAqH,QAAA,eAChBnH,OAAA,CAACH,YAAY;MACXqD,QAAQ,EAAE7C,SAAS,CAACE,qBAAqB,CAAE;MAC3CiH,aAAa,EAAEjH,qBAAsB;MACrCkE,cAAc,EAAEpE,SAAS,CAACmC,MAAO;MACjCiF,cAAc,EAAEhH,eAAe,CAACF,qBAAqB,CAAE;MACvDmH,cAAc,EAAGC,MAAM,IACrBjH,kBAAkB,CAAC;QACjB,GAAGD,eAAe;QAClB,CAACF,qBAAqB,GAAGoH;MAC3B,CAAC,CACF;MACDC,QAAQ,EAAEjH,WAAY;MACtBkH,SAAS,EAAE,CAAA1H,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEgD,IAAI,KAAI,MAAO;MACpC2E,aAAa,EAAEnH,WAAW,IAAI,EAAG;MACjCoH,MAAM,EAAEA,CAAA,KAAM;QACZ,IAAIxH,qBAAqB,KAAKF,SAAS,CAACmC,MAAM,GAAG,CAAC,EAAE;UAClDC,eAAe,CAAC,CAAC;QACnB,CAAC,MAAM;UACLjC,wBAAwB,CAACD,qBAAqB,GAAG,CAAC,CAAC;QACrD;MACF,CAAE;MACFyH,UAAU,EAAEA,CAAA,KAAMxH,wBAAwB,CAACD,qBAAqB,GAAG,CAAC;IAAE;MAAA6G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACe,CAAC;AAExB,CAAC;AAACrH,EAAA,CA9QID,QAAQ;EAAA,QAUGd,SAAS,EACPC,WAAW,EACXC,WAAW,EACXC,WAAW;AAAA;AAAA2I,EAAA,GAbxBhI,QAAQ;AAgRd,eAAeA,QAAQ;AAAC,IAAAgI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}