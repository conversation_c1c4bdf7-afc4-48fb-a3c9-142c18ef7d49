{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Chat\\\\index.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport axios from \"axios\";\nimport \"./index.css\"; // Import the custom CSS\nimport { chatWithChatGPT, uploadImg } from \"../../../apicalls/chat\";\nimport ContentRenderer from \"../../../components/ContentRenderer\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction ChatGPTIntegration() {\n  _s();\n  const [messages, setMessages] = useState([]);\n  const [prompt, setPrompt] = useState(\"\");\n  const [imageFile, setImageFile] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [isInitialized, setIsInitialized] = useState(false);\n\n  // Initialize chat with welcome message\n  React.useEffect(() => {\n    // Load cached messages\n    const cachedMessages = localStorage.getItem('chat_messages');\n    if (cachedMessages) {\n      setMessages(JSON.parse(cachedMessages));\n    } else {\n      // Set welcome message\n      setMessages([{\n        role: \"assistant\",\n        content: \"Hello! I'm your AI study assistant. I can help you with homework, explain concepts, solve problems, and answer questions. You can also upload images of your textbook pages or worksheets for help. What would you like to learn about today?\"\n      }]);\n    }\n    setIsInitialized(true);\n  }, []);\n\n  // Save messages to cache\n  React.useEffect(() => {\n    if (isInitialized && messages.length > 0) {\n      localStorage.setItem('chat_messages', JSON.stringify(messages));\n    }\n  }, [messages, isInitialized]);\n  const handleChat = async () => {\n    if (!prompt.trim() && !imageFile) return;\n    setIsLoading(true);\n    try {\n      let imageUrl = null;\n\n      // Step 1: Upload the image to the server (if an image is selected)\n      if (imageFile) {\n        const formData = new FormData();\n        formData.append(\"image\", imageFile);\n        const data = await uploadImg(formData);\n        if (data !== null && data !== void 0 && data.success) {\n          imageUrl = data.url; // Extract the S3 URL\n          console.log(\"Image URL: \", imageUrl);\n        } else {\n          throw new Error(\"Image upload failed\");\n        }\n      }\n\n      // Step 2: Construct the ChatGPT message payload\n      const userMessage = imageUrl ? {\n        role: \"user\",\n        content: [{\n          type: \"text\",\n          text: prompt\n        }, {\n          type: \"image_url\",\n          image_url: {\n            url: imageUrl\n          }\n        }]\n      } : {\n        role: \"user\",\n        content: prompt\n      };\n      const updatedMessages = [...messages, userMessage];\n      setMessages(updatedMessages);\n      setPrompt(\"\");\n\n      // Step 3: Send the payload to ChatGPT\n      const chatPayload = {\n        messages: updatedMessages\n      };\n      const chatRes = await chatWithChatGPT(chatPayload);\n      const apiResponse = chatRes === null || chatRes === void 0 ? void 0 : chatRes.data;\n      console.log(\"API Response: \", apiResponse);\n\n      // Step 4: Append the assistant's response to the conversation\n      setMessages(prev => [...prev, {\n        role: \"assistant\",\n        content: apiResponse\n      }]);\n      setImageFile(null);\n    } catch (error) {\n      console.error(\"Error during chat:\", error);\n      alert(\"An error occurred while processing your request. Please try again.\");\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleKeyPress = event => {\n    if (event.key === \"Enter\") {\n      handleChat(); // Trigger the handleChat function on Enter key\n    }\n  };\n\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"chat-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chat-messages\",\n      children: [messages.map((msg, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `message ${msg.role === \"user\" ? \"user-message\" : \"assistant-message\"}`,\n        children: /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: msg.role === \"assistant\" ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: msg !== null && msg !== void 0 && msg.content ? /*#__PURE__*/_jsxDEV(ContentRenderer, {\n              text: msg.content\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 21\n            }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Unable to get a response from AI\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 21\n            }, this)\n          }, void 0, false) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: typeof msg.content === \"string\" ? msg.content : msg.content.map((item, idx) => item.type === \"text\" ? /*#__PURE__*/_jsxDEV(\"p\", {\n              children: item.text\n            }, idx, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 25\n            }, this) : /*#__PURE__*/_jsxDEV(\"img\", {\n              src: item.image_url.url,\n              alt: \"User content\",\n              style: {\n                height: \"100px\"\n              }\n            }, idx, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 25\n            }, this))\n          }, void 0, false)\n        }, void 0, false)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 11\n      }, this)), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-indicator\",\n        children: \"Loading...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 23\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chat-input-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n        className: \"chat-input\",\n        placeholder: \"Type your message here...\",\n        value: prompt,\n        onChange: e => setPrompt(e.target.value)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"file\",\n        accept: \"image/*\",\n        onChange: e => setImageFile(e.target.files[0]),\n        style: {\n          width: \"200px\",\n          borderRadius: \"5px\",\n          marginRight: \"10px\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        disabled: isLoading,\n        className: \"send-button\",\n        onClick: handleChat,\n        children: \"Send\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 105,\n    columnNumber: 5\n  }, this);\n}\n_s(ChatGPTIntegration, \"kVHGPt3IFlxpzbULQvwPVqjsVLA=\");\n_c = ChatGPTIntegration;\nexport default ChatGPTIntegration;\nvar _c;\n$RefreshReg$(_c, \"ChatGPTIntegration\");", "map": {"version": 3, "names": ["React", "useState", "axios", "chatWithChatGPT", "uploadImg", "Content<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ChatGPTIntegration", "_s", "messages", "setMessages", "prompt", "setPrompt", "imageFile", "setImageFile", "isLoading", "setIsLoading", "isInitialized", "setIsInitialized", "useEffect", "cachedMessages", "localStorage", "getItem", "JSON", "parse", "role", "content", "length", "setItem", "stringify", "handleChat", "trim", "imageUrl", "formData", "FormData", "append", "data", "success", "url", "console", "log", "Error", "userMessage", "type", "text", "image_url", "updatedMessages", "chatPayload", "chatRes", "apiResponse", "prev", "error", "alert", "handleKeyPress", "event", "key", "className", "children", "map", "msg", "index", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "item", "idx", "src", "alt", "style", "height", "placeholder", "value", "onChange", "e", "target", "accept", "files", "width", "borderRadius", "marginRight", "disabled", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Chat/index.jsx"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport axios from \"axios\";\r\nimport \"./index.css\"; // Import the custom CSS\r\nimport { chatWithChatGPT, uploadImg } from \"../../../apicalls/chat\";\r\nimport ContentRenderer from \"../../../components/ContentRenderer\";\r\n\r\nfunction ChatGPTIntegration() {\r\n  const [messages, setMessages] = useState([]);\r\n  const [prompt, setPrompt] = useState(\"\");\r\n  const [imageFile, setImageFile] = useState(null);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [isInitialized, setIsInitialized] = useState(false);\r\n\r\n  // Initialize chat with welcome message\r\n  React.useEffect(() => {\r\n    // Load cached messages\r\n    const cachedMessages = localStorage.getItem('chat_messages');\r\n    if (cachedMessages) {\r\n      setMessages(JSON.parse(cachedMessages));\r\n    } else {\r\n      // Set welcome message\r\n      setMessages([{\r\n        role: \"assistant\",\r\n        content: \"Hello! I'm your AI study assistant. I can help you with homework, explain concepts, solve problems, and answer questions. You can also upload images of your textbook pages or worksheets for help. What would you like to learn about today?\"\r\n      }]);\r\n    }\r\n    setIsInitialized(true);\r\n  }, []);\r\n\r\n  // Save messages to cache\r\n  React.useEffect(() => {\r\n    if (isInitialized && messages.length > 0) {\r\n      localStorage.setItem('chat_messages', JSON.stringify(messages));\r\n    }\r\n  }, [messages, isInitialized]);\r\n\r\n  const handleChat = async () => {\r\n    if (!prompt.trim() && !imageFile) return;\r\n\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      let imageUrl = null;\r\n\r\n      // Step 1: Upload the image to the server (if an image is selected)\r\n      if (imageFile) {\r\n        const formData = new FormData();\r\n        formData.append(\"image\", imageFile);\r\n\r\n        const data = await uploadImg(formData);\r\n\r\n        if (data?.success) {\r\n          imageUrl = data.url; // Extract the S3 URL\r\n          console.log(\"Image URL: \", imageUrl);\r\n        } else {\r\n          throw new Error(\"Image upload failed\");\r\n        }\r\n      }\r\n\r\n      // Step 2: Construct the ChatGPT message payload\r\n      const userMessage = imageUrl\r\n        ? {\r\n          role: \"user\",\r\n          content: [\r\n            { type: \"text\", text: prompt },\r\n            { type: \"image_url\", image_url: { url: imageUrl } },\r\n          ],\r\n        }\r\n        : { role: \"user\", content: prompt };\r\n\r\n      const updatedMessages = [...messages, userMessage];\r\n      setMessages(updatedMessages);\r\n      setPrompt(\"\");\r\n\r\n      // Step 3: Send the payload to ChatGPT\r\n      const chatPayload = { messages: updatedMessages };\r\n\r\n      const chatRes = await chatWithChatGPT(chatPayload);\r\n\r\n      const apiResponse = chatRes?.data;\r\n      console.log(\"API Response: \", apiResponse);\r\n\r\n      // Step 4: Append the assistant's response to the conversation\r\n      setMessages((prev) => [\r\n        ...prev,\r\n        { role: \"assistant\", content: apiResponse },\r\n      ]);\r\n\r\n      setImageFile(null);\r\n    } catch (error) {\r\n      console.error(\"Error during chat:\", error);\r\n      alert(\"An error occurred while processing your request. Please try again.\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleKeyPress = (event) => {\r\n    if (event.key === \"Enter\") {\r\n      handleChat(); // Trigger the handleChat function on Enter key\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"chat-container\">\r\n      {/* Chat messages */}\r\n      <div className=\"chat-messages\">\r\n        {messages.map((msg, index) => (\r\n          <div\r\n            key={index}\r\n            className={`message ${msg.role === \"user\" ? \"user-message\" : \"assistant-message\"\r\n              }`}\r\n          >\r\n            <>\r\n              {msg.role === \"assistant\" ? (\r\n                <>\r\n                  {msg?.content ? (\r\n                    <ContentRenderer text={msg.content} />\r\n                  ) : (\r\n                    <p>Unable to get a response from AI</p>\r\n                  )}\r\n                </>\r\n              ) : (\r\n                <>\r\n                  {typeof msg.content === \"string\"\r\n                    ? msg.content\r\n                    : msg.content.map((item, idx) =>\r\n                      item.type === \"text\" ? (\r\n                        <p key={idx}>{item.text}</p>\r\n                      ) : (\r\n                        <img\r\n                          key={idx}\r\n                          src={item.image_url.url}\r\n                          alt=\"User content\"\r\n                          style={{ height: \"100px\" }}\r\n                        />\r\n                      )\r\n                    )}\r\n                </>\r\n              )}\r\n            </>\r\n          </div>\r\n        ))}\r\n        {isLoading && <div className=\"loading-indicator\">Loading...</div>}\r\n      </div>\r\n\r\n      {/* Input and upload */}\r\n      <div className=\"chat-input-container\">\r\n        <textarea\r\n          className=\"chat-input\"\r\n          placeholder=\"Type your message here...\"\r\n          value={prompt}\r\n          onChange={(e) => setPrompt(e.target.value)}\r\n        ></textarea>\r\n        <input\r\n          type=\"file\"\r\n          accept=\"image/*\"\r\n          onChange={(e) => setImageFile(e.target.files[0])}\r\n          style={{ width: \"200px\", borderRadius: \"5px\", marginRight: \"10px\" }}\r\n        />\r\n        <button\r\n          disabled={isLoading}\r\n          className=\"send-button\"\r\n          onClick={handleChat}\r\n        >\r\n          Send\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default ChatGPTIntegration;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,aAAa,CAAC,CAAC;AACtB,SAASC,eAAe,EAAEC,SAAS,QAAQ,wBAAwB;AACnE,OAAOC,eAAe,MAAM,qCAAqC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAElE,SAASC,kBAAkBA,CAAA,EAAG;EAAAC,EAAA;EAC5B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACa,MAAM,EAAEC,SAAS,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACiB,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACmB,aAAa,EAAEC,gBAAgB,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;;EAEzD;EACAD,KAAK,CAACsB,SAAS,CAAC,MAAM;IACpB;IACA,MAAMC,cAAc,GAAGC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;IAC5D,IAAIF,cAAc,EAAE;MAClBV,WAAW,CAACa,IAAI,CAACC,KAAK,CAACJ,cAAc,CAAC,CAAC;IACzC,CAAC,MAAM;MACL;MACAV,WAAW,CAAC,CAAC;QACXe,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAE;MACX,CAAC,CAAC,CAAC;IACL;IACAR,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACArB,KAAK,CAACsB,SAAS,CAAC,MAAM;IACpB,IAAIF,aAAa,IAAIR,QAAQ,CAACkB,MAAM,GAAG,CAAC,EAAE;MACxCN,YAAY,CAACO,OAAO,CAAC,eAAe,EAAEL,IAAI,CAACM,SAAS,CAACpB,QAAQ,CAAC,CAAC;IACjE;EACF,CAAC,EAAE,CAACA,QAAQ,EAAEQ,aAAa,CAAC,CAAC;EAE7B,MAAMa,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAACnB,MAAM,CAACoB,IAAI,CAAC,CAAC,IAAI,CAAClB,SAAS,EAAE;IAElCG,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF,IAAIgB,QAAQ,GAAG,IAAI;;MAEnB;MACA,IAAInB,SAAS,EAAE;QACb,MAAMoB,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEtB,SAAS,CAAC;QAEnC,MAAMuB,IAAI,GAAG,MAAMnC,SAAS,CAACgC,QAAQ,CAAC;QAEtC,IAAIG,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEC,OAAO,EAAE;UACjBL,QAAQ,GAAGI,IAAI,CAACE,GAAG,CAAC,CAAC;UACrBC,OAAO,CAACC,GAAG,CAAC,aAAa,EAAER,QAAQ,CAAC;QACtC,CAAC,MAAM;UACL,MAAM,IAAIS,KAAK,CAAC,qBAAqB,CAAC;QACxC;MACF;;MAEA;MACA,MAAMC,WAAW,GAAGV,QAAQ,GACxB;QACAP,IAAI,EAAE,MAAM;QACZC,OAAO,EAAE,CACP;UAAEiB,IAAI,EAAE,MAAM;UAAEC,IAAI,EAAEjC;QAAO,CAAC,EAC9B;UAAEgC,IAAI,EAAE,WAAW;UAAEE,SAAS,EAAE;YAAEP,GAAG,EAAEN;UAAS;QAAE,CAAC;MAEvD,CAAC,GACC;QAAEP,IAAI,EAAE,MAAM;QAAEC,OAAO,EAAEf;MAAO,CAAC;MAErC,MAAMmC,eAAe,GAAG,CAAC,GAAGrC,QAAQ,EAAEiC,WAAW,CAAC;MAClDhC,WAAW,CAACoC,eAAe,CAAC;MAC5BlC,SAAS,CAAC,EAAE,CAAC;;MAEb;MACA,MAAMmC,WAAW,GAAG;QAAEtC,QAAQ,EAAEqC;MAAgB,CAAC;MAEjD,MAAME,OAAO,GAAG,MAAMhD,eAAe,CAAC+C,WAAW,CAAC;MAElD,MAAME,WAAW,GAAGD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEZ,IAAI;MACjCG,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAES,WAAW,CAAC;;MAE1C;MACAvC,WAAW,CAAEwC,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;QAAEzB,IAAI,EAAE,WAAW;QAAEC,OAAO,EAAEuB;MAAY,CAAC,CAC5C,CAAC;MAEFnC,YAAY,CAAC,IAAI,CAAC;IACpB,CAAC,CAAC,OAAOqC,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1CC,KAAK,CAAC,oEAAoE,CAAC;IAC7E,CAAC,SAAS;MACRpC,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMqC,cAAc,GAAIC,KAAK,IAAK;IAChC,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,EAAE;MACzBzB,UAAU,CAAC,CAAC,CAAC,CAAC;IAChB;EACF,CAAC;;EAED,oBACE1B,OAAA;IAAKoD,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAE7BrD,OAAA;MAAKoD,SAAS,EAAC,eAAe;MAAAC,QAAA,GAC3BhD,QAAQ,CAACiD,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBACvBxD,OAAA;QAEEoD,SAAS,EAAG,WAAUG,GAAG,CAAClC,IAAI,KAAK,MAAM,GAAG,cAAc,GAAG,mBAC1D,EAAE;QAAAgC,QAAA,eAELrD,OAAA,CAAAE,SAAA;UAAAmD,QAAA,EACGE,GAAG,CAAClC,IAAI,KAAK,WAAW,gBACvBrB,OAAA,CAAAE,SAAA;YAAAmD,QAAA,EACGE,GAAG,aAAHA,GAAG,eAAHA,GAAG,CAAEjC,OAAO,gBACXtB,OAAA,CAACF,eAAe;cAAC0C,IAAI,EAAEe,GAAG,CAACjC;YAAQ;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAEtC5D,OAAA;cAAAqD,QAAA,EAAG;YAAgC;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UACvC,gBACD,CAAC,gBAEH5D,OAAA,CAAAE,SAAA;YAAAmD,QAAA,EACG,OAAOE,GAAG,CAACjC,OAAO,KAAK,QAAQ,GAC5BiC,GAAG,CAACjC,OAAO,GACXiC,GAAG,CAACjC,OAAO,CAACgC,GAAG,CAAC,CAACO,IAAI,EAAEC,GAAG,KAC1BD,IAAI,CAACtB,IAAI,KAAK,MAAM,gBAClBvC,OAAA;cAAAqD,QAAA,EAAcQ,IAAI,CAACrB;YAAI,GAAfsB,GAAG;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAgB,CAAC,gBAE5B5D,OAAA;cAEE+D,GAAG,EAAEF,IAAI,CAACpB,SAAS,CAACP,GAAI;cACxB8B,GAAG,EAAC,cAAc;cAClBC,KAAK,EAAE;gBAAEC,MAAM,EAAE;cAAQ;YAAE,GAHtBJ,GAAG;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAIT,CAEL;UAAC,gBACH;QACH,gBACD;MAAC,GA/BEJ,KAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAgCP,CACN,CAAC,EACDjD,SAAS,iBAAIX,OAAA;QAAKoD,SAAS,EAAC,mBAAmB;QAAAC,QAAA,EAAC;MAAU;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D,CAAC,eAGN5D,OAAA;MAAKoD,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBACnCrD,OAAA;QACEoD,SAAS,EAAC,YAAY;QACtBe,WAAW,EAAC,2BAA2B;QACvCC,KAAK,EAAE7D,MAAO;QACd8D,QAAQ,EAAGC,CAAC,IAAK9D,SAAS,CAAC8D,CAAC,CAACC,MAAM,CAACH,KAAK;MAAE;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,eACZ5D,OAAA;QACEuC,IAAI,EAAC,MAAM;QACXiC,MAAM,EAAC,SAAS;QAChBH,QAAQ,EAAGC,CAAC,IAAK5D,YAAY,CAAC4D,CAAC,CAACC,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAE;QACjDR,KAAK,EAAE;UAAES,KAAK,EAAE,OAAO;UAAEC,YAAY,EAAE,KAAK;UAAEC,WAAW,EAAE;QAAO;MAAE;QAAAnB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE,CAAC,eACF5D,OAAA;QACE6E,QAAQ,EAAElE,SAAU;QACpByC,SAAS,EAAC,aAAa;QACvB0B,OAAO,EAAEpD,UAAW;QAAA2B,QAAA,EACrB;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACxD,EAAA,CApKQD,kBAAkB;AAAA4E,EAAA,GAAlB5E,kBAAkB;AAsK3B,eAAeA,kBAAkB;AAAC,IAAA4E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}