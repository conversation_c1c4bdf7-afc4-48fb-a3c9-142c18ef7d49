{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\common\\\\Home\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from \"react\";\nimport \"./index.css\";\nimport { Link, useLocation, useNavigate } from \"react-router-dom\";\nimport { motion } from \"framer-motion\";\nimport { TbArrowBigRightLinesFilled, TbBrain, TbBook, TbTrophy, TbUsers, TbStar, TbSchool, TbRocket, TbUserPlus, TbLogin } from \"react-icons/tb\";\nimport { message } from \"antd\";\nimport { useSelector } from \"react-redux\";\nimport Image1 from \"../../../assets/collage-1.png\";\nimport { contactUs } from \"../../../apicalls/users\";\nimport NotificationBell from \"../../../components/common/NotificationBell\";\nimport ProfilePicture from \"../../../components/common/ProfilePicture\";\nimport TryForFreeModal from \"../../../components/common/TryForFreeModal\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Home = () => {\n  _s();\n  const homeSectionRef = useRef(null);\n  const reviewsSectionRef = useRef(null);\n  const contactUsRef = useRef(null);\n  const [formData, setFormData] = useState({\n    name: \"\",\n    email: \"\",\n    message: \"\"\n  });\n  const [loading, setLoading] = useState(false);\n  const [responseMessage, setResponseMessage] = useState(\"\");\n  const [showTryForFreeModal, setShowTryForFreeModal] = useState(false);\n  const {\n    user\n  } = useSelector(state => state.user);\n  const navigate = useNavigate();\n\n  // Handle Try for Free modal\n  const handleTryForFree = () => {\n    setShowTryForFreeModal(true);\n  };\n  const handleTryForFreeSubmit = trialData => {\n    // Navigate to trial experience with user data\n    navigate('/trial', {\n      state: {\n        trialUserInfo: trialData\n      }\n    });\n    setShowTryForFreeModal(false);\n  };\n  const scrollToSection = (ref, offset = 80) => {\n    if (ref !== null && ref !== void 0 && ref.current) {\n      const sectionTop = ref.current.offsetTop;\n      window.scrollTo({\n        top: sectionTop - offset,\n        behavior: \"smooth\"\n      });\n    }\n  };\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setResponseMessage(\"\");\n    try {\n      const data = await contactUs(formData);\n      if (data.success) {\n        message.success(\"Message sent successfully!\");\n        setResponseMessage(\"Message sent successfully!\");\n        setFormData({\n          name: \"\",\n          email: \"\",\n          message: \"\"\n        });\n      } else {\n        setResponseMessage(data.message || \"Something went wrong.\");\n      }\n    } catch (error) {\n      setResponseMessage(\"Error sending message. Please try again.\");\n    }\n    setLoading(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"Home\",\n    children: [/*#__PURE__*/_jsxDEV(motion.header, {\n      initial: {\n        y: -20,\n        opacity: 0\n      },\n      animate: {\n        y: 0,\n        opacity: 1\n      },\n      className: \"nav-modern bg-gradient-to-r from-white/98 via-blue-50/95 to-white/98 backdrop-blur-xl border-b border-blue-100/50 sticky top-0 z-30 shadow-lg shadow-blue-100/20\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8 xl:px-10\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between h-12 xs:h-14 sm:h-16 md:h-18 lg:h-20\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hidden md:flex items-center space-x-4 lg:space-x-6\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => scrollToSection(reviewsSectionRef),\n                className: \"nav-item text-sm md:text-base\",\n                children: \"Reviews\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 93,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 flex justify-center\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.2\n              },\n              className: \"relative group flex items-center space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"rounded-md overflow-hidden border-2 border-gray-300 shadow-lg relative\",\n                style: {\n                  width: '32px',\n                  height: '24px'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"https://flagcdn.com/w40/tz.png\",\n                  alt: \"Tanzania Flag\",\n                  className: \"w-full h-full object-cover\",\n                  style: {\n                    objectFit: 'cover'\n                  },\n                  onError: e => {\n                    // Fallback to another flag source if first fails\n                    e.target.src = \"https://upload.wikimedia.org/wikipedia/commons/thumb/3/38/Flag_of_Tanzania.svg/32px-Flag_of_Tanzania.svg.png\";\n                    e.target.onerror = () => {\n                      // Final fallback - hide image and show text\n                      e.target.style.display = 'none';\n                      e.target.parentElement.innerHTML = '<div class=\"w-full h-full bg-blue-600 flex items-center justify-center text-white text-xs font-bold\">TZ</div>';\n                    };\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 113,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative brainwave-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-xl sm:text-2xl md:text-3xl font-black tracking-tight relative z-10 select-none\",\n                  style: {\n                    fontFamily: \"'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif\",\n                    letterSpacing: '-0.02em'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(motion.span, {\n                    className: \"relative inline-block\",\n                    initial: {\n                      opacity: 0,\n                      x: -30,\n                      scale: 0.8\n                    },\n                    animate: {\n                      opacity: 1,\n                      x: 0,\n                      scale: 1,\n                      textShadow: [\"0 0 10px rgba(59, 130, 246, 0.5)\", \"0 0 20px rgba(59, 130, 246, 0.8)\", \"0 0 10px rgba(59, 130, 246, 0.5)\"]\n                    },\n                    transition: {\n                      duration: 1,\n                      delay: 0.3,\n                      textShadow: {\n                        duration: 2,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }\n                    },\n                    whileHover: {\n                      scale: 1.1,\n                      rotate: [0, -2, 2, 0],\n                      transition: {\n                        duration: 0.3\n                      }\n                    },\n                    style: {\n                      color: '#1f2937',\n                      fontWeight: '900',\n                      textShadow: '0 0 10px rgba(59, 130, 246, 0.5)'\n                    },\n                    children: [\"Brain\", /*#__PURE__*/_jsxDEV(motion.div, {\n                      className: \"absolute -top-1 -right-1 w-2 h-2 rounded-full\",\n                      animate: {\n                        opacity: [0, 1, 0],\n                        scale: [0.5, 1.2, 0.5],\n                        backgroundColor: ['#3b82f6', '#60a5fa', '#3b82f6']\n                      },\n                      transition: {\n                        duration: 1.5,\n                        repeat: Infinity,\n                        delay: 2\n                      },\n                      style: {\n                        backgroundColor: '#3b82f6',\n                        boxShadow: '0 0 10px #3b82f6'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 174,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 138,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(motion.span, {\n                    className: \"relative inline-block\",\n                    initial: {\n                      opacity: 0,\n                      x: 30,\n                      scale: 0.8\n                    },\n                    animate: {\n                      opacity: 1,\n                      x: 0,\n                      scale: 1,\n                      y: [0, -2, 0, 2, 0],\n                      textShadow: [\"0 0 10px rgba(16, 185, 129, 0.5)\", \"0 0 20px rgba(16, 185, 129, 0.8)\", \"0 0 10px rgba(16, 185, 129, 0.5)\"]\n                    },\n                    transition: {\n                      duration: 1,\n                      delay: 0.5,\n                      y: {\n                        duration: 3,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      },\n                      textShadow: {\n                        duration: 2.5,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }\n                    },\n                    whileHover: {\n                      scale: 1.1,\n                      rotate: [0, 2, -2, 0],\n                      transition: {\n                        duration: 0.3\n                      }\n                    },\n                    style: {\n                      color: '#059669',\n                      fontWeight: '900',\n                      textShadow: '0 0 10px rgba(16, 185, 129, 0.5)'\n                    },\n                    children: [\"wave\", /*#__PURE__*/_jsxDEV(motion.div, {\n                      className: \"absolute top-0 left-0 w-1.5 h-1.5 rounded-full\",\n                      animate: {\n                        opacity: [0, 1, 0],\n                        x: [0, 40, 80],\n                        y: [0, -5, 0, 5, 0],\n                        backgroundColor: ['#10b981', '#34d399', '#10b981']\n                      },\n                      transition: {\n                        duration: 3,\n                        repeat: Infinity,\n                        delay: 1\n                      },\n                      style: {\n                        backgroundColor: '#10b981',\n                        boxShadow: '0 0 8px #10b981'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 236,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 194,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 132,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  className: \"absolute -bottom-1 left-0 h-1 rounded-full\",\n                  initial: {\n                    width: 0,\n                    opacity: 0\n                  },\n                  animate: {\n                    width: '100%',\n                    opacity: 1,\n                    boxShadow: ['0 0 10px rgba(16, 185, 129, 0.5)', '0 0 20px rgba(59, 130, 246, 0.8)', '0 0 10px rgba(16, 185, 129, 0.5)']\n                  },\n                  transition: {\n                    duration: 1.5,\n                    delay: 1.2,\n                    boxShadow: {\n                      duration: 2,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    }\n                  },\n                  style: {\n                    background: 'linear-gradient(90deg, #3b82f6, #10b981, #3b82f6)',\n                    boxShadow: '0 0 15px rgba(16, 185, 129, 0.6)'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"rounded-full overflow-hidden border-2 border-white/20 relative\",\n                style: {\n                  background: '#f0f0f0',\n                  boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                  width: '32px',\n                  height: '32px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"/favicon.png\",\n                  alt: \"Brainwave Logo\",\n                  className: \"w-full h-full object-cover\",\n                  style: {\n                    objectFit: 'cover'\n                  },\n                  onError: e => {\n                    e.target.style.display = 'none';\n                    e.target.nextSibling.style.display = 'flex';\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-full h-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold\",\n                  style: {\n                    display: 'none',\n                    fontSize: '12px'\n                  },\n                  children: \"\\uD83E\\uDDE0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 306,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-gray-900/5 to-blue-500/5 blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 -z-10 scale-110\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-end space-x-2 sm:space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hidden md:flex items-center space-x-4 lg:space-x-6\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => scrollToSection(contactUsRef),\n                className: \"nav-item text-sm md:text-base\",\n                children: \"Contact Us\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 326,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 15\n            }, this), user && !(user !== null && user !== void 0 && user.isAdmin) && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.8\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                duration: 0.5,\n                delay: 0.2\n              },\n              children: /*#__PURE__*/_jsxDEV(NotificationBell, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 17\n            }, this), user && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.8\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                duration: 0.5,\n                delay: 0.3\n              },\n              className: \"flex items-center space-x-2 group\",\n              children: [/*#__PURE__*/_jsxDEV(ProfilePicture, {\n                user: user,\n                size: \"sm\",\n                showOnlineStatus: true,\n                style: {\n                  width: '32px',\n                  height: '32px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 349,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hidden sm:block text-right\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs md:text-sm font-medium text-gray-700 group-hover:text-green-600 transition-colors duration-300\",\n                  children: (user === null || user === void 0 ? void 0 : user.name) || 'User'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-gray-500 group-hover:text-green-500 transition-colors duration-300\",\n                  children: [\"Class \", user === null || user === void 0 ? void 0 : user.class]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 364,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 342,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative overflow-hidden bg-gradient-to-br from-blue-100 via-indigo-50 to-purple-100 py-12 min-h-[200px] z-10\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 opacity-10\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0\",\n          style: {\n            backgroundImage: `radial-gradient(circle at 25% 25%, #3b82f6 2px, transparent 2px),\n                             radial-gradient(circle at 75% 75%, #8b5cf6 2px, transparent 2px)`,\n            backgroundSize: '50px 50px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 378,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          animate: {\n            y: [0, -35, 0],\n            rotate: [0, 12, 0],\n            scale: [1, 1.3, 1]\n          },\n          transition: {\n            duration: 4,\n            repeat: Infinity,\n            ease: \"easeInOut\"\n          },\n          className: \"absolute top-8 left-1/6 text-blue-600 opacity-90 drop-shadow-2xl\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(TbBook, {\n              className: \"w-20 h-20\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 bg-blue-200 rounded-full blur-xl opacity-30 scale-150\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          animate: {\n            y: [0, -40, 0],\n            rotate: [0, -8, 0],\n            scale: [1, 1.4, 1]\n          },\n          transition: {\n            duration: 3.5,\n            repeat: Infinity,\n            ease: \"easeInOut\",\n            delay: 0.5\n          },\n          className: \"absolute top-4 right-1/5 text-green-600 opacity-95 drop-shadow-2xl\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n              className: \"w-24 h-24\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 bg-green-200 rounded-full blur-xl opacity-30 scale-150\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 421,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 407,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          animate: {\n            y: [0, -45, 0],\n            rotate: [0, 15, 0],\n            scale: [1, 1.5, 1]\n          },\n          transition: {\n            duration: 4.5,\n            repeat: Infinity,\n            ease: \"easeInOut\",\n            delay: 1\n          },\n          className: \"absolute top-12 right-1/3 text-purple-600 opacity-90 drop-shadow-2xl\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n              className: \"w-18 h-18\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 bg-purple-200 rounded-full blur-xl opacity-30 scale-150\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 427,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          animate: {\n            y: [0, -32, 0],\n            rotate: [0, -12, 0],\n            scale: [1, 1.2, 1]\n          },\n          transition: {\n            duration: 3.8,\n            repeat: Infinity,\n            ease: \"easeInOut\",\n            delay: 1.5\n          },\n          className: \"absolute top-16 left-1/3 text-orange-500 opacity-90 drop-shadow-2xl\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(TbStar, {\n              className: \"w-16 h-16\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 bg-orange-200 rounded-full blur-xl opacity-30 scale-150\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 447,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          animate: {\n            y: [0, -38, 0],\n            rotate: [0, 10, 0],\n            scale: [1, 1.6, 1]\n          },\n          transition: {\n            duration: 4.2,\n            repeat: Infinity,\n            ease: \"easeInOut\",\n            delay: 2\n          },\n          className: \"absolute top-6 left-1/2 text-indigo-600 opacity-95 drop-shadow-2xl\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(TbRocket, {\n              className: \"w-22 h-22\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 482,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 bg-indigo-200 rounded-full blur-xl opacity-30 scale-150\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 483,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 481,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 467,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          animate: {\n            y: [0, -25, 0],\n            rotate: [0, -6, 0],\n            scale: [1, 1.3, 1]\n          },\n          transition: {\n            duration: 5,\n            repeat: Infinity,\n            ease: \"easeInOut\",\n            delay: 2.5\n          },\n          className: \"absolute top-10 left-1/12 text-teal-500 opacity-85 drop-shadow-2xl\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(TbUsers, {\n              className: \"w-16 h-16\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 503,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 bg-teal-200 rounded-full blur-xl opacity-30 scale-150\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 504,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 502,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 488,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          animate: {\n            scale: [0, 1.5, 0],\n            opacity: [0, 1, 0],\n            rotate: [0, 180, 360]\n          },\n          transition: {\n            duration: 3,\n            repeat: Infinity,\n            ease: \"easeInOut\",\n            delay: 0\n          },\n          className: \"absolute top-4 left-1/8 text-yellow-400 drop-shadow-lg\",\n          children: /*#__PURE__*/_jsxDEV(TbStar, {\n            className: \"w-8 h-8\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 523,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 509,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          animate: {\n            scale: [0, 1.2, 0],\n            opacity: [0, 1, 0],\n            rotate: [0, -180, -360]\n          },\n          transition: {\n            duration: 2.5,\n            repeat: Infinity,\n            ease: \"easeInOut\",\n            delay: 1\n          },\n          className: \"absolute top-14 right-1/8 text-yellow-400 drop-shadow-lg\",\n          children: /*#__PURE__*/_jsxDEV(TbStar, {\n            className: \"w-6 h-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 540,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 526,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          animate: {\n            scale: [0, 1.3, 0],\n            opacity: [0, 1, 0],\n            rotate: [0, 90, 180]\n          },\n          transition: {\n            duration: 2.8,\n            repeat: Infinity,\n            ease: \"easeInOut\",\n            delay: 2\n          },\n          className: \"absolute top-2 left-3/4 text-yellow-400 drop-shadow-lg\",\n          children: /*#__PURE__*/_jsxDEV(TbStar, {\n            className: \"w-7 h-7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 557,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 543,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          animate: {\n            y: [0, -100, 0],\n            x: [0, 20, 0],\n            opacity: [0, 0.6, 0]\n          },\n          transition: {\n            duration: 6,\n            repeat: Infinity,\n            ease: \"easeInOut\",\n            delay: 0\n          },\n          className: \"absolute bottom-0 left-1/4 w-2 h-2 bg-blue-400 rounded-full\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 561,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          animate: {\n            y: [0, -120, 0],\n            x: [0, -15, 0],\n            opacity: [0, 0.8, 0]\n          },\n          transition: {\n            duration: 7,\n            repeat: Infinity,\n            ease: \"easeInOut\",\n            delay: 1.5\n          },\n          className: \"absolute bottom-0 right-1/3 w-3 h-3 bg-purple-400 rounded-full\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 576,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          animate: {\n            y: [0, -90, 0],\n            x: [0, 10, 0],\n            opacity: [0, 0.7, 0]\n          },\n          transition: {\n            duration: 5.5,\n            repeat: Infinity,\n            ease: \"easeInOut\",\n            delay: 3\n          },\n          className: \"absolute bottom-0 left-2/3 w-2 h-2 bg-green-400 rounded-full\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 591,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 386,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-0 left-0 w-full\",\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"relative block w-full h-16\",\n          xmlns: \"http://www.w3.org/2000/svg\",\n          viewBox: \"0 0 1200 120\",\n          preserveAspectRatio: \"none\",\n          children: [/*#__PURE__*/_jsxDEV(motion.path, {\n            d: \"M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z\",\n            fill: \"rgba(59, 130, 246, 0.4)\",\n            animate: {\n              d: [\"M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z\", \"M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z\", \"M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z\"]\n            },\n            transition: {\n              duration: 5,\n              repeat: Infinity,\n              ease: \"easeInOut\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 616,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.path, {\n            d: \"M0,0V35c35,18,70,30,105,25c45-7,90-25,135-28c55-4,110,12,165,18c50,5,100,3,150-6c40-6,80-18,120-12c30,4,60,15,90,10c25-4,50-15,75-10V0Z\",\n            fill: \"rgba(139, 92, 246, 0.3)\",\n            animate: {\n              d: [\"M0,0V35c35,18,70,30,105,25c45-7,90-25,135-28c55-4,110,12,165,18c50,5,100,3,150-6c40-6,80-18,120-12c30,4,60,15,90,10c25-4,50-15,75-10V0Z\", \"M0,0V28c28,14,62,35,96,30c40-5,85-30,130-25c50,6,105,18,160,12c45-5,95-10,145,3c35,8,75,22,115,15c25-4,52-18,80-6c18,7,40,22,65,18V0Z\", \"M0,0V35c35,18,70,30,105,25c45-7,90-25,135-28c55-4,110,12,165,18c50,5,100,3,150-6c40-6,80-18,120-12c30,4,60,15,90,10c25-4,50-15,75-10V0Z\"]\n            },\n            transition: {\n              duration: 7,\n              repeat: Infinity,\n              ease: \"easeInOut\",\n              delay: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 633,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.path, {\n            d: \"M0,0V20c20,10,45,20,70,18c30-3,60-15,90-16c35-2,70,8,105,12c30,3,60,2,90-4c25-4,50-12,75-8c20,3,40,10,60,7c15-2,30-8,45-6V0Z\",\n            fill: \"rgba(16, 185, 129, 0.2)\",\n            animate: {\n              d: [\"M0,0V20c20,10,45,20,70,18c30-3,60-15,90-16c35-2,70,8,105,12c30,3,60,2,90-4c25-4,50-12,75-8c20,3,40,10,60,7c15-2,30-8,45-6V0Z\", \"M0,0V15c18,8,42,25,68,22c28-3,58-20,88-18c32,2,68,12,102,8c28-3,58-6,88,2c22,5,48,14,72,10c18-3,38-12,58-4c12,5,28,15,48,12V0Z\", \"M0,0V20c20,10,45,20,70,18c30-3,60-15,90-16c35-2,70,8,105,12c30,3,60,2,90-4c25-4,50-12,75-8c20,3,40,10,60,7c15-2,30-8,45-6V0Z\"]\n            },\n            transition: {\n              duration: 9,\n              repeat: Infinity,\n              ease: \"easeInOut\",\n              delay: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 651,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 609,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 608,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 376,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      ref: homeSectionRef,\n      className: \"hero-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hero-grid\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: -50\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.6\n            },\n            className: \"hero-content\",\n            children: [/*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.2\n              },\n              className: \"hero-badge\",\n              children: [/*#__PURE__*/_jsxDEV(TbSchool, {\n                className: \"w-4 h-4 sm:w-5 sm:h-5 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 689,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs sm:text-sm\",\n                children: \"#1 Educational Platform in Tanzania\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 690,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 683,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"hero-title\",\n              children: [\"Fueling Bright Futures with\", \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gradient\",\n                children: [\"Education\", /*#__PURE__*/_jsxDEV(TbArrowBigRightLinesFilled, {\n                  className: \"inline w-8 h-8 ml-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 697,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 695,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 693,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"hero-subtitle\",\n              children: \"Discover limitless learning opportunities with our comprehensive online study platform. Study anywhere, anytime, and achieve your academic goals with confidence.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 701,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.4\n              },\n              className: \"cta-section\",\n              children: !user ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col sm:flex-row items-center justify-center gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                  onClick: handleTryForFree,\n                  className: \"btn btn-primary btn-large bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 border-0 shadow-lg hover:shadow-xl\",\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TbRocket, {\n                    className: \"w-5 h-5 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 723,\n                    columnNumber: 23\n                  }, this), \"Try for Free\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 717,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/register\",\n                  children: /*#__PURE__*/_jsxDEV(motion.button, {\n                    className: \"btn btn-primary btn-large bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 border-0 shadow-lg hover:shadow-xl\",\n                    whileHover: {\n                      scale: 1.05\n                    },\n                    whileTap: {\n                      scale: 0.95\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(TbUserPlus, {\n                      className: \"w-5 h-5 mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 734,\n                      columnNumber: 25\n                    }, this), \"Register Now\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 729,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 728,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/login\",\n                  children: /*#__PURE__*/_jsxDEV(motion.button, {\n                    className: \"btn btn-outline btn-large border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white transition-all duration-300\",\n                    whileHover: {\n                      scale: 1.05\n                    },\n                    whileTap: {\n                      scale: 0.95\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(TbLogin, {\n                      className: \"w-5 h-5 mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 746,\n                      columnNumber: 25\n                    }, this), \"Login Now\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 741,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 740,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 715,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/dashboard\",\n                children: /*#__PURE__*/_jsxDEV(motion.button, {\n                  className: \"btn btn-primary btn-large bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 border-0 shadow-lg hover:shadow-xl\",\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                    className: \"w-5 h-5 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 758,\n                    columnNumber: 23\n                  }, this), \"Go to Dashboard\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 753,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 752,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 708,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.6\n              },\n              className: \"trust-indicators\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"trust-indicator\",\n                children: [/*#__PURE__*/_jsxDEV(TbUsers, {\n                  style: {\n                    color: '#007BFF'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 775,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"15K+ Students\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 776,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 774,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"trust-indicator\",\n                children: [/*#__PURE__*/_jsxDEV(TbStar, {\n                  style: {\n                    color: '#f59e0b'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 779,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"4.9/5 Rating\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 780,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 778,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"trust-indicator\",\n                children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n                  style: {\n                    color: '#007BFF'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 783,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Award Winning\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 784,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 782,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 768,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 677,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: 50\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: 0.3\n            },\n            className: \"hero-image\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: Image1,\n                alt: \"Students Learning\",\n                loading: \"lazy\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 797,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                animate: {\n                  y: [-10, 10, -10]\n                },\n                transition: {\n                  duration: 4,\n                  repeat: Infinity\n                },\n                className: \"floating-element\",\n                style: {\n                  top: '-1rem',\n                  left: '-1rem'\n                },\n                children: /*#__PURE__*/_jsxDEV(TbBook, {\n                  style: {\n                    color: '#007BFF'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 810,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 804,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                animate: {\n                  y: [10, -10, 10]\n                },\n                transition: {\n                  duration: 3,\n                  repeat: Infinity\n                },\n                className: \"floating-element\",\n                style: {\n                  bottom: '-1rem',\n                  right: '-1rem'\n                },\n                children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n                  style: {\n                    color: '#f59e0b'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 819,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 813,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 796,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 790,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 675,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 674,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 673,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 sm:py-20 bg-gradient-to-br from-blue-50 via-white to-purple-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 50\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6\n          },\n          viewport: {\n            once: true\n          },\n          className: \"grid grid-cols-2 md:grid-cols-4 gap-6 sm:gap-8\",\n          children: [{\n            number: \"15K+\",\n            text: \"Active Students\",\n            icon: TbUsers,\n            color: \"from-blue-500 to-blue-600\"\n          }, {\n            number: \"500+\",\n            text: \"Expert Teachers\",\n            icon: TbSchool,\n            color: \"from-green-500 to-green-600\"\n          }, {\n            number: \"1000+\",\n            text: \"Video Lessons\",\n            icon: TbBook,\n            color: \"from-purple-500 to-purple-600\"\n          }, {\n            number: \"98%\",\n            text: \"Success Rate\",\n            icon: TbTrophy,\n            color: \"from-orange-500 to-orange-600\"\n          }].map((stat, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 30,\n              scale: 0.9\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0,\n              scale: 1\n            },\n            transition: {\n              duration: 0.5,\n              delay: index * 0.1\n            },\n            viewport: {\n              once: true\n            },\n            whileHover: {\n              scale: 1.05,\n              y: -5\n            },\n            className: \"bg-white rounded-2xl p-4 sm:p-6 shadow-lg hover:shadow-xl transition-all duration-300 text-center group border border-gray-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `w-12 h-12 sm:w-16 sm:h-16 mx-auto mb-3 sm:mb-4 rounded-full bg-gradient-to-r ${stat.color} flex items-center justify-center group-hover:scale-110 transition-transform duration-300`,\n              children: /*#__PURE__*/_jsxDEV(stat.icon, {\n                className: \"w-6 h-6 sm:w-8 sm:h-8 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 853,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 852,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl sm:text-3xl md:text-4xl font-bold text-gray-800 mb-1 sm:mb-2\",\n              children: stat.number\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 855,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs sm:text-sm md:text-base text-gray-600 font-medium\",\n              children: stat.text\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 856,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 843,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 830,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 829,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 828,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      ref: reviewsSectionRef,\n      className: \"reviews-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"reviews-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"reviews-title\",\n          children: \"Reviews from our students\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 866,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"reviews-grid\",\n          children: [{\n            rating: 5,\n            text: \"BrainWave has completely transformed my learning experience. The interactive lessons and expert guidance helped me excel in my studies.\",\n            user: {\n              name: \"Sarah Johnson\"\n            }\n          }, {\n            rating: 5,\n            text: \"The platform is incredibly user-friendly and the content quality is outstanding. I've improved my grades significantly since joining.\",\n            user: {\n              name: \"Michael Chen\"\n            }\n          }, {\n            rating: 5,\n            text: \"Amazing platform with excellent teachers. The video lessons are clear and easy to understand. Highly recommended!\",\n            user: {\n              name: \"Amina Hassan\"\n            }\n          }].map((review, index) => {\n            var _review$user;\n            return /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              whileInView: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: index * 0.1\n              },\n              viewport: {\n                once: true\n              },\n              className: \"review-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"review-rating\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    color: '#f59e0b',\n                    fontSize: '1.25rem'\n                  },\n                  children: '★'.repeat(review.rating)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 896,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 895,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"review-text\",\n                children: [\"\\\"\", review.text, \"\\\"\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 900,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"review-divider\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 901,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"review-author\",\n                children: (_review$user = review.user) === null || _review$user === void 0 ? void 0 : _review$user.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 902,\n                columnNumber: 17\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 887,\n              columnNumber: 15\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 869,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 865,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 864,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      ref: contactUsRef,\n      className: \"py-16 sm:py-20 bg-gradient-to-br from-gray-50 to-blue-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-12\",\n          children: [/*#__PURE__*/_jsxDEV(motion.h2, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6\n            },\n            viewport: {\n              once: true\n            },\n            className: \"text-3xl sm:text-4xl font-bold text-gray-800 mb-4\",\n            children: \"Contact Us\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 913,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: 0.1\n            },\n            viewport: {\n              once: true\n            },\n            className: \"text-lg text-gray-600 max-w-2xl mx-auto\",\n            children: \"Get in touch with us for any questions or support. We're here to help you succeed!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 922,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 912,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 lg:grid-cols-2 gap-12\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: -30\n            },\n            whileInView: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.6\n            },\n            viewport: {\n              once: true\n            },\n            className: \"bg-white rounded-2xl shadow-xl p-6 sm:p-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-2xl font-bold text-gray-800\",\n                children: \"Send us a Message\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 943,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-500\",\n                children: \"or\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 944,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"https://wa.me/25565528549?text=Hello! I'm interested in learning more about BrainWave Educational Platform.\",\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                className: \"inline-flex items-center space-x-2 bg-green-600 text-white px-4 py-2 rounded-lg font-bold hover:bg-green-700 hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl border-2 border-green-500 hover:border-green-600\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5\",\n                  fill: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.346\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 952,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 951,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"hidden sm:inline\",\n                  children: \"WhatsApp\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 954,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 945,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 942,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n              onSubmit: handleSubmit,\n              className: \"space-y-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 959,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"name\",\n                  placeholder: \"Your Full Name\",\n                  className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all\",\n                  value: formData.name,\n                  onChange: handleChange,\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 960,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 958,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Email\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 971,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"email\",\n                  name: \"email\",\n                  placeholder: \"<EMAIL>\",\n                  className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all\",\n                  value: formData.email,\n                  onChange: handleChange,\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 972,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 970,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Message\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 983,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  name: \"message\",\n                  placeholder: \"Tell us how we can help you...\",\n                  rows: \"5\",\n                  className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all resize-none\",\n                  value: formData.message,\n                  onChange: handleChange,\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 984,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 982,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                disabled: loading,\n                className: \"w-full py-3 px-6 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-medium rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-300 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed\",\n                children: loading ? \"Sending...\" : \"Send Message\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 994,\n                columnNumber: 17\n              }, this), responseMessage && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-center text-green-600 font-medium\",\n                children: responseMessage\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1002,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 957,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 935,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: 30\n            },\n            whileInView: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.6\n            },\n            viewport: {\n              once: true\n            },\n            className: \"space-y-8\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white rounded-2xl shadow-xl p-6 sm:p-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-2xl font-bold text-gray-800 mb-6\",\n                children: \"Other Ways to Reach Us\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1019,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-6 h-6 text-green-600\",\n                      fill: \"currentColor\",\n                      viewBox: \"0 0 24 24\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.346\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1024,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1023,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1022,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"font-medium text-gray-800\",\n                      children: \"WhatsApp Support\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1028,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-600\",\n                      children: \"+255 655 285 49\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1029,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1027,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1021,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-6 h-6 text-blue-600\",\n                      fill: \"none\",\n                      stroke: \"currentColor\",\n                      viewBox: \"0 0 24 24\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: \"2\",\n                        d: \"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1035,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1034,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1033,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"font-medium text-gray-800\",\n                      children: \"Email Support\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1039,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-600\",\n                      children: \"<EMAIL>\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1040,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1038,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1032,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-6 h-6 text-purple-600\",\n                      fill: \"none\",\n                      stroke: \"currentColor\",\n                      viewBox: \"0 0 24 24\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: \"2\",\n                        d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1046,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1045,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1044,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"font-medium text-gray-800\",\n                      children: \"Response Time\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1050,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-600\",\n                      children: \"Usually within 2 hours\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1051,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1049,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1043,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-6 h-6 text-indigo-600\",\n                      fill: \"none\",\n                      stroke: \"currentColor\",\n                      viewBox: \"0 0 24 24\",\n                      children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: \"2\",\n                        d: \"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1057,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: \"2\",\n                        d: \"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1058,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1056,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1055,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"font-medium text-gray-800\",\n                      children: \"Location\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1062,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-600\",\n                      children: \"Dar es Salaam, Tanzania\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1063,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1061,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1054,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1020,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1018,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1010,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 933,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 911,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 910,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"footer\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer-content\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"footer-text\",\n          children: \"\\xA9 2024 BrainWave Educational Platform. All rights reserved.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1076,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1075,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1074,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TryForFreeModal, {\n      isOpen: showTryForFreeModal,\n      onClose: () => setShowTryForFreeModal(false),\n      onSubmit: handleTryForFreeSubmit\n    }, showTryForFreeModal ? 'open' : 'closed', false, {\n      fileName: _jsxFileName,\n      lineNumber: 1083,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 81,\n    columnNumber: 5\n  }, this);\n};\n_s(Home, \"2+OooZ71GBUhaFkx9H6QmV/qdRg=\", false, function () {\n  return [useSelector, useNavigate];\n});\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "Link", "useLocation", "useNavigate", "motion", "TbArrowBigRightLinesFilled", "TbBrain", "TbBook", "TbTrophy", "TbUsers", "TbStar", "TbSchool", "TbRocket", "TbUserPlus", "<PERSON>b<PERSON><PERSON><PERSON>", "message", "useSelector", "Image1", "contactUs", "NotificationBell", "ProfilePicture", "TryForFreeModal", "jsxDEV", "_jsxDEV", "Home", "_s", "homeSectionRef", "reviewsSectionRef", "contactUsRef", "formData", "setFormData", "name", "email", "loading", "setLoading", "responseMessage", "setResponseMessage", "showTryForFreeModal", "setShowTryForFreeModal", "user", "state", "navigate", "handleTryForFree", "handleTryForFreeSubmit", "trialData", "trialUserInfo", "scrollToSection", "ref", "offset", "current", "sectionTop", "offsetTop", "window", "scrollTo", "top", "behavior", "handleChange", "e", "value", "target", "handleSubmit", "preventDefault", "data", "success", "error", "className", "children", "header", "initial", "y", "opacity", "animate", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "scale", "transition", "duration", "delay", "style", "width", "height", "src", "alt", "objectFit", "onError", "onerror", "display", "parentElement", "innerHTML", "fontFamily", "letterSpacing", "span", "x", "textShadow", "repeat", "Infinity", "ease", "whileHover", "rotate", "color", "fontWeight", "backgroundColor", "boxShadow", "background", "nextS<PERSON>ling", "fontSize", "isAdmin", "size", "showOnlineStatus", "class", "backgroundImage", "backgroundSize", "xmlns", "viewBox", "preserveAspectRatio", "path", "d", "fill", "button", "whileTap", "to", "left", "bottom", "right", "whileInView", "viewport", "once", "number", "text", "icon", "map", "stat", "index", "rating", "review", "_review$user", "h2", "p", "href", "rel", "onSubmit", "type", "placeholder", "onChange", "required", "rows", "disabled", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "isOpen", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/common/Home/index.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from \"react\";\r\nimport \"./index.css\";\r\nimport { Link, useLocation, useNavigate } from \"react-router-dom\";\r\nimport { motion } from \"framer-motion\";\r\nimport {\r\n  TbArrowBigRightLinesFilled,\r\n  TbBrain,\r\n  TbBook,\r\n  TbTrophy,\r\n  TbUsers,\r\n  TbStar,\r\n  TbSchool,\r\n  TbRocket,\r\n  TbUserPlus,\r\n  TbLogin\r\n} from \"react-icons/tb\";\r\nimport { message } from \"antd\";\r\nimport { useSelector } from \"react-redux\";\r\nimport Image1 from \"../../../assets/collage-1.png\";\r\nimport { contactUs } from \"../../../apicalls/users\";\r\nimport NotificationBell from \"../../../components/common/NotificationBell\";\r\nimport ProfilePicture from \"../../../components/common/ProfilePicture\";\r\nimport TryForFreeModal from \"../../../components/common/TryForFreeModal\";\r\n\r\nconst Home = () => {\r\n  const homeSectionRef = useRef(null);\r\n  const reviewsSectionRef = useRef(null);\r\n  const contactUsRef = useRef(null);\r\n  const [formData, setFormData] = useState({ name: \"\", email: \"\", message: \"\" });\r\n  const [loading, setLoading] = useState(false);\r\n  const [responseMessage, setResponseMessage] = useState(\"\");\r\n  const [showTryForFreeModal, setShowTryForFreeModal] = useState(false);\r\n  const { user } = useSelector((state) => state.user);\r\n  const navigate = useNavigate();\r\n\r\n  // Handle Try for Free modal\r\n  const handleTryForFree = () => {\r\n    setShowTryForFreeModal(true);\r\n  };\r\n\r\n  const handleTryForFreeSubmit = (trialData) => {\r\n    // Navigate to trial experience with user data\r\n    navigate('/trial', { state: { trialUserInfo: trialData } });\r\n    setShowTryForFreeModal(false);\r\n  };\r\n\r\n\r\n\r\n  const scrollToSection = (ref, offset = 80) => {\r\n    if (ref?.current) {\r\n      const sectionTop = ref.current.offsetTop;\r\n      window.scrollTo({ top: sectionTop - offset, behavior: \"smooth\" });\r\n    }\r\n  };\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setFormData({ ...formData, [name]: value });\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    setLoading(true);\r\n    setResponseMessage(\"\");\r\n    try {\r\n      const data = await contactUs(formData);\r\n      if (data.success) {\r\n        message.success(\"Message sent successfully!\");\r\n        setResponseMessage(\"Message sent successfully!\");\r\n        setFormData({ name: \"\", email: \"\", message: \"\" });\r\n      } else {\r\n        setResponseMessage(data.message || \"Something went wrong.\");\r\n      }\r\n    } catch (error) {\r\n      setResponseMessage(\"Error sending message. Please try again.\");\r\n    }\r\n    setLoading(false);\r\n  };\r\n\r\n  return (\r\n    <div className=\"Home\">\r\n      {/* Modern Responsive Header - Same as ProtectedRoute */}\r\n      <motion.header\r\n        initial={{ y: -20, opacity: 0 }}\r\n        animate={{ y: 0, opacity: 1 }}\r\n        className=\"nav-modern bg-gradient-to-r from-white/98 via-blue-50/95 to-white/98 backdrop-blur-xl border-b border-blue-100/50 sticky top-0 z-30 shadow-lg shadow-blue-100/20\"\r\n      >\r\n        <div className=\"px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8 xl:px-10\">\r\n          <div className=\"flex items-center justify-between h-12 xs:h-14 sm:h-16 md:h-18 lg:h-20\">\r\n            {/* Left section - Reviews */}\r\n            <div className=\"flex items-center space-x-2\">\r\n              <div className=\"hidden md:flex items-center space-x-4 lg:space-x-6\">\r\n                <button onClick={() => scrollToSection(reviewsSectionRef)} className=\"nav-item text-sm md:text-base\">Reviews</button>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Center Section - Tanzania Flag + Brainwave Title + Logo */}\r\n            <div className=\"flex-1 flex justify-center\">\r\n              <motion.div\r\n                initial={{ opacity: 0, scale: 0.9 }}\r\n                animate={{ opacity: 1, scale: 1 }}\r\n                transition={{ duration: 0.6, delay: 0.2 }}\r\n                className=\"relative group flex items-center space-x-3\"\r\n              >\r\n                {/* Tanzania Flag - Using actual flag image */}\r\n                <div\r\n                  className=\"rounded-md overflow-hidden border-2 border-gray-300 shadow-lg relative\"\r\n                  style={{\r\n                    width: '32px',\r\n                    height: '24px'\r\n                  }}\r\n                >\r\n                  <img\r\n                    src=\"https://flagcdn.com/w40/tz.png\"\r\n                    alt=\"Tanzania Flag\"\r\n                    className=\"w-full h-full object-cover\"\r\n                    style={{ objectFit: 'cover' }}\r\n                    onError={(e) => {\r\n                      // Fallback to another flag source if first fails\r\n                      e.target.src = \"https://upload.wikimedia.org/wikipedia/commons/thumb/3/38/Flag_of_Tanzania.svg/32px-Flag_of_Tanzania.svg.png\";\r\n                      e.target.onerror = () => {\r\n                        // Final fallback - hide image and show text\r\n                        e.target.style.display = 'none';\r\n                        e.target.parentElement.innerHTML = '<div class=\"w-full h-full bg-blue-600 flex items-center justify-center text-white text-xs font-bold\">TZ</div>';\r\n                      };\r\n                    }}\r\n                  />\r\n                </div>\r\n\r\n                {/* Amazing Animated Brainwave Text */}\r\n                <div className=\"relative brainwave-container\">\r\n                  <h1 className=\"text-xl sm:text-2xl md:text-3xl font-black tracking-tight relative z-10 select-none\"\r\n                      style={{\r\n                        fontFamily: \"'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif\",\r\n                        letterSpacing: '-0.02em'\r\n                      }}>\r\n                    {/* Brain - with amazing effects */}\r\n                    <motion.span\r\n                      className=\"relative inline-block\"\r\n                      initial={{ opacity: 0, x: -30, scale: 0.8 }}\r\n                      animate={{\r\n                        opacity: 1,\r\n                        x: 0,\r\n                        scale: 1,\r\n                        textShadow: [\r\n                          \"0 0 10px rgba(59, 130, 246, 0.5)\",\r\n                          \"0 0 20px rgba(59, 130, 246, 0.8)\",\r\n                          \"0 0 10px rgba(59, 130, 246, 0.5)\"\r\n                        ]\r\n                      }}\r\n                      transition={{\r\n                        duration: 1,\r\n                        delay: 0.3,\r\n                        textShadow: {\r\n                          duration: 2,\r\n                          repeat: Infinity,\r\n                          ease: \"easeInOut\"\r\n                        }\r\n                      }}\r\n                      whileHover={{\r\n                        scale: 1.1,\r\n                        rotate: [0, -2, 2, 0],\r\n                        transition: { duration: 0.3 }\r\n                      }}\r\n                      style={{\r\n                        color: '#1f2937',\r\n                        fontWeight: '900',\r\n                        textShadow: '0 0 10px rgba(59, 130, 246, 0.5)'\r\n                      }}\r\n                    >\r\n                      Brain\r\n\r\n                      {/* Electric spark */}\r\n                      <motion.div\r\n                        className=\"absolute -top-1 -right-1 w-2 h-2 rounded-full\"\r\n                        animate={{\r\n                          opacity: [0, 1, 0],\r\n                          scale: [0.5, 1.2, 0.5],\r\n                          backgroundColor: ['#3b82f6', '#60a5fa', '#3b82f6']\r\n                        }}\r\n                        transition={{\r\n                          duration: 1.5,\r\n                          repeat: Infinity,\r\n                          delay: 2\r\n                        }}\r\n                        style={{\r\n                          backgroundColor: '#3b82f6',\r\n                          boxShadow: '0 0 10px #3b82f6'\r\n                        }}\r\n                      />\r\n                    </motion.span>\r\n\r\n                    {/* Wave - with flowing effects (no space) */}\r\n                    <motion.span\r\n                      className=\"relative inline-block\"\r\n                      initial={{ opacity: 0, x: 30, scale: 0.8 }}\r\n                      animate={{\r\n                        opacity: 1,\r\n                        x: 0,\r\n                        scale: 1,\r\n                        y: [0, -2, 0, 2, 0],\r\n                        textShadow: [\r\n                          \"0 0 10px rgba(16, 185, 129, 0.5)\",\r\n                          \"0 0 20px rgba(16, 185, 129, 0.8)\",\r\n                          \"0 0 10px rgba(16, 185, 129, 0.5)\"\r\n                        ]\r\n                      }}\r\n                      transition={{\r\n                        duration: 1,\r\n                        delay: 0.5,\r\n                        y: {\r\n                          duration: 3,\r\n                          repeat: Infinity,\r\n                          ease: \"easeInOut\"\r\n                        },\r\n                        textShadow: {\r\n                          duration: 2.5,\r\n                          repeat: Infinity,\r\n                          ease: \"easeInOut\"\r\n                        }\r\n                      }}\r\n                      whileHover={{\r\n                        scale: 1.1,\r\n                        rotate: [0, 2, -2, 0],\r\n                        transition: { duration: 0.3 }\r\n                      }}\r\n                      style={{\r\n                        color: '#059669',\r\n                        fontWeight: '900',\r\n                        textShadow: '0 0 10px rgba(16, 185, 129, 0.5)'\r\n                      }}\r\n                    >\r\n                      wave\r\n\r\n                      {/* Wave particle */}\r\n                      <motion.div\r\n                        className=\"absolute top-0 left-0 w-1.5 h-1.5 rounded-full\"\r\n                        animate={{\r\n                          opacity: [0, 1, 0],\r\n                          x: [0, 40, 80],\r\n                          y: [0, -5, 0, 5, 0],\r\n                          backgroundColor: ['#10b981', '#34d399', '#10b981']\r\n                        }}\r\n                        transition={{\r\n                          duration: 3,\r\n                          repeat: Infinity,\r\n                          delay: 1\r\n                        }}\r\n                        style={{\r\n                          backgroundColor: '#10b981',\r\n                          boxShadow: '0 0 8px #10b981'\r\n                        }}\r\n                      />\r\n                    </motion.span>\r\n                  </h1>\r\n\r\n                  {/* Glowing underline effect */}\r\n                  <motion.div\r\n                    className=\"absolute -bottom-1 left-0 h-1 rounded-full\"\r\n                    initial={{ width: 0, opacity: 0 }}\r\n                    animate={{\r\n                      width: '100%',\r\n                      opacity: 1,\r\n                      boxShadow: [\r\n                        '0 0 10px rgba(16, 185, 129, 0.5)',\r\n                        '0 0 20px rgba(59, 130, 246, 0.8)',\r\n                        '0 0 10px rgba(16, 185, 129, 0.5)'\r\n                      ]\r\n                    }}\r\n                    transition={{\r\n                      duration: 1.5,\r\n                      delay: 1.2,\r\n                      boxShadow: {\r\n                        duration: 2,\r\n                        repeat: Infinity,\r\n                        ease: \"easeInOut\"\r\n                      }\r\n                    }}\r\n                    style={{\r\n                      background: 'linear-gradient(90deg, #3b82f6, #10b981, #3b82f6)',\r\n                      boxShadow: '0 0 15px rgba(16, 185, 129, 0.6)'\r\n                    }}\r\n                  />\r\n                </div>\r\n\r\n                {/* Official Logo - Small like profile */}\r\n                <div\r\n                  className=\"rounded-full overflow-hidden border-2 border-white/20 relative\"\r\n                  style={{\r\n                    background: '#f0f0f0',\r\n                    boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\r\n                    width: '32px',\r\n                    height: '32px'\r\n                  }}\r\n                >\r\n                  <img\r\n                    src=\"/favicon.png\"\r\n                    alt=\"Brainwave Logo\"\r\n                    className=\"w-full h-full object-cover\"\r\n                    style={{ objectFit: 'cover' }}\r\n                    onError={(e) => {\r\n                      e.target.style.display = 'none';\r\n                      e.target.nextSibling.style.display = 'flex';\r\n                    }}\r\n                  />\r\n                  <div\r\n                    className=\"w-full h-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold\"\r\n                    style={{\r\n                      display: 'none',\r\n                      fontSize: '12px'\r\n                    }}\r\n                  >\r\n                    🧠\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Modern Glow Effect */}\r\n                <div className=\"absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-gray-900/5 to-blue-500/5 blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 -z-10 scale-110\"></div>\r\n              </motion.div>\r\n            </div>\r\n\r\n            {/* Right Section - Contact Us + Notifications + User Profile */}\r\n            <div className=\"flex items-center justify-end space-x-2 sm:space-x-3\">\r\n              {/* Contact Us Button */}\r\n              <div className=\"hidden md:flex items-center space-x-4 lg:space-x-6\">\r\n                <button onClick={() => scrollToSection(contactUsRef)} className=\"nav-item text-sm md:text-base\">Contact Us</button>\r\n              </div>\r\n\r\n              {/* Notification Bell */}\r\n              {user && !user?.isAdmin && (\r\n                <motion.div\r\n                  initial={{ opacity: 0, scale: 0.8 }}\r\n                  animate={{ opacity: 1, scale: 1 }}\r\n                  transition={{ duration: 0.5, delay: 0.2 }}\r\n                >\r\n                  <NotificationBell />\r\n                </motion.div>\r\n              )}\r\n\r\n              {/* User Profile Section */}\r\n              {user && (\r\n                <motion.div\r\n                  initial={{ opacity: 0, scale: 0.8 }}\r\n                  animate={{ opacity: 1, scale: 1 }}\r\n                  transition={{ duration: 0.5, delay: 0.3 }}\r\n                  className=\"flex items-center space-x-2 group\"\r\n                >\r\n                  {/* Profile Picture with Online Status */}\r\n                  <ProfilePicture\r\n                    user={user}\r\n                    size=\"sm\"\r\n                    showOnlineStatus={true}\r\n                    style={{\r\n                      width: '32px',\r\n                      height: '32px'\r\n                    }}\r\n                  />\r\n\r\n                  {/* User Name and Class */}\r\n                  <div className=\"hidden sm:block text-right\">\r\n                    <div className=\"text-xs md:text-sm font-medium text-gray-700 group-hover:text-green-600 transition-colors duration-300\">\r\n                      {user?.name || 'User'}\r\n                    </div>\r\n                    <div className=\"text-xs text-gray-500 group-hover:text-green-500 transition-colors duration-300\">\r\n                      Class {user?.class}\r\n                    </div>\r\n                  </div>\r\n                </motion.div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </motion.header>\r\n\r\n      {/* Enhanced Animated Educational Elements - Bigger & Seamless */}\r\n      <div className=\"relative overflow-hidden bg-gradient-to-br from-blue-100 via-indigo-50 to-purple-100 py-12 min-h-[200px] z-10\">\r\n        {/* Background Pattern */}\r\n        <div className=\"absolute inset-0 opacity-10\">\r\n          <div className=\"absolute inset-0\" style={{\r\n            backgroundImage: `radial-gradient(circle at 25% 25%, #3b82f6 2px, transparent 2px),\r\n                             radial-gradient(circle at 75% 75%, #8b5cf6 2px, transparent 2px)`,\r\n            backgroundSize: '50px 50px'\r\n          }}></div>\r\n        </div>\r\n\r\n        <div className=\"absolute inset-0 overflow-hidden\">\r\n          {/* Large Floating Educational Icons - Enhanced */}\r\n          <motion.div\r\n            animate={{\r\n              y: [0, -35, 0],\r\n              rotate: [0, 12, 0],\r\n              scale: [1, 1.3, 1]\r\n            }}\r\n            transition={{\r\n              duration: 4,\r\n              repeat: Infinity,\r\n              ease: \"easeInOut\"\r\n            }}\r\n            className=\"absolute top-8 left-1/6 text-blue-600 opacity-90 drop-shadow-2xl\"\r\n          >\r\n            <div className=\"relative\">\r\n              <TbBook className=\"w-20 h-20\" />\r\n              <div className=\"absolute inset-0 bg-blue-200 rounded-full blur-xl opacity-30 scale-150\"></div>\r\n            </div>\r\n          </motion.div>\r\n\r\n          <motion.div\r\n            animate={{\r\n              y: [0, -40, 0],\r\n              rotate: [0, -8, 0],\r\n              scale: [1, 1.4, 1]\r\n            }}\r\n            transition={{\r\n              duration: 3.5,\r\n              repeat: Infinity,\r\n              ease: \"easeInOut\",\r\n              delay: 0.5\r\n            }}\r\n            className=\"absolute top-4 right-1/5 text-green-600 opacity-95 drop-shadow-2xl\"\r\n          >\r\n            <div className=\"relative\">\r\n              <TbBrain className=\"w-24 h-24\" />\r\n              <div className=\"absolute inset-0 bg-green-200 rounded-full blur-xl opacity-30 scale-150\"></div>\r\n            </div>\r\n          </motion.div>\r\n\r\n          <motion.div\r\n            animate={{\r\n              y: [0, -45, 0],\r\n              rotate: [0, 15, 0],\r\n              scale: [1, 1.5, 1]\r\n            }}\r\n            transition={{\r\n              duration: 4.5,\r\n              repeat: Infinity,\r\n              ease: \"easeInOut\",\r\n              delay: 1\r\n            }}\r\n            className=\"absolute top-12 right-1/3 text-purple-600 opacity-90 drop-shadow-2xl\"\r\n          >\r\n            <div className=\"relative\">\r\n              <TbTrophy className=\"w-18 h-18\" />\r\n              <div className=\"absolute inset-0 bg-purple-200 rounded-full blur-xl opacity-30 scale-150\"></div>\r\n            </div>\r\n          </motion.div>\r\n\r\n          <motion.div\r\n            animate={{\r\n              y: [0, -32, 0],\r\n              rotate: [0, -12, 0],\r\n              scale: [1, 1.2, 1]\r\n            }}\r\n            transition={{\r\n              duration: 3.8,\r\n              repeat: Infinity,\r\n              ease: \"easeInOut\",\r\n              delay: 1.5\r\n            }}\r\n            className=\"absolute top-16 left-1/3 text-orange-500 opacity-90 drop-shadow-2xl\"\r\n          >\r\n            <div className=\"relative\">\r\n              <TbStar className=\"w-16 h-16\" />\r\n              <div className=\"absolute inset-0 bg-orange-200 rounded-full blur-xl opacity-30 scale-150\"></div>\r\n            </div>\r\n          </motion.div>\r\n\r\n          <motion.div\r\n            animate={{\r\n              y: [0, -38, 0],\r\n              rotate: [0, 10, 0],\r\n              scale: [1, 1.6, 1]\r\n            }}\r\n            transition={{\r\n              duration: 4.2,\r\n              repeat: Infinity,\r\n              ease: \"easeInOut\",\r\n              delay: 2\r\n            }}\r\n            className=\"absolute top-6 left-1/2 text-indigo-600 opacity-95 drop-shadow-2xl\"\r\n          >\r\n            <div className=\"relative\">\r\n              <TbRocket className=\"w-22 h-22\" />\r\n              <div className=\"absolute inset-0 bg-indigo-200 rounded-full blur-xl opacity-30 scale-150\"></div>\r\n            </div>\r\n          </motion.div>\r\n\r\n          {/* Additional Large Floating Elements */}\r\n          <motion.div\r\n            animate={{\r\n              y: [0, -25, 0],\r\n              rotate: [0, -6, 0],\r\n              scale: [1, 1.3, 1]\r\n            }}\r\n            transition={{\r\n              duration: 5,\r\n              repeat: Infinity,\r\n              ease: \"easeInOut\",\r\n              delay: 2.5\r\n            }}\r\n            className=\"absolute top-10 left-1/12 text-teal-500 opacity-85 drop-shadow-2xl\"\r\n          >\r\n            <div className=\"relative\">\r\n              <TbUsers className=\"w-16 h-16\" />\r\n              <div className=\"absolute inset-0 bg-teal-200 rounded-full blur-xl opacity-30 scale-150\"></div>\r\n            </div>\r\n          </motion.div>\r\n\r\n          {/* Enhanced Sparkle Effects */}\r\n          <motion.div\r\n            animate={{\r\n              scale: [0, 1.5, 0],\r\n              opacity: [0, 1, 0],\r\n              rotate: [0, 180, 360]\r\n            }}\r\n            transition={{\r\n              duration: 3,\r\n              repeat: Infinity,\r\n              ease: \"easeInOut\",\r\n              delay: 0\r\n            }}\r\n            className=\"absolute top-4 left-1/8 text-yellow-400 drop-shadow-lg\"\r\n          >\r\n            <TbStar className=\"w-8 h-8\" />\r\n          </motion.div>\r\n\r\n          <motion.div\r\n            animate={{\r\n              scale: [0, 1.2, 0],\r\n              opacity: [0, 1, 0],\r\n              rotate: [0, -180, -360]\r\n            }}\r\n            transition={{\r\n              duration: 2.5,\r\n              repeat: Infinity,\r\n              ease: \"easeInOut\",\r\n              delay: 1\r\n            }}\r\n            className=\"absolute top-14 right-1/8 text-yellow-400 drop-shadow-lg\"\r\n          >\r\n            <TbStar className=\"w-6 h-6\" />\r\n          </motion.div>\r\n\r\n          <motion.div\r\n            animate={{\r\n              scale: [0, 1.3, 0],\r\n              opacity: [0, 1, 0],\r\n              rotate: [0, 90, 180]\r\n            }}\r\n            transition={{\r\n              duration: 2.8,\r\n              repeat: Infinity,\r\n              ease: \"easeInOut\",\r\n              delay: 2\r\n            }}\r\n            className=\"absolute top-2 left-3/4 text-yellow-400 drop-shadow-lg\"\r\n          >\r\n            <TbStar className=\"w-7 h-7\" />\r\n          </motion.div>\r\n\r\n          {/* Floating Particles */}\r\n          <motion.div\r\n            animate={{\r\n              y: [0, -100, 0],\r\n              x: [0, 20, 0],\r\n              opacity: [0, 0.6, 0]\r\n            }}\r\n            transition={{\r\n              duration: 6,\r\n              repeat: Infinity,\r\n              ease: \"easeInOut\",\r\n              delay: 0\r\n            }}\r\n            className=\"absolute bottom-0 left-1/4 w-2 h-2 bg-blue-400 rounded-full\"\r\n          ></motion.div>\r\n\r\n          <motion.div\r\n            animate={{\r\n              y: [0, -120, 0],\r\n              x: [0, -15, 0],\r\n              opacity: [0, 0.8, 0]\r\n            }}\r\n            transition={{\r\n              duration: 7,\r\n              repeat: Infinity,\r\n              ease: \"easeInOut\",\r\n              delay: 1.5\r\n            }}\r\n            className=\"absolute bottom-0 right-1/3 w-3 h-3 bg-purple-400 rounded-full\"\r\n          ></motion.div>\r\n\r\n          <motion.div\r\n            animate={{\r\n              y: [0, -90, 0],\r\n              x: [0, 10, 0],\r\n              opacity: [0, 0.7, 0]\r\n            }}\r\n            transition={{\r\n              duration: 5.5,\r\n              repeat: Infinity,\r\n              ease: \"easeInOut\",\r\n              delay: 3\r\n            }}\r\n            className=\"absolute bottom-0 left-2/3 w-2 h-2 bg-green-400 rounded-full\"\r\n          ></motion.div>\r\n        </div>\r\n\r\n        {/* Enhanced Seamless Wave Animation */}\r\n        <div className=\"absolute bottom-0 left-0 w-full\">\r\n          <svg\r\n            className=\"relative block w-full h-16\"\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            viewBox=\"0 0 1200 120\"\r\n            preserveAspectRatio=\"none\"\r\n          >\r\n            {/* Primary Wave */}\r\n            <motion.path\r\n              d=\"M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z\"\r\n              fill=\"rgba(59, 130, 246, 0.4)\"\r\n              animate={{\r\n                d: [\r\n                  \"M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z\",\r\n                  \"M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z\",\r\n                  \"M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z\"\r\n                ]\r\n              }}\r\n              transition={{\r\n                duration: 5,\r\n                repeat: Infinity,\r\n                ease: \"easeInOut\"\r\n              }}\r\n            />\r\n            {/* Secondary Wave */}\r\n            <motion.path\r\n              d=\"M0,0V35c35,18,70,30,105,25c45-7,90-25,135-28c55-4,110,12,165,18c50,5,100,3,150-6c40-6,80-18,120-12c30,4,60,15,90,10c25-4,50-15,75-10V0Z\"\r\n              fill=\"rgba(139, 92, 246, 0.3)\"\r\n              animate={{\r\n                d: [\r\n                  \"M0,0V35c35,18,70,30,105,25c45-7,90-25,135-28c55-4,110,12,165,18c50,5,100,3,150-6c40-6,80-18,120-12c30,4,60,15,90,10c25-4,50-15,75-10V0Z\",\r\n                  \"M0,0V28c28,14,62,35,96,30c40-5,85-30,130-25c50,6,105,18,160,12c45-5,95-10,145,3c35,8,75,22,115,15c25-4,52-18,80-6c18,7,40,22,65,18V0Z\",\r\n                  \"M0,0V35c35,18,70,30,105,25c45-7,90-25,135-28c55-4,110,12,165,18c50,5,100,3,150-6c40-6,80-18,120-12c30,4,60,15,90,10c25-4,50-15,75-10V0Z\"\r\n                ]\r\n              }}\r\n              transition={{\r\n                duration: 7,\r\n                repeat: Infinity,\r\n                ease: \"easeInOut\",\r\n                delay: 1\r\n              }}\r\n            />\r\n            {/* Third Wave for more depth */}\r\n            <motion.path\r\n              d=\"M0,0V20c20,10,45,20,70,18c30-3,60-15,90-16c35-2,70,8,105,12c30,3,60,2,90-4c25-4,50-12,75-8c20,3,40,10,60,7c15-2,30-8,45-6V0Z\"\r\n              fill=\"rgba(16, 185, 129, 0.2)\"\r\n              animate={{\r\n                d: [\r\n                  \"M0,0V20c20,10,45,20,70,18c30-3,60-15,90-16c35-2,70,8,105,12c30,3,60,2,90-4c25-4,50-12,75-8c20,3,40,10,60,7c15-2,30-8,45-6V0Z\",\r\n                  \"M0,0V15c18,8,42,25,68,22c28-3,58-20,88-18c32,2,68,12,102,8c28-3,58-6,88,2c22,5,48,14,72,10c18-3,38-12,58-4c12,5,28,15,48,12V0Z\",\r\n                  \"M0,0V20c20,10,45,20,70,18c30-3,60-15,90-16c35-2,70,8,105,12c30,3,60,2,90-4c25-4,50-12,75-8c20,3,40,10,60,7c15-2,30-8,45-6V0Z\"\r\n                ]\r\n              }}\r\n              transition={{\r\n                duration: 9,\r\n                repeat: Infinity,\r\n                ease: \"easeInOut\",\r\n                delay: 2\r\n              }}\r\n            />\r\n          </svg>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Hero Section */}\r\n      <section ref={homeSectionRef} className=\"hero-section\">\r\n        <div className=\"container\">\r\n          <div className=\"hero-grid\">\r\n            {/* Hero Content */}\r\n            <motion.div\r\n              initial={{ opacity: 0, x: -50 }}\r\n              animate={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.6 }}\r\n              className=\"hero-content\"\r\n            >\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.2 }}\r\n                className=\"hero-badge\"\r\n              >\r\n                <TbSchool className=\"w-4 h-4 sm:w-5 sm:h-5 mr-2\" />\r\n                <span className=\"text-xs sm:text-sm\">#1 Educational Platform in Tanzania</span>\r\n              </motion.div>\r\n\r\n              <h1 className=\"hero-title\">\r\n                Fueling Bright Futures with{\" \"}\r\n                <span className=\"text-gradient\">\r\n                  Education\r\n                  <TbArrowBigRightLinesFilled className=\"inline w-8 h-8 ml-2\" />\r\n                </span>\r\n              </h1>\r\n\r\n              <p className=\"hero-subtitle\">\r\n                Discover limitless learning opportunities with our comprehensive\r\n                online study platform. Study anywhere, anytime, and achieve your\r\n                academic goals with confidence.\r\n              </p>\r\n\r\n              {/* CTA Buttons */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.4 }}\r\n                className=\"cta-section\"\r\n              >\r\n                {!user ? (\r\n                  <div className=\"flex flex-col sm:flex-row items-center justify-center gap-4\">\r\n                    {/* Try for Free Button */}\r\n                    <motion.button\r\n                      onClick={handleTryForFree}\r\n                      className=\"btn btn-primary btn-large bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 border-0 shadow-lg hover:shadow-xl\"\r\n                      whileHover={{ scale: 1.05 }}\r\n                      whileTap={{ scale: 0.95 }}\r\n                    >\r\n                      <TbRocket className=\"w-5 h-5 mr-2\" />\r\n                      Try for Free\r\n                    </motion.button>\r\n\r\n                    {/* Register Now Button */}\r\n                    <Link to=\"/register\">\r\n                      <motion.button\r\n                        className=\"btn btn-primary btn-large bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 border-0 shadow-lg hover:shadow-xl\"\r\n                        whileHover={{ scale: 1.05 }}\r\n                        whileTap={{ scale: 0.95 }}\r\n                      >\r\n                        <TbUserPlus className=\"w-5 h-5 mr-2\" />\r\n                        Register Now\r\n                      </motion.button>\r\n                    </Link>\r\n\r\n                    {/* Login Now Button */}\r\n                    <Link to=\"/login\">\r\n                      <motion.button\r\n                        className=\"btn btn-outline btn-large border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white transition-all duration-300\"\r\n                        whileHover={{ scale: 1.05 }}\r\n                        whileTap={{ scale: 0.95 }}\r\n                      >\r\n                        <TbLogin className=\"w-5 h-5 mr-2\" />\r\n                        Login Now\r\n                      </motion.button>\r\n                    </Link>\r\n                  </div>\r\n                ) : (\r\n                  <Link to=\"/dashboard\">\r\n                    <motion.button\r\n                      className=\"btn btn-primary btn-large bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 border-0 shadow-lg hover:shadow-xl\"\r\n                      whileHover={{ scale: 1.05 }}\r\n                      whileTap={{ scale: 0.95 }}\r\n                    >\r\n                      <TbBrain className=\"w-5 h-5 mr-2\" />\r\n                      Go to Dashboard\r\n                    </motion.button>\r\n                  </Link>\r\n                )}\r\n              </motion.div>\r\n\r\n\r\n\r\n              {/* Trust Indicators */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.6 }}\r\n                className=\"trust-indicators\"\r\n              >\r\n                <div className=\"trust-indicator\">\r\n                  <TbUsers style={{color: '#007BFF'}} />\r\n                  <span>15K+ Students</span>\r\n                </div>\r\n                <div className=\"trust-indicator\">\r\n                  <TbStar style={{color: '#f59e0b'}} />\r\n                  <span>4.9/5 Rating</span>\r\n                </div>\r\n                <div className=\"trust-indicator\">\r\n                  <TbTrophy style={{color: '#007BFF'}} />\r\n                  <span>Award Winning</span>\r\n                </div>\r\n              </motion.div>\r\n            </motion.div>\r\n\r\n            {/* Hero Image */}\r\n            <motion.div\r\n              initial={{ opacity: 0, x: 50 }}\r\n              animate={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.6, delay: 0.3 }}\r\n              className=\"hero-image\"\r\n            >\r\n              <div className=\"relative\">\r\n                <img\r\n                  src={Image1}\r\n                  alt=\"Students Learning\"\r\n                  loading=\"lazy\"\r\n                />\r\n\r\n                {/* Floating Elements */}\r\n                <motion.div\r\n                  animate={{ y: [-10, 10, -10] }}\r\n                  transition={{ duration: 4, repeat: Infinity }}\r\n                  className=\"floating-element\"\r\n                  style={{top: '-1rem', left: '-1rem'}}\r\n                >\r\n                  <TbBook style={{color: '#007BFF'}} />\r\n                </motion.div>\r\n\r\n                <motion.div\r\n                  animate={{ y: [10, -10, 10] }}\r\n                  transition={{ duration: 3, repeat: Infinity }}\r\n                  className=\"floating-element\"\r\n                  style={{bottom: '-1rem', right: '-1rem'}}\r\n                >\r\n                  <TbTrophy style={{color: '#f59e0b'}} />\r\n                </motion.div>\r\n              </div>\r\n            </motion.div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Stats Section */}\r\n      <section className=\"py-16 sm:py-20 bg-gradient-to-br from-blue-50 via-white to-purple-50\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 50 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"grid grid-cols-2 md:grid-cols-4 gap-6 sm:gap-8\"\r\n          >\r\n            {[\r\n              { number: \"15K+\", text: \"Active Students\", icon: TbUsers, color: \"from-blue-500 to-blue-600\" },\r\n              { number: \"500+\", text: \"Expert Teachers\", icon: TbSchool, color: \"from-green-500 to-green-600\" },\r\n              { number: \"1000+\", text: \"Video Lessons\", icon: TbBook, color: \"from-purple-500 to-purple-600\" },\r\n              { number: \"98%\", text: \"Success Rate\", icon: TbTrophy, color: \"from-orange-500 to-orange-600\" }\r\n            ].map((stat, index) => (\r\n              <motion.div\r\n                key={index}\r\n                initial={{ opacity: 0, y: 30, scale: 0.9 }}\r\n                whileInView={{ opacity: 1, y: 0, scale: 1 }}\r\n                transition={{ duration: 0.5, delay: index * 0.1 }}\r\n                viewport={{ once: true }}\r\n                whileHover={{ scale: 1.05, y: -5 }}\r\n                className=\"bg-white rounded-2xl p-4 sm:p-6 shadow-lg hover:shadow-xl transition-all duration-300 text-center group border border-gray-100\"\r\n              >\r\n                <div className={`w-12 h-12 sm:w-16 sm:h-16 mx-auto mb-3 sm:mb-4 rounded-full bg-gradient-to-r ${stat.color} flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>\r\n                  <stat.icon className=\"w-6 h-6 sm:w-8 sm:h-8 text-white\" />\r\n                </div>\r\n                <div className=\"text-2xl sm:text-3xl md:text-4xl font-bold text-gray-800 mb-1 sm:mb-2\">{stat.number}</div>\r\n                <div className=\"text-xs sm:text-sm md:text-base text-gray-600 font-medium\">{stat.text}</div>\r\n              </motion.div>\r\n            ))}\r\n          </motion.div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Reviews Section */}\r\n      <section ref={reviewsSectionRef} className=\"reviews-section\">\r\n        <div className=\"reviews-container\">\r\n          <h2 className=\"reviews-title\">\r\n            Reviews from our students\r\n          </h2>\r\n          <div className=\"reviews-grid\">\r\n            {[\r\n              {\r\n                rating: 5,\r\n                text: \"BrainWave has completely transformed my learning experience. The interactive lessons and expert guidance helped me excel in my studies.\",\r\n                user: { name: \"Sarah Johnson\" }\r\n              },\r\n              {\r\n                rating: 5,\r\n                text: \"The platform is incredibly user-friendly and the content quality is outstanding. I've improved my grades significantly since joining.\",\r\n                user: { name: \"Michael Chen\" }\r\n              },\r\n              {\r\n                rating: 5,\r\n                text: \"Amazing platform with excellent teachers. The video lessons are clear and easy to understand. Highly recommended!\",\r\n                user: { name: \"Amina Hassan\" }\r\n              }\r\n            ].map((review, index) => (\r\n              <motion.div\r\n                key={index}\r\n                initial={{ opacity: 0, y: 30 }}\r\n                whileInView={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: index * 0.1 }}\r\n                viewport={{ once: true }}\r\n                className=\"review-card\"\r\n              >\r\n                <div className=\"review-rating\">\r\n                  <div style={{ color: '#f59e0b', fontSize: '1.25rem' }}>\r\n                    {'★'.repeat(review.rating)}\r\n                  </div>\r\n                </div>\r\n                <div className=\"review-text\">\"{review.text}\"</div>\r\n                <div className=\"review-divider\"></div>\r\n                <div className=\"review-author\">{review.user?.name}</div>\r\n              </motion.div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Contact Section */}\r\n      <section ref={contactUsRef} className=\"py-16 sm:py-20 bg-gradient-to-br from-gray-50 to-blue-50\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"text-center mb-12\">\r\n            <motion.h2\r\n              initial={{ opacity: 0, y: 20 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6 }}\r\n              viewport={{ once: true }}\r\n              className=\"text-3xl sm:text-4xl font-bold text-gray-800 mb-4\"\r\n            >\r\n              Contact Us\r\n            </motion.h2>\r\n            <motion.p\r\n              initial={{ opacity: 0, y: 20 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6, delay: 0.1 }}\r\n              viewport={{ once: true }}\r\n              className=\"text-lg text-gray-600 max-w-2xl mx-auto\"\r\n            >\r\n              Get in touch with us for any questions or support. We're here to help you succeed!\r\n            </motion.p>\r\n          </div>\r\n\r\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12\">\r\n            {/* Contact Form */}\r\n            <motion.div\r\n              initial={{ opacity: 0, x: -30 }}\r\n              whileInView={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.6 }}\r\n              viewport={{ once: true }}\r\n              className=\"bg-white rounded-2xl shadow-xl p-6 sm:p-8\"\r\n            >\r\n              <div className=\"flex items-center justify-between mb-6\">\r\n                <h3 className=\"text-2xl font-bold text-gray-800\">Send us a Message</h3>\r\n                <div className=\"text-sm text-gray-500\">or</div>\r\n                <a\r\n                  href=\"https://wa.me/25565528549?text=Hello! I'm interested in learning more about BrainWave Educational Platform.\"\r\n                  target=\"_blank\"\r\n                  rel=\"noopener noreferrer\"\r\n                  className=\"inline-flex items-center space-x-2 bg-green-600 text-white px-4 py-2 rounded-lg font-bold hover:bg-green-700 hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl border-2 border-green-500 hover:border-green-600\"\r\n                >\r\n                  <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path d=\"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.346\"/>\r\n                  </svg>\r\n                  <span className=\"hidden sm:inline\">WhatsApp</span>\r\n                </a>\r\n              </div>\r\n              <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Name</label>\r\n                  <input\r\n                    type=\"text\"\r\n                    name=\"name\"\r\n                    placeholder=\"Your Full Name\"\r\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all\"\r\n                    value={formData.name}\r\n                    onChange={handleChange}\r\n                    required\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Email</label>\r\n                  <input\r\n                    type=\"email\"\r\n                    name=\"email\"\r\n                    placeholder=\"<EMAIL>\"\r\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all\"\r\n                    value={formData.email}\r\n                    onChange={handleChange}\r\n                    required\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Message</label>\r\n                  <textarea\r\n                    name=\"message\"\r\n                    placeholder=\"Tell us how we can help you...\"\r\n                    rows=\"5\"\r\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all resize-none\"\r\n                    value={formData.message}\r\n                    onChange={handleChange}\r\n                    required\r\n                  ></textarea>\r\n                </div>\r\n                <button\r\n                  type=\"submit\"\r\n                  disabled={loading}\r\n                  className=\"w-full py-3 px-6 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-medium rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-300 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                >\r\n                  {loading ? \"Sending...\" : \"Send Message\"}\r\n                </button>\r\n                {responseMessage && (\r\n                  <p className=\"text-center text-green-600 font-medium\">\r\n                    {responseMessage}\r\n                  </p>\r\n                )}\r\n              </form>\r\n            </motion.div>\r\n\r\n            {/* Contact Information */}\r\n            <motion.div\r\n              initial={{ opacity: 0, x: 30 }}\r\n              whileInView={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.6 }}\r\n              viewport={{ once: true }}\r\n              className=\"space-y-8\"\r\n            >\r\n              {/* Contact Methods */}\r\n              <div className=\"bg-white rounded-2xl shadow-xl p-6 sm:p-8\">\r\n                <h3 className=\"text-2xl font-bold text-gray-800 mb-6\">Other Ways to Reach Us</h3>\r\n                <div className=\"space-y-4\">\r\n                  <div className=\"flex items-center space-x-4\">\r\n                    <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\">\r\n                      <svg className=\"w-6 h-6 text-green-600\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path d=\"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.346\"/>\r\n                      </svg>\r\n                    </div>\r\n                    <div>\r\n                      <p className=\"font-medium text-gray-800\">WhatsApp Support</p>\r\n                      <p className=\"text-gray-600\">+255 655 285 49</p>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"flex items-center space-x-4\">\r\n                    <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\">\r\n                      <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"/>\r\n                      </svg>\r\n                    </div>\r\n                    <div>\r\n                      <p className=\"font-medium text-gray-800\">Email Support</p>\r\n                      <p className=\"text-gray-600\"><EMAIL></p>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"flex items-center space-x-4\">\r\n                    <div className=\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center\">\r\n                      <svg className=\"w-6 h-6 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"/>\r\n                      </svg>\r\n                    </div>\r\n                    <div>\r\n                      <p className=\"font-medium text-gray-800\">Response Time</p>\r\n                      <p className=\"text-gray-600\">Usually within 2 hours</p>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"flex items-center space-x-4\">\r\n                    <div className=\"w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center\">\r\n                      <svg className=\"w-6 h-6 text-indigo-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"/>\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"/>\r\n                      </svg>\r\n                    </div>\r\n                    <div>\r\n                      <p className=\"font-medium text-gray-800\">Location</p>\r\n                      <p className=\"text-gray-600\">Dar es Salaam, Tanzania</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </motion.div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Footer */}\r\n      <footer className=\"footer\">\r\n        <div className=\"footer-content\">\r\n          <p className=\"footer-text\">\r\n            © 2024 BrainWave Educational Platform. All rights reserved.\r\n          </p>\r\n        </div>\r\n      </footer>\r\n\r\n      {/* Try for Free Modal */}\r\n      <TryForFreeModal\r\n        key={showTryForFreeModal ? 'open' : 'closed'}\r\n        isOpen={showTryForFreeModal}\r\n        onClose={() => setShowTryForFreeModal(false)}\r\n        onSubmit={handleTryForFreeSubmit}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Home;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,OAAO,aAAa;AACpB,SAASC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,0BAA0B,EAC1BC,OAAO,EACPC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,MAAM,EACNC,QAAQ,EACRC,QAAQ,EACRC,UAAU,EACVC,OAAO,QACF,gBAAgB;AACvB,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,WAAW,QAAQ,aAAa;AACzC,OAAOC,MAAM,MAAM,+BAA+B;AAClD,SAASC,SAAS,QAAQ,yBAAyB;AACnD,OAAOC,gBAAgB,MAAM,6CAA6C;AAC1E,OAAOC,cAAc,MAAM,2CAA2C;AACtE,OAAOC,eAAe,MAAM,4CAA4C;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzE,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAMC,cAAc,GAAG3B,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM4B,iBAAiB,GAAG5B,MAAM,CAAC,IAAI,CAAC;EACtC,MAAM6B,YAAY,GAAG7B,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM,CAAC8B,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAAC;IAAEiC,IAAI,EAAE,EAAE;IAAEC,KAAK,EAAE,EAAE;IAAEjB,OAAO,EAAE;EAAG,CAAC,CAAC;EAC9E,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACqC,eAAe,EAAEC,kBAAkB,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACuC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM;IAAEyC;EAAK,CAAC,GAAGvB,WAAW,CAAEwB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAME,QAAQ,GAAGtC,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMuC,gBAAgB,GAAGA,CAAA,KAAM;IAC7BJ,sBAAsB,CAAC,IAAI,CAAC;EAC9B,CAAC;EAED,MAAMK,sBAAsB,GAAIC,SAAS,IAAK;IAC5C;IACAH,QAAQ,CAAC,QAAQ,EAAE;MAAED,KAAK,EAAE;QAAEK,aAAa,EAAED;MAAU;IAAE,CAAC,CAAC;IAC3DN,sBAAsB,CAAC,KAAK,CAAC;EAC/B,CAAC;EAID,MAAMQ,eAAe,GAAGA,CAACC,GAAG,EAAEC,MAAM,GAAG,EAAE,KAAK;IAC5C,IAAID,GAAG,aAAHA,GAAG,eAAHA,GAAG,CAAEE,OAAO,EAAE;MAChB,MAAMC,UAAU,GAAGH,GAAG,CAACE,OAAO,CAACE,SAAS;MACxCC,MAAM,CAACC,QAAQ,CAAC;QAAEC,GAAG,EAAEJ,UAAU,GAAGF,MAAM;QAAEO,QAAQ,EAAE;MAAS,CAAC,CAAC;IACnE;EACF,CAAC;EAED,MAAMC,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAE1B,IAAI;MAAE2B;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChC7B,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAE,CAACE,IAAI,GAAG2B;IAAM,CAAC,CAAC;EAC7C,CAAC;EAED,MAAME,YAAY,GAAG,MAAOH,CAAC,IAAK;IAChCA,CAAC,CAACI,cAAc,CAAC,CAAC;IAClB3B,UAAU,CAAC,IAAI,CAAC;IAChBE,kBAAkB,CAAC,EAAE,CAAC;IACtB,IAAI;MACF,MAAM0B,IAAI,GAAG,MAAM5C,SAAS,CAACW,QAAQ,CAAC;MACtC,IAAIiC,IAAI,CAACC,OAAO,EAAE;QAChBhD,OAAO,CAACgD,OAAO,CAAC,4BAA4B,CAAC;QAC7C3B,kBAAkB,CAAC,4BAA4B,CAAC;QAChDN,WAAW,CAAC;UAAEC,IAAI,EAAE,EAAE;UAAEC,KAAK,EAAE,EAAE;UAAEjB,OAAO,EAAE;QAAG,CAAC,CAAC;MACnD,CAAC,MAAM;QACLqB,kBAAkB,CAAC0B,IAAI,CAAC/C,OAAO,IAAI,uBAAuB,CAAC;MAC7D;IACF,CAAC,CAAC,OAAOiD,KAAK,EAAE;MACd5B,kBAAkB,CAAC,0CAA0C,CAAC;IAChE;IACAF,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,oBACEX,OAAA;IAAK0C,SAAS,EAAC,MAAM;IAAAC,QAAA,gBAEnB3C,OAAA,CAACnB,MAAM,CAAC+D,MAAM;MACZC,OAAO,EAAE;QAAEC,CAAC,EAAE,CAAC,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MAChCC,OAAO,EAAE;QAAEF,CAAC,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAE,CAAE;MAC9BL,SAAS,EAAC,kKAAkK;MAAAC,QAAA,eAE5K3C,OAAA;QAAK0C,SAAS,EAAC,+CAA+C;QAAAC,QAAA,eAC5D3C,OAAA;UAAK0C,SAAS,EAAC,wEAAwE;UAAAC,QAAA,gBAErF3C,OAAA;YAAK0C,SAAS,EAAC,6BAA6B;YAAAC,QAAA,eAC1C3C,OAAA;cAAK0C,SAAS,EAAC,oDAAoD;cAAAC,QAAA,eACjE3C,OAAA;gBAAQiD,OAAO,EAAEA,CAAA,KAAM1B,eAAe,CAACnB,iBAAiB,CAAE;gBAACsC,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAAC;cAAO;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNrD,OAAA;YAAK0C,SAAS,EAAC,4BAA4B;YAAAC,QAAA,eACzC3C,OAAA,CAACnB,MAAM,CAACyE,GAAG;cACTT,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEQ,KAAK,EAAE;cAAI,CAAE;cACpCP,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEQ,KAAK,EAAE;cAAE,CAAE;cAClCC,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1ChB,SAAS,EAAC,4CAA4C;cAAAC,QAAA,gBAGtD3C,OAAA;gBACE0C,SAAS,EAAC,wEAAwE;gBAClFiB,KAAK,EAAE;kBACLC,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE;gBACV,CAAE;gBAAAlB,QAAA,eAEF3C,OAAA;kBACE8D,GAAG,EAAC,gCAAgC;kBACpCC,GAAG,EAAC,eAAe;kBACnBrB,SAAS,EAAC,4BAA4B;kBACtCiB,KAAK,EAAE;oBAAEK,SAAS,EAAE;kBAAQ,CAAE;kBAC9BC,OAAO,EAAG/B,CAAC,IAAK;oBACd;oBACAA,CAAC,CAACE,MAAM,CAAC0B,GAAG,GAAG,8GAA8G;oBAC7H5B,CAAC,CAACE,MAAM,CAAC8B,OAAO,GAAG,MAAM;sBACvB;sBACAhC,CAAC,CAACE,MAAM,CAACuB,KAAK,CAACQ,OAAO,GAAG,MAAM;sBAC/BjC,CAAC,CAACE,MAAM,CAACgC,aAAa,CAACC,SAAS,GAAG,+GAA+G;oBACpJ,CAAC;kBACH;gBAAE;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGNrD,OAAA;gBAAK0C,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,gBAC3C3C,OAAA;kBAAI0C,SAAS,EAAC,qFAAqF;kBAC/FiB,KAAK,EAAE;oBACLW,UAAU,EAAE,yDAAyD;oBACrEC,aAAa,EAAE;kBACjB,CAAE;kBAAA5B,QAAA,gBAEJ3C,OAAA,CAACnB,MAAM,CAAC2F,IAAI;oBACV9B,SAAS,EAAC,uBAAuB;oBACjCG,OAAO,EAAE;sBAAEE,OAAO,EAAE,CAAC;sBAAE0B,CAAC,EAAE,CAAC,EAAE;sBAAElB,KAAK,EAAE;oBAAI,CAAE;oBAC5CP,OAAO,EAAE;sBACPD,OAAO,EAAE,CAAC;sBACV0B,CAAC,EAAE,CAAC;sBACJlB,KAAK,EAAE,CAAC;sBACRmB,UAAU,EAAE,CACV,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;oBAEtC,CAAE;oBACFlB,UAAU,EAAE;sBACVC,QAAQ,EAAE,CAAC;sBACXC,KAAK,EAAE,GAAG;sBACVgB,UAAU,EAAE;wBACVjB,QAAQ,EAAE,CAAC;wBACXkB,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR;oBACF,CAAE;oBACFC,UAAU,EAAE;sBACVvB,KAAK,EAAE,GAAG;sBACVwB,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;sBACrBvB,UAAU,EAAE;wBAAEC,QAAQ,EAAE;sBAAI;oBAC9B,CAAE;oBACFE,KAAK,EAAE;sBACLqB,KAAK,EAAE,SAAS;sBAChBC,UAAU,EAAE,KAAK;sBACjBP,UAAU,EAAE;oBACd,CAAE;oBAAA/B,QAAA,GACH,OAGC,eACA3C,OAAA,CAACnB,MAAM,CAACyE,GAAG;sBACTZ,SAAS,EAAC,+CAA+C;sBACzDM,OAAO,EAAE;wBACPD,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBAClBQ,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;wBACtB2B,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;sBACnD,CAAE;sBACF1B,UAAU,EAAE;wBACVC,QAAQ,EAAE,GAAG;wBACbkB,MAAM,EAAEC,QAAQ;wBAChBlB,KAAK,EAAE;sBACT,CAAE;sBACFC,KAAK,EAAE;wBACLuB,eAAe,EAAE,SAAS;wBAC1BC,SAAS,EAAE;sBACb;oBAAE;sBAAAjC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS,CAAC,eAGdrD,OAAA,CAACnB,MAAM,CAAC2F,IAAI;oBACV9B,SAAS,EAAC,uBAAuB;oBACjCG,OAAO,EAAE;sBAAEE,OAAO,EAAE,CAAC;sBAAE0B,CAAC,EAAE,EAAE;sBAAElB,KAAK,EAAE;oBAAI,CAAE;oBAC3CP,OAAO,EAAE;sBACPD,OAAO,EAAE,CAAC;sBACV0B,CAAC,EAAE,CAAC;sBACJlB,KAAK,EAAE,CAAC;sBACRT,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;sBACnB4B,UAAU,EAAE,CACV,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;oBAEtC,CAAE;oBACFlB,UAAU,EAAE;sBACVC,QAAQ,EAAE,CAAC;sBACXC,KAAK,EAAE,GAAG;sBACVZ,CAAC,EAAE;wBACDW,QAAQ,EAAE,CAAC;wBACXkB,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR,CAAC;sBACDH,UAAU,EAAE;wBACVjB,QAAQ,EAAE,GAAG;wBACbkB,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR;oBACF,CAAE;oBACFC,UAAU,EAAE;sBACVvB,KAAK,EAAE,GAAG;sBACVwB,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;sBACrBvB,UAAU,EAAE;wBAAEC,QAAQ,EAAE;sBAAI;oBAC9B,CAAE;oBACFE,KAAK,EAAE;sBACLqB,KAAK,EAAE,SAAS;sBAChBC,UAAU,EAAE,KAAK;sBACjBP,UAAU,EAAE;oBACd,CAAE;oBAAA/B,QAAA,GACH,MAGC,eACA3C,OAAA,CAACnB,MAAM,CAACyE,GAAG;sBACTZ,SAAS,EAAC,gDAAgD;sBAC1DM,OAAO,EAAE;wBACPD,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBAClB0B,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;wBACd3B,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBACnBoC,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;sBACnD,CAAE;sBACF1B,UAAU,EAAE;wBACVC,QAAQ,EAAE,CAAC;wBACXkB,MAAM,EAAEC,QAAQ;wBAChBlB,KAAK,EAAE;sBACT,CAAE;sBACFC,KAAK,EAAE;wBACLuB,eAAe,EAAE,SAAS;wBAC1BC,SAAS,EAAE;sBACb;oBAAE;sBAAAjC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eAGLrD,OAAA,CAACnB,MAAM,CAACyE,GAAG;kBACTZ,SAAS,EAAC,4CAA4C;kBACtDG,OAAO,EAAE;oBAAEe,KAAK,EAAE,CAAC;oBAAEb,OAAO,EAAE;kBAAE,CAAE;kBAClCC,OAAO,EAAE;oBACPY,KAAK,EAAE,MAAM;oBACbb,OAAO,EAAE,CAAC;oBACVoC,SAAS,EAAE,CACT,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;kBAEtC,CAAE;kBACF3B,UAAU,EAAE;oBACVC,QAAQ,EAAE,GAAG;oBACbC,KAAK,EAAE,GAAG;oBACVyB,SAAS,EAAE;sBACT1B,QAAQ,EAAE,CAAC;sBACXkB,MAAM,EAAEC,QAAQ;sBAChBC,IAAI,EAAE;oBACR;kBACF,CAAE;kBACFlB,KAAK,EAAE;oBACLyB,UAAU,EAAE,mDAAmD;oBAC/DD,SAAS,EAAE;kBACb;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGNrD,OAAA;gBACE0C,SAAS,EAAC,gEAAgE;gBAC1EiB,KAAK,EAAE;kBACLyB,UAAU,EAAE,SAAS;kBACrBD,SAAS,EAAE,4BAA4B;kBACvCvB,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE;gBACV,CAAE;gBAAAlB,QAAA,gBAEF3C,OAAA;kBACE8D,GAAG,EAAC,cAAc;kBAClBC,GAAG,EAAC,gBAAgB;kBACpBrB,SAAS,EAAC,4BAA4B;kBACtCiB,KAAK,EAAE;oBAAEK,SAAS,EAAE;kBAAQ,CAAE;kBAC9BC,OAAO,EAAG/B,CAAC,IAAK;oBACdA,CAAC,CAACE,MAAM,CAACuB,KAAK,CAACQ,OAAO,GAAG,MAAM;oBAC/BjC,CAAC,CAACE,MAAM,CAACiD,WAAW,CAAC1B,KAAK,CAACQ,OAAO,GAAG,MAAM;kBAC7C;gBAAE;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACFrD,OAAA;kBACE0C,SAAS,EAAC,gHAAgH;kBAC1HiB,KAAK,EAAE;oBACLQ,OAAO,EAAE,MAAM;oBACfmB,QAAQ,EAAE;kBACZ,CAAE;kBAAA3C,QAAA,EACH;gBAED;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNrD,OAAA;gBAAK0C,SAAS,EAAC;cAAyK;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAGNrD,OAAA;YAAK0C,SAAS,EAAC,sDAAsD;YAAAC,QAAA,gBAEnE3C,OAAA;cAAK0C,SAAS,EAAC,oDAAoD;cAAAC,QAAA,eACjE3C,OAAA;gBAAQiD,OAAO,EAAEA,CAAA,KAAM1B,eAAe,CAAClB,YAAY,CAAE;gBAACqC,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAAC;cAAU;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChH,CAAC,EAGLrC,IAAI,IAAI,EAACA,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEuE,OAAO,kBACrBvF,OAAA,CAACnB,MAAM,CAACyE,GAAG;cACTT,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEQ,KAAK,EAAE;cAAI,CAAE;cACpCP,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEQ,KAAK,EAAE;cAAE,CAAE;cAClCC,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAAAf,QAAA,eAE1C3C,OAAA,CAACJ,gBAAgB;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CACb,EAGArC,IAAI,iBACHhB,OAAA,CAACnB,MAAM,CAACyE,GAAG;cACTT,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEQ,KAAK,EAAE;cAAI,CAAE;cACpCP,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEQ,KAAK,EAAE;cAAE,CAAE;cAClCC,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1ChB,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAG7C3C,OAAA,CAACH,cAAc;gBACbmB,IAAI,EAAEA,IAAK;gBACXwE,IAAI,EAAC,IAAI;gBACTC,gBAAgB,EAAE,IAAK;gBACvB9B,KAAK,EAAE;kBACLC,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE;gBACV;cAAE;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGFrD,OAAA;gBAAK0C,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBACzC3C,OAAA;kBAAK0C,SAAS,EAAC,wGAAwG;kBAAAC,QAAA,EACpH,CAAA3B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAER,IAAI,KAAI;gBAAM;kBAAA0C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eACNrD,OAAA;kBAAK0C,SAAS,EAAC,iFAAiF;kBAAAC,QAAA,GAAC,QACzF,EAAC3B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0E,KAAK;gBAAA;kBAAAxC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,eAGhBrD,OAAA;MAAK0C,SAAS,EAAC,+GAA+G;MAAAC,QAAA,gBAE5H3C,OAAA;QAAK0C,SAAS,EAAC,6BAA6B;QAAAC,QAAA,eAC1C3C,OAAA;UAAK0C,SAAS,EAAC,kBAAkB;UAACiB,KAAK,EAAE;YACvCgC,eAAe,EAAG;AAC9B,8FAA8F;YAClFC,cAAc,EAAE;UAClB;QAAE;UAAA1C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENrD,OAAA;QAAK0C,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAE/C3C,OAAA,CAACnB,MAAM,CAACyE,GAAG;UACTN,OAAO,EAAE;YACPF,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;YACdiC,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAClBxB,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;UACnB,CAAE;UACFC,UAAU,EAAE;YACVC,QAAQ,EAAE,CAAC;YACXkB,MAAM,EAAEC,QAAQ;YAChBC,IAAI,EAAE;UACR,CAAE;UACFnC,SAAS,EAAC,kEAAkE;UAAAC,QAAA,eAE5E3C,OAAA;YAAK0C,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvB3C,OAAA,CAAChB,MAAM;cAAC0D,SAAS,EAAC;YAAW;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChCrD,OAAA;cAAK0C,SAAS,EAAC;YAAwE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAEbrD,OAAA,CAACnB,MAAM,CAACyE,GAAG;UACTN,OAAO,EAAE;YACPF,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;YACdiC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;YAClBxB,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;UACnB,CAAE;UACFC,UAAU,EAAE;YACVC,QAAQ,EAAE,GAAG;YACbkB,MAAM,EAAEC,QAAQ;YAChBC,IAAI,EAAE,WAAW;YACjBnB,KAAK,EAAE;UACT,CAAE;UACFhB,SAAS,EAAC,oEAAoE;UAAAC,QAAA,eAE9E3C,OAAA;YAAK0C,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvB3C,OAAA,CAACjB,OAAO;cAAC2D,SAAS,EAAC;YAAW;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjCrD,OAAA;cAAK0C,SAAS,EAAC;YAAyE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5F;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAEbrD,OAAA,CAACnB,MAAM,CAACyE,GAAG;UACTN,OAAO,EAAE;YACPF,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;YACdiC,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAClBxB,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;UACnB,CAAE;UACFC,UAAU,EAAE;YACVC,QAAQ,EAAE,GAAG;YACbkB,MAAM,EAAEC,QAAQ;YAChBC,IAAI,EAAE,WAAW;YACjBnB,KAAK,EAAE;UACT,CAAE;UACFhB,SAAS,EAAC,sEAAsE;UAAAC,QAAA,eAEhF3C,OAAA;YAAK0C,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvB3C,OAAA,CAACf,QAAQ;cAACyD,SAAS,EAAC;YAAW;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClCrD,OAAA;cAAK0C,SAAS,EAAC;YAA0E;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7F;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAEbrD,OAAA,CAACnB,MAAM,CAACyE,GAAG;UACTN,OAAO,EAAE;YACPF,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;YACdiC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;YACnBxB,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;UACnB,CAAE;UACFC,UAAU,EAAE;YACVC,QAAQ,EAAE,GAAG;YACbkB,MAAM,EAAEC,QAAQ;YAChBC,IAAI,EAAE,WAAW;YACjBnB,KAAK,EAAE;UACT,CAAE;UACFhB,SAAS,EAAC,qEAAqE;UAAAC,QAAA,eAE/E3C,OAAA;YAAK0C,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvB3C,OAAA,CAACb,MAAM;cAACuD,SAAS,EAAC;YAAW;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChCrD,OAAA;cAAK0C,SAAS,EAAC;YAA0E;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7F;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAEbrD,OAAA,CAACnB,MAAM,CAACyE,GAAG;UACTN,OAAO,EAAE;YACPF,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;YACdiC,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAClBxB,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;UACnB,CAAE;UACFC,UAAU,EAAE;YACVC,QAAQ,EAAE,GAAG;YACbkB,MAAM,EAAEC,QAAQ;YAChBC,IAAI,EAAE,WAAW;YACjBnB,KAAK,EAAE;UACT,CAAE;UACFhB,SAAS,EAAC,oEAAoE;UAAAC,QAAA,eAE9E3C,OAAA;YAAK0C,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvB3C,OAAA,CAACX,QAAQ;cAACqD,SAAS,EAAC;YAAW;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClCrD,OAAA;cAAK0C,SAAS,EAAC;YAA0E;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7F;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAGbrD,OAAA,CAACnB,MAAM,CAACyE,GAAG;UACTN,OAAO,EAAE;YACPF,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;YACdiC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;YAClBxB,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;UACnB,CAAE;UACFC,UAAU,EAAE;YACVC,QAAQ,EAAE,CAAC;YACXkB,MAAM,EAAEC,QAAQ;YAChBC,IAAI,EAAE,WAAW;YACjBnB,KAAK,EAAE;UACT,CAAE;UACFhB,SAAS,EAAC,oEAAoE;UAAAC,QAAA,eAE9E3C,OAAA;YAAK0C,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvB3C,OAAA,CAACd,OAAO;cAACwD,SAAS,EAAC;YAAW;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjCrD,OAAA;cAAK0C,SAAS,EAAC;YAAwE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAGbrD,OAAA,CAACnB,MAAM,CAACyE,GAAG;UACTN,OAAO,EAAE;YACPO,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;YAClBR,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YAClBgC,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG;UACtB,CAAE;UACFvB,UAAU,EAAE;YACVC,QAAQ,EAAE,CAAC;YACXkB,MAAM,EAAEC,QAAQ;YAChBC,IAAI,EAAE,WAAW;YACjBnB,KAAK,EAAE;UACT,CAAE;UACFhB,SAAS,EAAC,wDAAwD;UAAAC,QAAA,eAElE3C,OAAA,CAACb,MAAM;YAACuD,SAAS,EAAC;UAAS;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eAEbrD,OAAA,CAACnB,MAAM,CAACyE,GAAG;UACTN,OAAO,EAAE;YACPO,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;YAClBR,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YAClBgC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG;UACxB,CAAE;UACFvB,UAAU,EAAE;YACVC,QAAQ,EAAE,GAAG;YACbkB,MAAM,EAAEC,QAAQ;YAChBC,IAAI,EAAE,WAAW;YACjBnB,KAAK,EAAE;UACT,CAAE;UACFhB,SAAS,EAAC,0DAA0D;UAAAC,QAAA,eAEpE3C,OAAA,CAACb,MAAM;YAACuD,SAAS,EAAC;UAAS;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eAEbrD,OAAA,CAACnB,MAAM,CAACyE,GAAG;UACTN,OAAO,EAAE;YACPO,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;YAClBR,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YAClBgC,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG;UACrB,CAAE;UACFvB,UAAU,EAAE;YACVC,QAAQ,EAAE,GAAG;YACbkB,MAAM,EAAEC,QAAQ;YAChBC,IAAI,EAAE,WAAW;YACjBnB,KAAK,EAAE;UACT,CAAE;UACFhB,SAAS,EAAC,wDAAwD;UAAAC,QAAA,eAElE3C,OAAA,CAACb,MAAM;YAACuD,SAAS,EAAC;UAAS;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eAGbrD,OAAA,CAACnB,MAAM,CAACyE,GAAG;UACTN,OAAO,EAAE;YACPF,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;YACf2B,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YACb1B,OAAO,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;UACrB,CAAE;UACFS,UAAU,EAAE;YACVC,QAAQ,EAAE,CAAC;YACXkB,MAAM,EAAEC,QAAQ;YAChBC,IAAI,EAAE,WAAW;YACjBnB,KAAK,EAAE;UACT,CAAE;UACFhB,SAAS,EAAC;QAA6D;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC,eAEdrD,OAAA,CAACnB,MAAM,CAACyE,GAAG;UACTN,OAAO,EAAE;YACPF,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;YACf2B,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;YACd1B,OAAO,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;UACrB,CAAE;UACFS,UAAU,EAAE;YACVC,QAAQ,EAAE,CAAC;YACXkB,MAAM,EAAEC,QAAQ;YAChBC,IAAI,EAAE,WAAW;YACjBnB,KAAK,EAAE;UACT,CAAE;UACFhB,SAAS,EAAC;QAAgE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC,eAEdrD,OAAA,CAACnB,MAAM,CAACyE,GAAG;UACTN,OAAO,EAAE;YACPF,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;YACd2B,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YACb1B,OAAO,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;UACrB,CAAE;UACFS,UAAU,EAAE;YACVC,QAAQ,EAAE,GAAG;YACbkB,MAAM,EAAEC,QAAQ;YAChBC,IAAI,EAAE,WAAW;YACjBnB,KAAK,EAAE;UACT,CAAE;UACFhB,SAAS,EAAC;QAA8D;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC,eAGNrD,OAAA;QAAK0C,SAAS,EAAC,iCAAiC;QAAAC,QAAA,eAC9C3C,OAAA;UACE0C,SAAS,EAAC,4BAA4B;UACtCmD,KAAK,EAAC,4BAA4B;UAClCC,OAAO,EAAC,cAAc;UACtBC,mBAAmB,EAAC,MAAM;UAAApD,QAAA,gBAG1B3C,OAAA,CAACnB,MAAM,CAACmH,IAAI;YACVC,CAAC,EAAC,uNAAuN;YACzNC,IAAI,EAAC,yBAAyB;YAC9BlD,OAAO,EAAE;cACPiD,CAAC,EAAE,CACD,uNAAuN,EACvN,8WAA8W,EAC9W,uNAAuN;YAE3N,CAAE;YACFzC,UAAU,EAAE;cACVC,QAAQ,EAAE,CAAC;cACXkB,MAAM,EAAEC,QAAQ;cAChBC,IAAI,EAAE;YACR;UAAE;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEFrD,OAAA,CAACnB,MAAM,CAACmH,IAAI;YACVC,CAAC,EAAC,yIAAyI;YAC3IC,IAAI,EAAC,yBAAyB;YAC9BlD,OAAO,EAAE;cACPiD,CAAC,EAAE,CACD,yIAAyI,EACzI,uIAAuI,EACvI,yIAAyI;YAE7I,CAAE;YACFzC,UAAU,EAAE;cACVC,QAAQ,EAAE,CAAC;cACXkB,MAAM,EAAEC,QAAQ;cAChBC,IAAI,EAAE,WAAW;cACjBnB,KAAK,EAAE;YACT;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEFrD,OAAA,CAACnB,MAAM,CAACmH,IAAI;YACVC,CAAC,EAAC,8HAA8H;YAChIC,IAAI,EAAC,yBAAyB;YAC9BlD,OAAO,EAAE;cACPiD,CAAC,EAAE,CACD,8HAA8H,EAC9H,gIAAgI,EAChI,8HAA8H;YAElI,CAAE;YACFzC,UAAU,EAAE;cACVC,QAAQ,EAAE,CAAC;cACXkB,MAAM,EAAEC,QAAQ;cAChBC,IAAI,EAAE,WAAW;cACjBnB,KAAK,EAAE;YACT;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrD,OAAA;MAASwB,GAAG,EAAErB,cAAe;MAACuC,SAAS,EAAC,cAAc;MAAAC,QAAA,eACpD3C,OAAA;QAAK0C,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxB3C,OAAA;UAAK0C,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAExB3C,OAAA,CAACnB,MAAM,CAACyE,GAAG;YACTT,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAE0B,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCzB,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAE0B,CAAC,EAAE;YAAE,CAAE;YAC9BjB,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9Bf,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAExB3C,OAAA,CAACnB,MAAM,CAACyE,GAAG;cACTT,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BU,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1ChB,SAAS,EAAC,YAAY;cAAAC,QAAA,gBAEtB3C,OAAA,CAACZ,QAAQ;gBAACsD,SAAS,EAAC;cAA4B;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnDrD,OAAA;gBAAM0C,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAAmC;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CAAC,eAEbrD,OAAA;cAAI0C,SAAS,EAAC,YAAY;cAAAC,QAAA,GAAC,6BACE,EAAC,GAAG,eAC/B3C,OAAA;gBAAM0C,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAC,WAE9B,eAAA3C,OAAA,CAAClB,0BAA0B;kBAAC4D,SAAS,EAAC;gBAAqB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAELrD,OAAA;cAAG0C,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAI7B;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAGJrD,OAAA,CAACnB,MAAM,CAACyE,GAAG;cACTT,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BU,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1ChB,SAAS,EAAC,aAAa;cAAAC,QAAA,EAEtB,CAAC3B,IAAI,gBACJhB,OAAA;gBAAK0C,SAAS,EAAC,6DAA6D;gBAAAC,QAAA,gBAE1E3C,OAAA,CAACnB,MAAM,CAACsH,MAAM;kBACZlD,OAAO,EAAE9B,gBAAiB;kBAC1BuB,SAAS,EAAC,mJAAmJ;kBAC7JoC,UAAU,EAAE;oBAAEvB,KAAK,EAAE;kBAAK,CAAE;kBAC5B6C,QAAQ,EAAE;oBAAE7C,KAAK,EAAE;kBAAK,CAAE;kBAAAZ,QAAA,gBAE1B3C,OAAA,CAACX,QAAQ;oBAACqD,SAAS,EAAC;kBAAc;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAEvC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC,eAGhBrD,OAAA,CAACtB,IAAI;kBAAC2H,EAAE,EAAC,WAAW;kBAAA1D,QAAA,eAClB3C,OAAA,CAACnB,MAAM,CAACsH,MAAM;oBACZzD,SAAS,EAAC,+IAA+I;oBACzJoC,UAAU,EAAE;sBAAEvB,KAAK,EAAE;oBAAK,CAAE;oBAC5B6C,QAAQ,EAAE;sBAAE7C,KAAK,EAAE;oBAAK,CAAE;oBAAAZ,QAAA,gBAE1B3C,OAAA,CAACV,UAAU;sBAACoD,SAAS,EAAC;oBAAc;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAEzC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAe;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eAGPrD,OAAA,CAACtB,IAAI;kBAAC2H,EAAE,EAAC,QAAQ;kBAAA1D,QAAA,eACf3C,OAAA,CAACnB,MAAM,CAACsH,MAAM;oBACZzD,SAAS,EAAC,iIAAiI;oBAC3IoC,UAAU,EAAE;sBAAEvB,KAAK,EAAE;oBAAK,CAAE;oBAC5B6C,QAAQ,EAAE;sBAAE7C,KAAK,EAAE;oBAAK,CAAE;oBAAAZ,QAAA,gBAE1B3C,OAAA,CAACT,OAAO;sBAACmD,SAAS,EAAC;oBAAc;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,aAEtC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAe;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,gBAENrD,OAAA,CAACtB,IAAI;gBAAC2H,EAAE,EAAC,YAAY;gBAAA1D,QAAA,eACnB3C,OAAA,CAACnB,MAAM,CAACsH,MAAM;kBACZzD,SAAS,EAAC,+IAA+I;kBACzJoC,UAAU,EAAE;oBAAEvB,KAAK,EAAE;kBAAK,CAAE;kBAC5B6C,QAAQ,EAAE;oBAAE7C,KAAK,EAAE;kBAAK,CAAE;kBAAAZ,QAAA,gBAE1B3C,OAAA,CAACjB,OAAO;oBAAC2D,SAAS,EAAC;kBAAc;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,mBAEtC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ;YACP;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CAAC,eAKbrD,OAAA,CAACnB,MAAM,CAACyE,GAAG;cACTT,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BU,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1ChB,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAE5B3C,OAAA;gBAAK0C,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9B3C,OAAA,CAACd,OAAO;kBAACyE,KAAK,EAAE;oBAACqB,KAAK,EAAE;kBAAS;gBAAE;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACtCrD,OAAA;kBAAA2C,QAAA,EAAM;gBAAa;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,eACNrD,OAAA;gBAAK0C,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9B3C,OAAA,CAACb,MAAM;kBAACwE,KAAK,EAAE;oBAACqB,KAAK,EAAE;kBAAS;gBAAE;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACrCrD,OAAA;kBAAA2C,QAAA,EAAM;gBAAY;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC,eACNrD,OAAA;gBAAK0C,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9B3C,OAAA,CAACf,QAAQ;kBAAC0E,KAAK,EAAE;oBAACqB,KAAK,EAAE;kBAAS;gBAAE;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACvCrD,OAAA;kBAAA2C,QAAA,EAAM;gBAAa;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGbrD,OAAA,CAACnB,MAAM,CAACyE,GAAG;YACTT,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAE0B,CAAC,EAAE;YAAG,CAAE;YAC/BzB,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAE0B,CAAC,EAAE;YAAE,CAAE;YAC9BjB,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC1ChB,SAAS,EAAC,YAAY;YAAAC,QAAA,eAEtB3C,OAAA;cAAK0C,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvB3C,OAAA;gBACE8D,GAAG,EAAEpE,MAAO;gBACZqE,GAAG,EAAC,mBAAmB;gBACvBrD,OAAO,EAAC;cAAM;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,eAGFrD,OAAA,CAACnB,MAAM,CAACyE,GAAG;gBACTN,OAAO,EAAE;kBAAEF,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE;gBAAE,CAAE;gBAC/BU,UAAU,EAAE;kBAAEC,QAAQ,EAAE,CAAC;kBAAEkB,MAAM,EAAEC;gBAAS,CAAE;gBAC9ClC,SAAS,EAAC,kBAAkB;gBAC5BiB,KAAK,EAAE;kBAAC5B,GAAG,EAAE,OAAO;kBAAEuE,IAAI,EAAE;gBAAO,CAAE;gBAAA3D,QAAA,eAErC3C,OAAA,CAAChB,MAAM;kBAAC2E,KAAK,EAAE;oBAACqB,KAAK,EAAE;kBAAS;gBAAE;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,eAEbrD,OAAA,CAACnB,MAAM,CAACyE,GAAG;gBACTN,OAAO,EAAE;kBAAEF,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE;gBAAE,CAAE;gBAC9BU,UAAU,EAAE;kBAAEC,QAAQ,EAAE,CAAC;kBAAEkB,MAAM,EAAEC;gBAAS,CAAE;gBAC9ClC,SAAS,EAAC,kBAAkB;gBAC5BiB,KAAK,EAAE;kBAAC4C,MAAM,EAAE,OAAO;kBAAEC,KAAK,EAAE;gBAAO,CAAE;gBAAA7D,QAAA,eAEzC3C,OAAA,CAACf,QAAQ;kBAAC0E,KAAK,EAAE;oBAACqB,KAAK,EAAE;kBAAS;gBAAE;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVrD,OAAA;MAAS0C,SAAS,EAAC,sEAAsE;MAAAC,QAAA,eACvF3C,OAAA;QAAK0C,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrD3C,OAAA,CAACnB,MAAM,CAACyE,GAAG;UACTT,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAG,CAAE;UAC/B2D,WAAW,EAAE;YAAE1D,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAE,CAAE;UAClCU,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BiD,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzBjE,SAAS,EAAC,gDAAgD;UAAAC,QAAA,EAEzD,CACC;YAAEiE,MAAM,EAAE,MAAM;YAAEC,IAAI,EAAE,iBAAiB;YAAEC,IAAI,EAAE5H,OAAO;YAAE8F,KAAK,EAAE;UAA4B,CAAC,EAC9F;YAAE4B,MAAM,EAAE,MAAM;YAAEC,IAAI,EAAE,iBAAiB;YAAEC,IAAI,EAAE1H,QAAQ;YAAE4F,KAAK,EAAE;UAA8B,CAAC,EACjG;YAAE4B,MAAM,EAAE,OAAO;YAAEC,IAAI,EAAE,eAAe;YAAEC,IAAI,EAAE9H,MAAM;YAAEgG,KAAK,EAAE;UAAgC,CAAC,EAChG;YAAE4B,MAAM,EAAE,KAAK;YAAEC,IAAI,EAAE,cAAc;YAAEC,IAAI,EAAE7H,QAAQ;YAAE+F,KAAK,EAAE;UAAgC,CAAC,CAChG,CAAC+B,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAChBjH,OAAA,CAACnB,MAAM,CAACyE,GAAG;YAETT,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE,EAAE;cAAES,KAAK,EAAE;YAAI,CAAE;YAC3CkD,WAAW,EAAE;cAAE1D,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE,CAAC;cAAES,KAAK,EAAE;YAAE,CAAE;YAC5CC,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAEuD,KAAK,GAAG;YAAI,CAAE;YAClDP,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzB7B,UAAU,EAAE;cAAEvB,KAAK,EAAE,IAAI;cAAET,CAAC,EAAE,CAAC;YAAE,CAAE;YACnCJ,SAAS,EAAC,gIAAgI;YAAAC,QAAA,gBAE1I3C,OAAA;cAAK0C,SAAS,EAAG,gFAA+EsE,IAAI,CAAChC,KAAM,2FAA2F;cAAArC,QAAA,eACpM3C,OAAA,CAACgH,IAAI,CAACF,IAAI;gBAACpE,SAAS,EAAC;cAAkC;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC,eACNrD,OAAA;cAAK0C,SAAS,EAAC,uEAAuE;cAAAC,QAAA,EAAEqE,IAAI,CAACJ;YAAM;cAAA1D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1GrD,OAAA;cAAK0C,SAAS,EAAC,2DAA2D;cAAAC,QAAA,EAAEqE,IAAI,CAACH;YAAI;cAAA3D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,GAZvF4D,KAAK;YAAA/D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAaA,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVrD,OAAA;MAASwB,GAAG,EAAEpB,iBAAkB;MAACsC,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC1D3C,OAAA;QAAK0C,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC3C,OAAA;UAAI0C,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAE9B;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLrD,OAAA;UAAK0C,SAAS,EAAC,cAAc;UAAAC,QAAA,EAC1B,CACC;YACEuE,MAAM,EAAE,CAAC;YACTL,IAAI,EAAE,yIAAyI;YAC/I7F,IAAI,EAAE;cAAER,IAAI,EAAE;YAAgB;UAChC,CAAC,EACD;YACE0G,MAAM,EAAE,CAAC;YACTL,IAAI,EAAE,uIAAuI;YAC7I7F,IAAI,EAAE;cAAER,IAAI,EAAE;YAAe;UAC/B,CAAC,EACD;YACE0G,MAAM,EAAE,CAAC;YACTL,IAAI,EAAE,mHAAmH;YACzH7F,IAAI,EAAE;cAAER,IAAI,EAAE;YAAe;UAC/B,CAAC,CACF,CAACuG,GAAG,CAAC,CAACI,MAAM,EAAEF,KAAK;YAAA,IAAAG,YAAA;YAAA,oBAClBpH,OAAA,CAACnB,MAAM,CAACyE,GAAG;cAETT,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/B2D,WAAW,EAAE;gBAAE1D,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAClCU,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAEuD,KAAK,GAAG;cAAI,CAAE;cAClDP,QAAQ,EAAE;gBAAEC,IAAI,EAAE;cAAK,CAAE;cACzBjE,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAEvB3C,OAAA;gBAAK0C,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC5B3C,OAAA;kBAAK2D,KAAK,EAAE;oBAAEqB,KAAK,EAAE,SAAS;oBAAEM,QAAQ,EAAE;kBAAU,CAAE;kBAAA3C,QAAA,EACnD,GAAG,CAACgC,MAAM,CAACwC,MAAM,CAACD,MAAM;gBAAC;kBAAAhE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNrD,OAAA;gBAAK0C,SAAS,EAAC,aAAa;gBAAAC,QAAA,GAAC,IAAC,EAACwE,MAAM,CAACN,IAAI,EAAC,IAAC;cAAA;gBAAA3D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAClDrD,OAAA;gBAAK0C,SAAS,EAAC;cAAgB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtCrD,OAAA;gBAAK0C,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAAyE,YAAA,GAAED,MAAM,CAACnG,IAAI,cAAAoG,YAAA,uBAAXA,YAAA,CAAa5G;cAAI;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,GAdnD4D,KAAK;cAAA/D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAeA,CAAC;UAAA,CACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVrD,OAAA;MAASwB,GAAG,EAAEnB,YAAa;MAACqC,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eAC9F3C,OAAA;QAAK0C,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD3C,OAAA;UAAK0C,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC3C,OAAA,CAACnB,MAAM,CAACwI,EAAE;YACRxE,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAG,CAAE;YAC/B2D,WAAW,EAAE;cAAE1D,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAE,CAAE;YAClCU,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9BiD,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzBjE,SAAS,EAAC,mDAAmD;YAAAC,QAAA,EAC9D;UAED;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eACZrD,OAAA,CAACnB,MAAM,CAACyI,CAAC;YACPzE,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAG,CAAE;YAC/B2D,WAAW,EAAE;cAAE1D,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAE,CAAE;YAClCU,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC1CgD,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzBjE,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EACpD;UAED;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAENrD,OAAA;UAAK0C,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBAErD3C,OAAA,CAACnB,MAAM,CAACyE,GAAG;YACTT,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAE0B,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCgC,WAAW,EAAE;cAAE1D,OAAO,EAAE,CAAC;cAAE0B,CAAC,EAAE;YAAE,CAAE;YAClCjB,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9BiD,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzBjE,SAAS,EAAC,2CAA2C;YAAAC,QAAA,gBAErD3C,OAAA;cAAK0C,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrD3C,OAAA;gBAAI0C,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAAiB;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvErD,OAAA;gBAAK0C,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAE;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC/CrD,OAAA;gBACEuH,IAAI,EAAC,6GAA6G;gBAClHnF,MAAM,EAAC,QAAQ;gBACfoF,GAAG,EAAC,qBAAqB;gBACzB9E,SAAS,EAAC,qOAAqO;gBAAAC,QAAA,gBAE/O3C,OAAA;kBAAK0C,SAAS,EAAC,SAAS;kBAACwD,IAAI,EAAC,cAAc;kBAACJ,OAAO,EAAC,WAAW;kBAAAnD,QAAA,eAC9D3C,OAAA;oBAAMiG,CAAC,EAAC;kBAAklC;oBAAA/C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzlC,CAAC,eACNrD,OAAA;kBAAM0C,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNrD,OAAA;cAAMyH,QAAQ,EAAEpF,YAAa;cAACK,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACjD3C,OAAA;gBAAA2C,QAAA,gBACE3C,OAAA;kBAAO0C,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAI;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC5ErD,OAAA;kBACE0H,IAAI,EAAC,MAAM;kBACXlH,IAAI,EAAC,MAAM;kBACXmH,WAAW,EAAC,gBAAgB;kBAC5BjF,SAAS,EAAC,6HAA6H;kBACvIP,KAAK,EAAE7B,QAAQ,CAACE,IAAK;kBACrBoH,QAAQ,EAAE3F,YAAa;kBACvB4F,QAAQ;gBAAA;kBAAA3E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrD,OAAA;gBAAA2C,QAAA,gBACE3C,OAAA;kBAAO0C,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAK;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC7ErD,OAAA;kBACE0H,IAAI,EAAC,OAAO;kBACZlH,IAAI,EAAC,OAAO;kBACZmH,WAAW,EAAC,wBAAwB;kBACpCjF,SAAS,EAAC,6HAA6H;kBACvIP,KAAK,EAAE7B,QAAQ,CAACG,KAAM;kBACtBmH,QAAQ,EAAE3F,YAAa;kBACvB4F,QAAQ;gBAAA;kBAAA3E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrD,OAAA;gBAAA2C,QAAA,gBACE3C,OAAA;kBAAO0C,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAO;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC/ErD,OAAA;kBACEQ,IAAI,EAAC,SAAS;kBACdmH,WAAW,EAAC,gCAAgC;kBAC5CG,IAAI,EAAC,GAAG;kBACRpF,SAAS,EAAC,yIAAyI;kBACnJP,KAAK,EAAE7B,QAAQ,CAACd,OAAQ;kBACxBoI,QAAQ,EAAE3F,YAAa;kBACvB4F,QAAQ;gBAAA;kBAAA3E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNrD,OAAA;gBACE0H,IAAI,EAAC,QAAQ;gBACbK,QAAQ,EAAErH,OAAQ;gBAClBgC,SAAS,EAAC,2OAA2O;gBAAAC,QAAA,EAEpPjC,OAAO,GAAG,YAAY,GAAG;cAAc;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,EACRzC,eAAe,iBACdZ,OAAA;gBAAG0C,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAClD/B;cAAe;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eAGbrD,OAAA,CAACnB,MAAM,CAACyE,GAAG;YACTT,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAE0B,CAAC,EAAE;YAAG,CAAE;YAC/BgC,WAAW,EAAE;cAAE1D,OAAO,EAAE,CAAC;cAAE0B,CAAC,EAAE;YAAE,CAAE;YAClCjB,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9BiD,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzBjE,SAAS,EAAC,WAAW;YAAAC,QAAA,eAGrB3C,OAAA;cAAK0C,SAAS,EAAC,2CAA2C;cAAAC,QAAA,gBACxD3C,OAAA;gBAAI0C,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAC;cAAsB;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjFrD,OAAA;gBAAK0C,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB3C,OAAA;kBAAK0C,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C3C,OAAA;oBAAK0C,SAAS,EAAC,oEAAoE;oBAAAC,QAAA,eACjF3C,OAAA;sBAAK0C,SAAS,EAAC,wBAAwB;sBAACwD,IAAI,EAAC,cAAc;sBAACJ,OAAO,EAAC,WAAW;sBAAAnD,QAAA,eAC7E3C,OAAA;wBAAMiG,CAAC,EAAC;sBAAklC;wBAAA/C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzlC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNrD,OAAA;oBAAA2C,QAAA,gBACE3C,OAAA;sBAAG0C,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,EAAC;oBAAgB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eAC7DrD,OAAA;sBAAG0C,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAe;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNrD,OAAA;kBAAK0C,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C3C,OAAA;oBAAK0C,SAAS,EAAC,mEAAmE;oBAAAC,QAAA,eAChF3C,OAAA;sBAAK0C,SAAS,EAAC,uBAAuB;sBAACwD,IAAI,EAAC,MAAM;sBAAC8B,MAAM,EAAC,cAAc;sBAAClC,OAAO,EAAC,WAAW;sBAAAnD,QAAA,eAC1F3C,OAAA;wBAAMiI,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAC,GAAG;wBAAClC,CAAC,EAAC;sBAAsG;wBAAA/C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1K;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNrD,OAAA;oBAAA2C,QAAA,gBACE3C,OAAA;sBAAG0C,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,EAAC;oBAAa;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eAC1DrD,OAAA;sBAAG0C,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAuB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNrD,OAAA;kBAAK0C,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C3C,OAAA;oBAAK0C,SAAS,EAAC,qEAAqE;oBAAAC,QAAA,eAClF3C,OAAA;sBAAK0C,SAAS,EAAC,yBAAyB;sBAACwD,IAAI,EAAC,MAAM;sBAAC8B,MAAM,EAAC,cAAc;sBAAClC,OAAO,EAAC,WAAW;sBAAAnD,QAAA,eAC5F3C,OAAA;wBAAMiI,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAC,GAAG;wBAAClC,CAAC,EAAC;sBAA6C;wBAAA/C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNrD,OAAA;oBAAA2C,QAAA,gBACE3C,OAAA;sBAAG0C,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,EAAC;oBAAa;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eAC1DrD,OAAA;sBAAG0C,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAsB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNrD,OAAA;kBAAK0C,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C3C,OAAA;oBAAK0C,SAAS,EAAC,qEAAqE;oBAAAC,QAAA,eAClF3C,OAAA;sBAAK0C,SAAS,EAAC,yBAAyB;sBAACwD,IAAI,EAAC,MAAM;sBAAC8B,MAAM,EAAC,cAAc;sBAAClC,OAAO,EAAC,WAAW;sBAAAnD,QAAA,gBAC5F3C,OAAA;wBAAMiI,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAC,GAAG;wBAAClC,CAAC,EAAC;sBAAoF;wBAAA/C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC,eAC3JrD,OAAA;wBAAMiI,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAC,GAAG;wBAAClC,CAAC,EAAC;sBAAkC;wBAAA/C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNrD,OAAA;oBAAA2C,QAAA,gBACE3C,OAAA;sBAAG0C,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACrDrD,OAAA;sBAAG0C,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAuB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVrD,OAAA;MAAQ0C,SAAS,EAAC,QAAQ;MAAAC,QAAA,eACxB3C,OAAA;QAAK0C,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC7B3C,OAAA;UAAG0C,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAE3B;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGTrD,OAAA,CAACF,eAAe;MAEdsI,MAAM,EAAEtH,mBAAoB;MAC5BuH,OAAO,EAAEA,CAAA,KAAMtH,sBAAsB,CAAC,KAAK,CAAE;MAC7C0G,QAAQ,EAAErG;IAAuB,GAH5BN,mBAAmB,GAAG,MAAM,GAAG,QAAQ;MAAAoC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAI7C,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACnD,EAAA,CA1iCID,IAAI;EAAA,QAQSR,WAAW,EACXb,WAAW;AAAA;AAAA0J,EAAA,GATxBrI,IAAI;AA4iCV,eAAeA,IAAI;AAAC,IAAAqI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}